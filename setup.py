from setuptools import setup, find_packages

setup(
    name="insights_project",
    version="0.1.0",
    packages=find_packages(where="src/backend"),
    package_dir={"": "src/backend"},
    install_requires=[
        # List your project's dependencies here
        "fastapi>=0.68.0",
        "uvicorn>=0.15.0",
        "langchain>=0.0.200",
        "langchain-aws>=0.0.1",
        "sqlalchemy>=1.4.0",
        "python-dotenv>=0.19.0",
        "pydantic>=1.8.0",
    ],
    python_requires=">=3.8",
)
