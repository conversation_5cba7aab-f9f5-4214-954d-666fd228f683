FROM node:18-alpine as build

WORKDIR /app

# Install dependencies
COPY src/frontend/package.json src/frontend/package-lock.json* ./
RUN npm install

# Copy source code
COPY src/frontend ./

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 3000

CMD ["nginx", "-g", "daemon off;"]