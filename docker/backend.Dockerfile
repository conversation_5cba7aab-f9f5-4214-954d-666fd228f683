FROM python:3.9-slim

WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    # default-libmysqlclient-dev \
    # pkg-config \
    # libpq-dev \
    # git \
    && rm -rf /var/lib/apt/lists/*

# First create the directory structure
RUN mkdir -p src/backend

# Copy requirements files for better caching
COPY pyproject.toml setup.py ./

# Install dependencies
RUN \
    # pip install --no-cache-dir --upgrade pip && \
    # pip install --no-cache-dir sqlalchemy==1.4.46 PyHive==0.6.5 && \
    # pip install --no-cache-dir --index-url https://pypi.grofer.io/simple/ pencilbox==1.7.38 && \
    pip install --no-cache-dir fastapi uvicorn websockets pydantic pydantic-settings python-dotenv \
    langchain langchain-aws boto3 trino slack_sdk==3.34.0 hvac

# Now copy the rest of the application
COPY . .

# Set the working directory to the backend app
WORKDIR /app/src/backend

# Expose the port the app will run on
EXPOSE 8000

# Command to run the application
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]