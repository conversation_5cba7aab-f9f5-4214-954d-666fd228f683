server {
    listen 3000;
    
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
    
            location /api/ {
                proxy_pass http://backend:8000/api/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                # Increase timeouts for complex queries with multiple steps
                proxy_read_timeout 300s;      # Increase from default 60s to 300s (5 minutes)
                proxy_connect_timeout 300s;   # Increase connection timeout
                proxy_send_timeout 300s;      # Increase send timeout
            }
    location /ws/ {
        proxy_pass http://backend:8000/ws/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}