# Vector Database POC with Elasticsearch

This is a proof-of-concept implementation of vector search using Elasticsearch.

## Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose
- Python 3.8+

## Setup

1. Start the Elasticsearch and Kibana containers:
   ```bash
   docker-compose up -d
   ```

2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Run the test script:
   ```bash
   python test_vector_search.py
   ```

## Accessing the Services

- **Elasticsearch**: http://localhost:9200
- **Kibana**: http://localhost:5601

## How It Works

The POC demonstrates:
1. Creating an Elasticsearch index with a vector field
2. Indexing sample documents with vector embeddings
3. Performing vector similarity search

## Next Steps

- Replace the dummy embedding function with a real model (e.g., Sentence-BERT, OpenAI embeddings)
- Adjust the vector dimensions according to your embedding model
- Add more sophisticated search features like filtering and boosting

## Clean Up

To stop and remove the containers:
```bash
docker-compose down -v
```
