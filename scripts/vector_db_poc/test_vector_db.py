import boto3

client = boto3.client('bedrock-agent-runtime')

# response = client.retrieve_and_generate(
#     input={
#         "text": "What is Blinkit?"
#     },
#     retrieveAndGenerateConfiguration={
#         "knowledgeBaseConfiguration": {
#             "knowledgeBaseId": "your-knowledgebase-id"
#         },
#         "type": "KNOWLEDGE_BASE"
#     }
# )


# # To see documents retrieved:
# print(response['retrievalResults'])  # Contains document chunks and metadata


information_schema_kb_id = "ERUFXEXDHU"

response = client.retrieve(
    knowledgeBaseId=information_schema_kb_id,
    retrievalQuery={'text': 'Pet care'},
    retrievalConfiguration={
        "vectorSearchConfiguration": {
            "numberOfResults": 1
        }
    }
)

for doc in response['retrievalResults']:
    print(f"Score: {doc['score']}")
    print(f"Content: {doc['content']}")
    print(f"Metadata: {doc.get('metadata')}")
    print("-" * 50)