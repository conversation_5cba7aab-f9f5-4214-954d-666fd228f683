#!/usr/bin/env python3
import os
import re
import argparse
from pathlib import Path
from typing import List, Dict, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from pprint import pprint

from elasticsearch import Elasticsearch, helpers
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Google's Generative AI
genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
EMBEDDING_MODEL = "models/embedding-001"
EMBEDDING_DIMENSIONS = 768

@dataclass
class ColumnInfo:
    """Represents a column in a database table schema."""
    name: str
    data_type: str
    description: str = ""
    is_primary_key: bool = False
    is_foreign_key: bool = False
    is_partition: bool = False
    constraints: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "data_type": self.data_type,
            "description": self.description,
            "is_primary_key": self.is_primary_key,
            "is_foreign_key": self.is_foreign_key,
            "is_partition": self.is_partition,
            "constraints": self.constraints
        }

@dataclass
class TableSchema:
    """Represents a database table schema."""
    name: str
    schema_name: str
    database: str
    description: str = ""
    columns: List[ColumnInfo] = field(default_factory=list)
    is_partitioned: bool = False
    partition_info: Optional[Dict] = None
    partition_key: Optional[str] = None
    source_file: Optional[Path] = None
    join_keys: List[Dict[str, Any]] = field(default_factory=list)

    @property
    def full_name(self) -> str:
        return f"{self.database}.{self.schema_name}.{self.name}"

    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "schema": self.schema_name,
            "database": self.database,
            "description": self.description,
            "is_partitioned": self.is_partitioned,
            "partition_info": self.partition_info or {},
            "columns": [col.to_dict() for col in self.columns]
        }

class SchemaParser:
    """Parses schema files into structured TableSchema objects."""
    @staticmethod
    def parse_schema_file(file_path: Path) -> Optional[TableSchema]:
        """Parse a schema file into a TableSchema object."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Default values
            db_name = "unknown"
            schema_name = "unknown"
            table_name = file_path.stem# Try to extract from file path if in documents/ or test_docs/
            parts = list(file_path.parts)
            for dir_name in ['documents', 'test_docs']:
                if dir_name in parts:
                    dir_idx = parts.index(dir_name)
                    if dir_idx + 1 < len(parts):
                        db_name = parts[dir_idx + 1]
                    if dir_idx + 2 < len(parts):
                        schema_name = parts[dir_idx + 2]
                    break
            
            # Extract table info from file content (overrides path-based values)
            table_match = re.search(r'# Table:\s*(\w+)\.(\w+)\.(\w+)', content, re.IGNORECASE)
            if table_match:
                db_name, schema_name, table_name = table_match.groups()
            
            # Clean up table name
            table_name = table_name.replace(f"{db_name}_{schema_name}_", "").replace("blinkit_dwh_", "")
            
            # Initialize table
            table = TableSchema(
                name=table_name,
                schema_name=schema_name,
                database=db_name,
                source_file=file_path
            )
            
            # Extract table description
            desc_match = re.search(r'## Description\n([^#]+)', content, re.DOTALL)
            if desc_match:
                table.description = desc_match.group(1).strip()
            
            # Extract partition info with key detection
            part_match = re.search(r'## Partitioning\n([^#]+)', content, re.DOTALL)
            if part_match:
                partition_text = part_match.group(1).strip()
                table.is_partitioned = "not partitioned" not in partition_text.lower()
                # Try to extract partition key
                key_match = re.search(r'partitioned by\s+[`\'"]?(\w+)[`\'"]?', partition_text, re.IGNORECASE)
                if not key_match:
                    key_match = re.search(r'partition\s+key\s+[is:|=]?\s+[`\'"]?(\w+)[`\'"]?', partition_text, re.IGNORECASE)
                if not key_match:
                    # Look for mentions of column names with partition or key nearby
                    key_match = re.search(r'[`\'"]?(\w+)[`\'"]?.*\b(?:partition|partitioned)\b', partition_text, re.IGNORECASE)
                table.partition_key = key_match.group(1) if key_match else None
                table.partition_info = {
                    "info": partition_text,
                    "partition_key": table.partition_key
                }
            
            # Extract columns
            cols_section = re.search(r'## Columns\n([\s\S]*?)(?=##|$)', content)
            if cols_section:
                table.columns = SchemaParser._parse_columns(cols_section.group(1))
            
            # Identify join keys from columns
            table.join_keys = SchemaParser._identify_join_keys(table.columns)
            return table
        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
            return None

    @staticmethod
    def _identify_join_keys(columns: List[ColumnInfo]) -> List[Dict[str, Any]]:
        """Identify potential join keys from columns.
        
        This looks for primary keys, foreign keys, and columns that have'id' or 'key'
        in their name, which are likely candidates for join operations.
        """
        join_keys = []
        for col in columns:
            # Initialize join key metadata
            join_key = {
                "column": col.name,
                "type": [],
                "importance": 0
            }
            
            # Assign importance based on key type and naming patterns
            if col.is_primary_key:
                join_key["type"].append("primary_key")
                join_key["importance"] += 10
            if col.is_foreign_key:
                join_key["type"].append("foreign_key")
                join_key["importance"] += 8# Extract referenced table if available
                for constraint in col.constraints:
                    if constraint.startswith("REFERENCES "):
                        join_key["referenced_table"] = constraint.replace("REFERENCES ", "").strip()
                        break
            
            # Check name patterns for potential join keys
            name_lower = col.name.lower()
            if name_lower == "id" or name_lower.endswith("_id"):
                join_key["type"].append("id_column")
                join_key["importance"] += 5
            elif "key" in name_lower:
                join_key["type"].append("key_column")
                join_key["importance"] += 3# Only include if it has some importance as a join key
            if join_key["importance"] > 0:
                join_keys.append(join_key)# Sort by importance descending
        return sorted(join_keys, key=lambda x: x["importance"], reverse=True)

    @staticmethod
    def _parse_columns(content: str) -> List[ColumnInfo]:
        """Parse columns section intoColumnInfo objects."""
        columns = []
        current_col = None
        
        # Split into lines and process
        lines = content.split('\n')
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            i += 1
            
            if not line:
                continue
                
            # New column definition
            if line.startswith('-'):
                # Save previous column if exists
                if current_col is not None:
                    columns.append(current_col)
                
                # Parse column name (handle both "- column_name" and "- column_name (description)")
                col_name = line[1:].split('(')[0].split(':', 1)[0].strip()
                current_col = ColumnInfo(name=col_name, data_type="string")
                # Check for primary/foreign key indicators
                current_col.is_primary_key = any(x in line.lower() for x in ['primary key', '(pk)'])
                current_col.is_foreign_key = any(x in line.lower() for x in ['foreign key', '(fk','references'])# Extract referenced table if available
                if current_col.is_foreign_key:
                    fk_match = re.search(r'(?:references|FK to)\s+([^\s,)]+)', line, re.IGNORECASE)
                    if fk_match:
                        current_col.constraints.append(f"REFERENCES {fk_match.group(1)}")
            
            # Process column attributes (multiline aware)
            elif current_col is not None and ':' in line:
                key, value = [s.strip() for s in line.split(':', 1)]
                key_lower = key.lower()
                
                if 'data type' in key_lower:
                    current_col.data_type = value.strip()
                elif 'description' in key_lower:
                    # Handle multi-line descriptions
                    desc_lines = [value]
                    while i < len(lines) and lines[i].startswith('    '):
                        desc_lines.append(lines[i].strip())
                        i += 1
                    current_col.description = ' '.join(desc_lines).strip()
                elif 'not null' in key_lower or 'not null' in value.lower():
                    current_col.constraints.append("NOT NULL")
                elif 'primary key' in key_lower or 'primary key' in value.lower():
                    current_col.is_primary_key = True
                elif 'foreign key' in key_lower or 'foreign key' in value.lower():
                    current_col.is_foreign_key = True
                    fk_match = re.search(r'(?:references|FK to)\s+([^\s,)]+)', value, re.IGNORECASE)
                    if fk_match:
                        current_col.constraints.append(f"REFERENCES {fk_match.group(1)}")
                elif 'partition' in key_lower or 'partition' in value.lower():
                    current_col.is_partition = True
        
        # Add the last column
        if current_col is not None:
            columns.append(current_col)
            
        return columns

class SchemaVectorStore:
    """Handles vector storage and retrieval of schema information."""
    
    def __init__(self, es_client: Elasticsearch, index_name: str = "schema_search"):
        self.es = es_client
        self.index_name = index_name
        self.dimensions = EMBEDDING_DIMENSIONS
    
    def create_index(self):
        """Create index with optimized mapping for schema search."""
        mapping = {
            "mappings": {
                "properties": {
                    "content": {"type": "text", "analyzer": "english"},
                    "embedding": {
                        "type": "dense_vector",
                        "dims": self.dimensions,
                        "index": True,
                        "similarity": "cosine"
                    },
                    "metadata": {
                        "type": "nested",
                        "properties": {
                            "entity_type": {"type": "keyword"},
                            "database": {"type": "keyword"},
                            "schema": {"type": "keyword"},
                            "table": {"type": "keyword"},
                            "column": {"type": "keyword"},
                            "data_type": {"type": "keyword"},
                            "is_primary_key": {"type": "boolean"},
                            "is_foreign_key": {"type": "boolean"},
                            "is_partition": {"type": "boolean"},
                            "description": {"type": "text", "analyzer": "english"},
                            "column_description": {"type": "text", "analyzer": "english"},
                            "constraints": {"type": "keyword"}
                        }
                    }
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }
        
        # Delete existing index if it exists
        try:
            # First try to delete the index if it exists
            try:
                self.es.options(ignore_status=[400, 404]).indices.delete(index=self.index_name)
                print(f"Deleted existing index: {self.index_name}")
            except Exception as e:
                print(f"No existing index to delete or error deleting: {e}")
            
            # Create new index with the mapping
            self.es.indices.create(
                index=self.index_name,
                mappings=mapping["mappings"],
                settings=mapping["settings"]
            )
            print(f"Successfully created index: {self.index_name}")
            return True
        except Exception as e:
            print(f"Error creating index: {e}")
            return False
            
    def index_table(self, table: TableSchema):
        """Index a table's schema information."""
        # Prepare key columns for rich table representation
        key_columns = []
        for col in table.columns:
            if col.is_primary_key or col.is_foreign_key or col.is_partition:
                col_type = []
                if col.is_primary_key: col_type.append("primary key")
                if col.is_foreign_key: col_type.append("foreign key")
                if col.is_partition: col_type.append("partition key")
                key_columns.append(f"{col.name} ({', '.join(col_type)})")

        # Create richer table content
        important_columns = [c.name for c in table.columns[:min(10, len(table.columns))]]
        partition_info = f"Partitioned by {table.partition_key}" if table.partition_key else "Not partitioned"
        if table.is_partitioned and not table.partition_key and table.partition_info:
            partition_info = table.partition_info.get("info", "Partitioned (key unknown)")

        # Index table-level information with enhanced content
        table_doc = {
            "content": f"""
Table: {table.full_name}
Description: {table.description}
Key columns: {', '.join(key_columns) if key_columns else 'None'}
Partition: {partition_info}
Important columns: {', '.join(important_columns)}""".strip(),
            "metadata": {
                "entity_type": "table",
                "database": table.database,
                "schema": table.schema_name,
                "table": table.name,
                "description": table.description,
                "is_partitioned": table.is_partitioned,
                "partition_key": table.partition_key,
                "join_keys": [jk["column"] for jk in table.join_keys]
            }
        }
        
        # Generate embedding for table
        table_embedding = self._get_embedding(table_doc["content"])
        table_doc["embedding"] = table_embedding
        self.es.index(index=self.index_name, document=table_doc)
        
        # Index columns with enhanced metadata
        for column in table.columns:
            # Calculate column importance
            column_metadata = self._enrich_column_metadata(column, table)
            column_doc = {
                "content": (
                    f"Column {table.full_name}.{column.name} "
                    f"({column.data_type}): {column.description}"
                ),
                "metadata": {
                    "entity_type": "column",
                    "database": table.database,
                    "schema": table.schema_name,
                    "table": table.name,
                    "column": column.name,
                    "column_description": column.description,
                    "data_type": column.data_type,
                    "is_primary_key": column.is_primary_key,
                    "is_foreign_key": column.is_foreign_key,
                    "is_partition": column.is_partition,
                    "constraints": column.constraints,
                    "importance_score": column_metadata["importance_score"],
                    "join_relevance": column_metadata["join_relevance"],
                    "query_usage": column_metadata["query_usage"]
                }
            }
            # Generate embedding for column
            column_embedding = self._get_embedding(column_doc["content"])
            column_doc["embedding"] = column_embedding
            self.es.index(index=self.index_name, document=column_doc)

            # Index column+table joint representation for better semantic matching
            column_table_doc = {
                "content": f"""
Table {table.full_name} has column {column.name} ({column.data_type})
which contains {column.description}.
{table.description}""".strip(),
                "metadata": {
                    "entity_type": "column_table",
                    "database": table.database,
                    "schema": table.schema_name,
                    "table": table.name,
                    "column": column.name,
                    "data_type": column.data_type,
                    "is_primary_key": column.is_primary_key,
                    "is_foreign_key": column.is_foreign_key,
                    "is_partition": column.is_partition
                }
            }
            column_table_embedding = self._get_embedding(column_table_doc["content"])
            column_table_doc["embedding"] = column_table_embedding
            self.es.index(index=self.index_name, document=column_table_doc)
            
    def search_schema(
        self,
        query: str,
        top_k: int = 5,
        filters: Optional[Dict] = None,
        min_score: float = 0.7
    ) -> List[Dict]:
        """Search schema with semantic similarity and filters."""
        # Generate query embedding
        query_embedding = self._get_embedding(query)
        
        # Build the search query
        search_body = {
            "query": {
                "script_score": {
                    "query": {"bool": {"must": []}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                        "params": {"query_vector": query_embedding}
                    }
                }
            },
            "size": top_k,
            "min_score": min_score,
            "_source": {
                "includes": ["content", "metadata", "embedding"]
            },
            "highlight": {
                "fields": {
                    "content": {}
                }
            }
        }
        
        # Add filters if provided
        if filters:
            bool_query = search_body["query"]["script_score"]["query"]["bool"]
            for field, value in filters.items():
                if value is not None:
                    bool_query["must"].append({"term": {f"metadata.{field}": value}})
        
        # Execute search
        response = self.es.search(index=self.index_name, body=search_body)
        return self._format_search_results(response)
    
    def _get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using Gemini."""
        try:
            result = genai.embed_content(
                model=EMBEDDING_MODEL,
                content=text,
                task_type="retrieval_document"
            )
            return result["embedding"]
        except Exception as e:
            print(f"Error generating embedding: {e}")
            return [0.0] * self.dimensions
    
    def _format_search_results(self, response: Dict) -> List[Dict]:
        """Format search results into a more readable format."""
        results = []
        for hit in response["hits"]["hits"]:
            source = hit["_source"]
            metadata = source.get("metadata", {})
            highlights = hit.get("highlight", {})
            
            # Get highlighted content if available, otherwise use original content
            content = " ".join(highlights.get("content", [source.get("content", "")]))
            
            result = {
                "score": hit.get("_score", 0.0),
                "content": content,
                "type": metadata.get("entity_type"),
                "database": metadata.get("database"),
                "schema": metadata.get("schema"),
                "table": metadata.get("table"),
                "table_description": metadata.get("description", ""),
                "column": metadata.get("column"),
                "column_description": metadata.get("column_description", ""),
                "data_type": metadata.get("data_type"),
                "is_primary_key": metadata.get("is_primary_key", False),
                "is_foreign_key": metadata.get("is_foreign_key", False),
                "is_partition": metadata.get("is_partition", False),
                "constraints": metadata.get("constraints", [])
            }
            results.append(result)
        
        # Sort by score in descending order
        return sorted(results, key=lambda x: x["score"], reverse=True)
        
    def _enrich_column_metadata(self, column: ColumnInfo, table: TableSchema) -> Dict[str, Any]:
        """Calculate column importance and other metadata for better ranking and filtering."""
        importance_score = 0
        join_relevance = 0
        query_usage = []
        
        # Base importance from key types
        if column.is_primary_key:
            importance_score += 10
            query_usage.append("filtering")
            query_usage.append("joining")
            join_relevance += 10
        
        if column.is_foreign_key:
            importance_score += 8
            query_usage.append("joining")
            join_relevance += 8
        
        if column.is_partition:
            importance_score += 9
            query_usage.append("filtering")
            query_usage.append("partitioning")
        
        # Check name patterns for common column types
        name_lower = column.name.lower()
        
        # ID columns are often used for joins
        if name_lower == "id" or name_lower.endswith("_id") or name_lower.endswith("_key"):
            importance_score += 5
            join_relevance += 5
            if "joining" not in query_usage:
                query_usage.append("joining")
        
        # Date and timestamp columns are important for filtering
        if any(pattern in name_lower for pattern in ["date", "time", "timestamp", "created", "updated"]):
            importance_score += 4
            if "filtering" not in query_usage:
                query_usage.append("filtering")
            if "date" in name_lower or "time" in name_lower:
                query_usage.append("time_series")
        
        # Name or description columns are often used for display
        if any(pattern in name_lower for pattern in ["name", "title", "description", "label"]):
            importance_score += 3
            if "display" not in query_usage:
                query_usage.append("display")
        
        # Status, type, category columns are used for grouping and filtering
        if any(pattern in name_lower for pattern in ["status", "type", "category", "state", "flag"]):
            importance_score += 3
            if "grouping" not in query_usage:
                query_usage.append("grouping")
            if "filtering" not in query_usage:
                query_usage.append("filtering")
        
        # Amount, price, count columns are used for aggregation
        if any(pattern in name_lower for pattern in ["amount", "price", "cost", "sum", "count", "total", "quantity"]):
            importance_score += 4
            if "aggregation" not in query_usage:
                query_usage.append("aggregation")
        
        return {
            "importance_score": importance_score,
            "join_relevance": join_relevance,
            "query_usage": query_usage
        }
        
    def search_for_sql_generation(
        self,
        query: str,
        top_k: int = 3,
        filters: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Specialized search method optimized for SQL generation.
        
        This method performs a two-phase search:
        1. First, find the most relevant tables for the query
        2. Then, fetch complete schema information for those tables
        
        Returns a structure optimized for passing to an LLM to generate SQL.
        """
        # Phase 1: Find relevant tables with a lower threshold to capture more possibilities
        table_filters = filters.copy() if filters else {}
        table_filters["entity_type"] = "table"
        table_results = self.search_schema(
            query=query,
            top_k=top_k,
            filters=table_filters,
            min_score=0.6  # Lower threshold for tables
        )
        
        # Get unique table identifiers
        table_identifiers = set()
        for result in table_results:
            if all(k in result for k in ["database", "schema", "table"]):
                table_identifiers.add((
                    result["database"],
                    result["schema"],
                    result["table"]
                ))
        
        # If no tables found, try searching columns and their parent tables
        if not table_identifiers:
            column_filters = filters.copy() if filters else {}
            column_filters["entity_type"] = "column"
            
            column_results = self.search_schema(
                query=query,
                top_k=top_k * 2,  # Get more columns since we'll aggregate by table
                filters=column_filters,
                min_score=0.65
            )
            
            for result in column_results:
                if all(k in result for k in ["database", "schema", "table"]):
                    table_identifiers.add((
                        result["database"],
                        result["schema"],
                        result["table"]
                    ))
        
        # Phase 2: Fetch complete table information for each identified table
        tables_info = []
        for db, schema, table in table_identifiers:
            table_info = self._fetch_complete_table_info(db, schema, table)
            if table_info:
                tables_info.append(table_info)
        
        # Sort tables by relevance score
        tables_info.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
        
        return {
            "query": query,
            "tables": tables_info[:top_k],  # Limit to top-k most relevant tables
            "suggested_tables": [t["full_name"] for t in tables_info[:top_k]]
        }
    
    def _fetch_complete_table_info(
        self,
        database: str,
        schema: str,
        table: str
    ) -> Optional[Dict[str, Any]]:
        """Fetch complete information for a table including all its columns.Retrieves the table document and all column documents for a given table
        and combines them into a comprehensive structure.
        """
        # Get table document
        table_query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"metadata.entity_type": "table"}},
                        {"term": {"metadata.database": database}},
                        {"term": {"metadata.schema": schema}},
                        {"term": {"metadata.table": table}}
                    ]
                }
            }
        }
        
        table_response = self.es.search(index=self.index_name, body=table_query, size=1)
        if table_response["hits"]["total"]["value"] == 0:
            return None
        
        table_doc = table_response["hits"]["hits"][0]["_source"]
        table_metadata = table_doc.get("metadata", {})
        
        # Get all columns for this table
        column_query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"metadata.entity_type": "column"}},
                        {"term": {"metadata.database": database}},
                        {"term": {"metadata.schema": schema}},
                        {"term": {"metadata.table": table}}
                    ]
                }
            },
            "size": 100,  # Get up to 100 columns
            "sort": [{"metadata.importance_score": {"order": "desc"}}]
        }
        
        column_response = self.es.search(index=self.index_name, body=column_query)
        columns = []
        
        for hit in column_response["hits"]["hits"]:
            col_doc = hit["_source"]
            col_metadata = col_doc.get("metadata", {})
            
            column = {
                "name": col_metadata.get("column", ""),
                "data_type": col_metadata.get("data_type", ""),
                "description": col_metadata.get("column_description", ""),
                "is_primary_key": col_metadata.get("is_primary_key", False),
                "is_foreign_key": col_metadata.get("is_foreign_key", False),
                "is_partition": col_metadata.get("is_partition", False),
                "constraints": col_metadata.get("constraints", []),
                "importance_score": col_metadata.get("importance_score", 0),
                "join_relevance": col_metadata.get("join_relevance", 0),
                                "query_usage": col_metadata.get("query_usage", [])
            }
            columns.append(column)
        
        # Sort columns by importance
        columns.sort(key=lambda x: x["importance_score"], reverse=True)
        # Construct the full table info
        full_name = f"{database}.{schema}.{table}"
        # Identify partition key
        partition_key = table_metadata.get("partition_key")
        partition_columns = [c for c in columns if c["is_partition"]]
        if not partition_key and partition_columns:
            partition_key = partition_columns[0]["name"]
            
        # Identify primary key columns
        primary_keys = [c["name"] for c in columns if c["is_primary_key"]]
        
        # Identify important join columns
        join_columns = []
        for col in columns:
            if col["is_primary_key"] or col["is_foreign_key"] or col["join_relevance"] > 3:
                join_columns.append({
                    "name": col["name"],
                    "is_primary_key": col["is_primary_key"],
                    "is_foreign_key": col["is_foreign_key"],
                    "join_relevance": col["join_relevance"]
                })
        
        return {
            "full_name": full_name,
            "database": database,
            "schema": schema,
            "table": table,
            "description": table_metadata.get("description", ""),
            "is_partitioned": table_metadata.get("is_partitioned", False),
            "partition_key": partition_key,
            "primary_keys": primary_keys,
            "join_columns": join_columns[:5],  # Top 5 join columns
            "columns": columns,
            "column_count": len(columns),
            "relevance_score": 0  # Will be populated by search_for_sql_generation
        }

def main():
    # Set up argument parser with SQL generation support
    parser = argparse.ArgumentParser(description="Schema Search with Vector Embeddings")
    parser.add_argument(
        "--reindex",
        action="store_true",
        help="Reindex all schema documents"
    )
    parser.add_argument(
        "--query",
        type=str,
        help="Search query"
    )
    parser.add_argument(
        "--database",
        type=str,
        help="Filter by database name"
    )
    parser.add_argument(
        "--schema",
        type=str,
        help="Filter by schema name"
    )
    parser.add_argument(
        "--table",
        type=str,
        help="Filter by table name"
    )
    parser.add_argument(
        "--column",
        type=str,
        help="Filter by column name"
    )
    parser.add_argument(
        "--docs-dir",
        type=str,
        # default="documents",
        default="test_docs",
        help="Directory containing schema documents"
    )
    parser.add_argument(
        "--top-k",
        type=int,
        default=5,
        help="Number of results to return"
    )
    parser.add_argument(
        "--sql-query",
        type=str,
        help="Natural language query to convert to SQL - returns complete table schemas"
    )
    parser.add_argument(
        "--json",
        action="store_true",
        help="Output results in JSON format (useful for LLM consumption)"
    )
    args = parser.parse_args()
    
    # Initialize Elasticsearch client with better timeouts and retries
    es = Elasticsearch(
        ["http://localhost:9200"],
        request_timeout=60,  # Increased to 60 seconds
        retry_on_timeout=True,
        # max_retries=5,  # Increased number of retries
        sniff_on_start=True,
        sniff_on_node_failure=True,
        sniff_timeout=30,  # Increased sniff timeout
        # Add connection timeout
        # timeout=30,
        # Add retry on status codes
        retry_on_status=(429, 502, 503, 504),
        # Add basic connection pooling
        max_retries=5,
        # Add keepalive
        # maxsize=25,
        # Add connection pool settings
        connections_per_node=10,
    )
    
    # Test the connection
    # try:
    #     if not es.ping():
    #         raise ConnectionError("Could not connect to Elasticsearch")
    #     print("Successfully connected to Elasticsearch")
    # except Exception as e:
    #     print(f"Error connecting to Elasticsearch: {e}")
    #     print("Please make sure Elasticsearch is running and accessible at http://localhost:9200")
    #     print("You can start it with: docker-compose up -d")
    #     return
        
    vs = SchemaVectorStore(es)
    
    # Create or reindex if needed
    if args.reindex or not es.indices.exists(index=vs.index_name):
        print("Creating index and indexing schema documents...")
        vs.create_index()
        
        # Find and parse all schema files
        schema_files = list(Path(args.docs_dir).rglob("*.txt"))
        print(f"Found {len(schema_files)} schema files in {args.docs_dir}")
        
        if not schema_files:
            print(f"No schema files found in {args.docs_dir}")
            return
        
        parser = SchemaParser()
        for file_path in schema_files:
            table = parser.parse_schema_file(file_path)
            if table:
                print(f"Indexing {table.full_name}")
                vs.index_table(table)
    
    # Prepare filters
    filters = {}
    if args.database:
        filters["database"] = args.database.lower()
    if args.schema:
        filters["schema"] = args.schema.lower()
    if args.table:
        filters["table"] = args.table.lower()
    if args.column:
        filters["column"] = args.column.lower()
    
    if args.query:
        # Single query mode
        print(f"\nSearching for: {args.query}")
        if filters:
            print(f"Filters: {filters}")
            
        results = vs.search_schema(args.query, top_k=args.top_k, filters=filters)
        
        if not results:
            print("No results found.")
        else:
            print(f"\nTop {len(results)} results:")
            for i, result in enumerate(results, 1):
                print(f"\n{i}. Score: {result['score']:.4f}")
                
                # Print table/column header
                entity_type = result.get('type', 'unknown').capitalize()
                full_name = f"{result.get('database', '')}.{result.get('schema', '')}.{result.get('table', '')}"
                
                if result.get('column'):
                    full_name += f".{result['column']}"
                    print(f"   {entity_type}: {full_name}")
                    print(f"   Data Type: {result.get('data_type', 'N/A')}")
                    if result.get('column_description'):
                        print(f"   Description: {result['column_description']}")
                else:
                    print(f"   {entity_type}: {full_name}")
                    if result.get('table_description'):
                        print(f"   Description: {result['table_description']}")
                
                # Print constraints if any
                constraints = result.get('constraints', [])
                if constraints:
                    print("   Constraints:", ", ".join(constraints))
                
                # Print highlighted content
                if result.get('content'):
                    print("\n   " + result['content'].replace("\n", "\n   "))
    
    # SQL generation mode
    elif args.sql_query:
        print(f"\nSearching for tables relevant to: {args.sql_query}")
        # Use the specialized search method for SQL generation
        sql_search_results = vs.search_for_sql_generation(
            query=args.sql_query,
            top_k=args.top_k,
            filters=filters
        )
        
        # Get the tables from the search results
        tables = sql_search_results.get("tables", [])
        if not tables:
            print("No relevant tables found for the query.")
        else:
            print(f"\nFound {len(tables)} relevant tables:")
            
            # Output format depends on whether JSON was requested
            if args.json:
                import json
                print(json.dumps(sql_search_results, indent=2))
            else:
                # Pretty print the table information
                for i, table in enumerate(tables, 1):
                    print(f"\n{i}. Table: {table['full_name']}")
                    print(f"   Description: {table['description']}")
                    # Print partition information if available
                    if table['is_partitioned']:
                        print(f"   Partition Key: {table['partition_key'] or 'unknown'}")
                
                    # Print primary keys if available
                    if table['primary_keys']:
                        print(f"   Primary Keys: {', '.join(table['primary_keys'])}")
                    
                    # Print important join columns
                    if table['join_columns']:
                        print("   Important Join Columns:")
                        for jc in table['join_columns']:
                            key_type = []
                            if jc['is_primary_key']: key_type.append("PK")
                            if jc['is_foreign_key']: key_type.append("FK")
                            key_info = f" ({', '.join(key_type)})" if key_type else ""
                            print(f"     - {jc['name']}{key_info}")
                    
                    # Print columns (top10 most important)
                    top_columns = sorted(table['columns'], key=lambda x: x['importance_score'], reverse=True)[:10]
                    print(f"   Top Columns ({min(10, len(top_columns))} of {table['column_count']}):")
                    for col in top_columns:
                        col_type = []
                        if col['is_primary_key']: col_type.append("PK")
                        if col['is_foreign_key']: col_type.append("FK")
                        if col['is_partition']: col_type.append("PART")
                        type_info = f" ({', '.join(col_type)})" if col_type else ""
                        print(f"     - {col['name']} ({col['data_type']}){type_info}: {col['description'][:50]}")

if __name__ == "__main__":
    main()