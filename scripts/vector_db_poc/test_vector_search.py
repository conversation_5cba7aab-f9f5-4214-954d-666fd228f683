import os
from pathlib import Path
from elasticsearch import Elasticsearch, helpers
from typing import List, Dict, Any, Optional
import google.generativeai as genai
from dotenv import load_dotenv
import re

# Load environment variables from .env file
load_dotenv()

class DocumentLoader:
    """Handles loading and processing of schema documents with structured metadata."""
    
    def __init__(self, docs_dir: str = "documents"):
        """Initialize with the directory containing schema documents."""
        self.docs_dir = Path(docs_dir)
        if not self.docs_dir.exists():
            raise FileNotFoundError(f"Documents directory not found: {self.docs_dir}")
    
    def load_documents(self) -> List[Dict[str, Any]]:
        """
        Recursively load and process all schema files.
        
        Returns:
            List of dictionaries containing structured schema information
        """
        documents = []
        txt_files = list(self.docs_dir.rglob('*.txt'))
        
        if not txt_files:
            print(f"Warning: No .txt files found in {self.docs_dir} or its subdirectories")
            return documents
        
        for file_path in txt_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    content = f.read()
                    if content:
                        # Extract schema and table name from path
                        rel_path = file_path.relative_to(self.docs_dir)
                        schema_parts = str(rel_path.parent).split(os.sep)
                        table_name = file_path.stem
                        
                        # Parse the schema file
                        table_docs = self.parse_schema_to_chunks(
                            content=content,
                            schema_parts=schema_parts,
                            table_name=table_name
                        )
                        documents.extend(table_docs)
                        
            except Exception as e:
                print(f"Error processing file {file_path}: {e}")
        
        print(f"Successfully processed {len(documents)} schema columns from {len(txt_files)} files")
        return documents
    
    @staticmethod
    def parse_schema_to_chunks(content: str, schema_parts: List[str], table_name: str) -> List[Dict]:
        """Parse schema text into structured chunks with metadata."""
        chunks = []
        current_column = None
        current_desc = []
        table_description = ""
        
        # Extract table description
        table_match = re.search(r'## Description\n([^#]+)', content, re.DOTALL)
        if table_match:
            table_description = table_match.group(1).strip()
        
        # Process each line
        for line in content.split('\n'):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            if line.startswith('- '):
                # Save previous column
                if current_column and current_desc:
                    column_info = '\n'.join(current_desc)
                    chunks.append({
                        "content": f"Table: {'.'.join(schema_parts)}.{table_name}\n"
                                 f"Table Description: {table_description}\n"
                                 f"Column: {current_column}\n{column_info}",
                        "metadata": {
                            "schema": schema_parts[0] if len(schema_parts) > 0 else "",
                            "database": schema_parts[1] if len(schema_parts) > 1 else "",
                            "table": table_name,
                            "column": current_column,
                            "full_path": f"{'.'.join(schema_parts)}.{table_name}.{current_column}",
                            "data_type": extract_data_type(column_info),
                            "is_primary_key": "primary key" in column_info.lower(),
                            "is_foreign_key": any(x in column_info.lower() for x in ["foreign key", "references", "join key"]),
                            "description": extract_description(column_info)
                        }
                    })
                
                # Start new column
                current_column = line[2:].split(':')[0].strip()
                current_desc = [line]
            elif current_column:
                current_desc.append(line)
        
        # Add last column
        if current_column and current_desc:
            column_info = '\n'.join(current_desc)
            chunks.append({
                "content": f"Table: {'.'.join(schema_parts)}.{table_name}\n"
                         f"Table Description: {table_description}\n"
                         f"Column: {current_column}\n{column_info}",
                "metadata": {
                    "schema": schema_parts[0] if len(schema_parts) > 0 else "",
                    "database": schema_parts[1] if len(schema_parts) > 1 else "",
                    "table": table_name,
                    "column": current_column,
                    "full_path": f"{'.'.join(schema_parts)}.{table_name}.{current_column}",
                    "data_type": extract_data_type(column_info),
                    "is_primary_key": "primary key" in column_info.lower(),
                    "is_foreign_key": any(x in column_info.lower() for x in ["foreign key", "references", "join key"]),
                    "description": extract_description(column_info)
                }
            })
        
        return chunks

def extract_data_type(column_info: str) -> str:
    """Extract data type from column information."""
    match = re.search(r'Data type[:\s]+([\w\(\)]+)', column_info, re.IGNORECASE)
    return match.group(1).strip() if match else "unknown"

def extract_description(column_info: str) -> str:
    """Extract description from column information."""
    match = re.search(r'Description[:\s]+(.+)', column_info, re.IGNORECASE | re.DOTALL)
    return match.group(1).strip() if match else ""

class VectorSearchPOC:
    """Handles vector search operations using Elasticsearch and Gemini embeddings for schema search."""
    
    def __init__(self, index_name: str = "schema_search"):
        """Initialize with index name and set up connections."""
        self.es = Elasticsearch("http://localhost:9200")
        self.index_name = index_name
        self.dimensions = 768  # Update if using a different model
        self.doc_loader = DocumentLoader()
        
        # Initialize Gemini
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError("GOOGLE_API_KEY not found in environment variables")
            
        genai.configure(api_key=api_key)
        self.embedding_model = genai.embed_content
        self.model_name = "gemini-embedding-exp-03-07"

    def create_index(self):
        """Create an index with hybrid search capabilities for schema search."""
        # Delete existing index if it exists
        try:
            if self.es.indices.exists(index=self.index_name):
                print(f"Index {self.index_name} already exists. Deleting it...")
                self.es.indices.delete(index=self.index_name)
        except Exception as e:
            print(f"Error checking/deleting index: {e}")
        
        # Create index with mapping for hybrid search
        # Simplified mapping for standard Elasticsearch
        mapping = {
            "mappings": {
                "properties": {
                    "content": {
                        "type": "text",
                        "analyzer": "english"
                    },
                    "embedding": {
                        "type": "dense_vector",
                        "dims": self.dimensions,
                        "index": True,
                        "similarity": "cosine"
                    },
                    "metadata": {
                        "type": "nested",
                        "properties": {
                            "schema": {"type": "keyword"},
                            "database": {"type": "keyword"},
                            "table": {"type": "keyword"},
                            "column": {"type": "keyword"},
                            "full_path": {"type": "keyword"},
                            "data_type": {"type": "keyword"},
                            "is_primary_key": {"type": "boolean"},
                            "is_foreign_key": {"type": "boolean"},
                            "description": {"type": "text"}
                        }
                    }
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }
        
        try:
            self.es.indices.create(index=self.index_name, body=mapping)
            print(f"Created index: {self.index_name}")
        except Exception as e:
            print(f"Error creating index: {e}")
            raise

    def generate_embeddings(self, text: str, is_query: bool = False) -> List[float]:
        """Generate embeddings using Gemini model.
        
        Args:
            text: Text to generate embeddings for
            is_query: Whether the text is a search query
            
        Returns:
            List of floats representing the embedding vector
        """
        try:
            response = self.embedding_model(
                model=self.model_name,
                content=text,
                task_type="retrieval_query" if is_query else "retrieval_document"
            )
            return response['embedding']
        except Exception as e:
            print(f"Error generating embeddings: {e}")
            raise

    def index_documents(self, batch_size: int = 50):
        """
        Load and index schema documents with structured metadata.
        
        Args:
            batch_size: Number of documents to process in each batch
        """
        # Load documents with structured schema information
        documents = self.doc_loader.load_documents()
        if not documents:
            print("No schema documents to index.")
            return
        
        actions = []
        total_indexed = 0
        
        print(f"Processing {len(documents)} schema columns...")
        
        for doc in documents:
            try:
                # Generate embedding for the content
                embedding = self.generate_embeddings(doc["content"])
                
                # Prepare document for indexing
                action = {
                    "_index": self.index_name,
                    "_source": {
                        "content": doc["content"],
                        "embedding": embedding,
                        "metadata": doc["metadata"]
                    }
                }
                
                actions.append(action)
                
                # Process in batches
                if len(actions) >= batch_size:
                    helpers.bulk(self.es, actions)
                    total_indexed += len(actions)
                    actions = []
                    print(f"Indexed {total_indexed} columns...")
                    
            except Exception as e:
                print(f"Error processing document: {e}")
                continue
        
        # Process any remaining actions
        if actions:
            helpers.bulk(self.es, actions)
            total_indexed += len(actions)
        
        # Refresh index
        self.es.indices.refresh(index=self.index_name)
        print(f"Finished indexing {total_indexed} schema columns from {len(documents)} documents")

    def search_similar(
        self, 
        query: str, 
        top_k: int = 5, 
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Hybrid search combining vector similarity and keyword matching.
        
        Args:
            query: The search query string
            top_k: Number of results to return
            filters: Dictionary of filters to apply (e.g., {"schema": "blinkit", "data_type": "date"})
            
        Returns:
            List of dictionaries containing matching schema information and metadata
        """
        try:
            # Generate query embedding
            query_embedding = self.generate_embeddings(query, is_query=True)
            
            # Build the query
            must_clauses = [
                {
                    "match": {
                        "content": {
                            "query": query,
                            "boost": 0.5
                        }
                    }
                }
            ]
            
            # Add filters if provided
            if filters:
                for field, value in filters.items():
                    must_clauses.append({
                        "term": {f"metadata.{field}": value}
                    })
            
            search_body = {
                "query": {
                    "bool": {
                        "must": must_clauses,
                        "should": [
                            {
                                "script_score": {
                                    "query": {"match_all": {}},
                                    "script": {
                                        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                                        "params": {"query_vector": query_embedding}
                                    }
                                }
                            }
 ]
                    }
                },
                "size": top_k,
                "_source": ["content", "metadata"]
            }
            
            response = self.es.search(
                index=self.index_name,
                body=search_body
            )
            
            # Process and format results
            results = []
            for hit in response["hits"]["hits"]:
                source = hit["_source"]
                metadata = source.get("metadata", {})
                
                result = {
                    "score": hit["_score"],
                    "content": source["content"],
                    "schema": metadata.get("schema", ""),
                    "table": metadata.get("table", ""),
                    "column": metadata.get("column", ""),
                    "data_type": metadata.get("data_type", ""),
                    "is_primary_key": metadata.get("is_primary_key", False),
                    "is_foreign_key": metadata.get("is_foreign_key", False),
                    "description": metadata.get("description", "")
                }
                results.append(result)
                
            return results
            
        except Exception as e:
            print(f"Error searching for similar documents: {str(e)}")
            return []

if __name__ == "__main__":
    import argparse
    from pprint import pprint
    
    parser = argparse.ArgumentParser(description='Schema Search with Elasticsearch and Gemini')
    parser.add_argument('--query', type=str, help='Search query')
    parser.add_argument('--reindex', action='store_true', help='Reindex all schema documents')
    parser.add_argument('--schema', type=str, help='Filter by schema name')
    parser.add_argument('--data-type', type=str, help='Filter by data type')
    parser.add_argument('--table', type=str, help='Filter by table name')
    args = parser.parse_args()
    
    vs = VectorSearchPOC()
    
    # Create or reindex if needed
    if args.reindex or not vs.es.indices.exists(index=vs.index_name):
        print("Creating index and indexing schema documents...")
        vs.create_index()
        vs.index_documents()
    
    # Prepare filters
    filters = {}
    if args.schema:
        filters["schema"] = args.schema
    if args.data_type:
        filters["data_type"] = args.data_type.lower()
    if args.table:
        filters["table"] = args.table.lower()
    
    if args.query:
        # Single query mode
        print(f"\nSearching for: {args.query}")
        if filters:
            print(f"Filters: {filters}")
            
        results = vs.search_similar(args.query, filters=filters)
        
        if not results:
            print("No results found.")
        else:
            print(f"\nTop {len(results)} results:")
            for i, result in enumerate(results, 1):
                print(f"\n{i}. Score: {result['score']:.4f}")
                print(f"   Table: {result['schema']}.{result['table']}.{result['column']}")
                print(f"   Type: {result['data_type']}")
                if result['is_primary_key']:
                    print("   Primary Key")
                if result['is_foreign_key']:
                    print("   Foreign Key")
                if result['description']:
                    print(f"   Description: {result['description']}")
                print(f"\n   Content:\n   {result['content']}")
    else:
        # Interactive mode
        print("\nSchema Search with Elasticsearch and Gemini")
        print("Type 'exit' to quit\n")
        
        while True:
            try:
                query = input("\nEnter your search query: ").strip()
                if query.lower() in ['exit', 'quit']:
                    break
                    
                if not query:
                    continue
                
                # Check for filter commands
                if query.startswith("/"):
                    parts = query[1:].split("=")
                    if len(parts) == 2:
                        filter_type, value = parts[0].lower(), parts[1].strip()
                        if filter_type in ["schema", "type", "table"]:
                            if filter_type == "type":
                                filter_type = "data_type"
                            filters[filter_type] = value.lower()
                            print(f"Filter set: {filter_type} = {value}")
                        else:
                            print(f"Unknown filter type: {filter_type}")
                    elif query.lower() == "/clear":
                        filters.clear()
                        print("Filters cleared")
                    elif query.lower() == "/filters":
                        print("\nCurrent filters:")
                        for k, v in filters.items():
                            print(f"  {k}: {v}")
                    else:
                        print("\nAvailable commands:")
                        print("  /schema=name - Filter by schema")
                        print("  /type=name   - Filter by data type")
                        print("  /table=name  - Filter by table")
                        print("  /filters     - Show current filters")
                        print("  /clear       - Clear all filters")
                    continue
                
                # Perform search
                print(f"\nSearching for: {query}")
                if filters:
                    print(f"Filters: {filters}")
                
                results = vs.search_similar(query, filters=filters)
                
                if not results:
                    print("No results found.")
                    continue
                
                print(f"\nTop {len(results)} results:")
                for i, result in enumerate(results, 1):
                    print(f"\n{i}. Score: {result['score']:.4f}")
                    print(f"   Table: {result['schema']}.{result['table']}.{result['column']}")
                    print(f"   Type: {result['data_type']}")
                    if result['is_primary_key']:
                        print("   Primary Key")
                    if result['is_foreign_key']:
                        print("   Foreign Key")
                    if result['description']:
                        print(f"   Description: {result['description']}")
                
                print("\nEnter '/help' for filter commands")
                
            except KeyboardInterrupt:
                print("\nExiting...")
                break
            except Exception as e:
                print(f"Error: {e}")
