# Table: blinkit.dwh.dim_date

# Data Dictionary: blinkit.dwh.dim_date

## Description
This table serves as a date dimension table in the data warehouse, providing a comprehensive breakdown of date-related attributes for time-based analysis and reporting.

## Partitioning
This table is not partitioned.

## Columns

- date_key
    - Data type: integer
    - Description: Unique identifier for each date, likely used as a primary key and for joining with fact tables.

- date
    - Data type: date
    - Description: The actual date represented by this row.

- day_of_week
    - Data type: real
    - Description: Numeric representation of the day of the week (e.g., 1-7).

- day_of_week_name
    - Data type: varchar
    - Description: Name of the day of the week (e.g., Monday, Tuesday).

- day_of_month
    - Data type: integer
    - Description: Numeric day of the month (1-31).

- day_of_month_name
    - Data type: varchar
    - Description: Textual representation of the day of the month.

- day_of_year
    - Data type: integer
    - Description: Numeric day of the year (1-366).

- day_of_year_name
    - Data type: varchar
    - Description: Textual representation of the day of the year.

- week
    - Data type: integer
    - Description: Week number within the year.

- week_name
    - Data type: varchar
    - Description: Textual representation of the week.

- week_start_date
    - Data type: date
    - Description: Start date of the week containing this date.

- month
    - Data type: integer
    - Description: Numeric month of the year (1-12).

- month_name
    - Data type: varchar
    - Description: Name of the month (e.g., January, February).

- month_end_date
    - Data type: date
    - Description: Last date of the month containing this date.

- month_start_date
    - Data type: date
    - Description: First date of the month containing this date.

- quarter
    - Data type: integer
    - Description: Quarter of the year (1-4).

- quarter_name
    - Data type: varchar
    - Description: Textual representation of the quarter.

- year
    - Data type: integer
    - Description: Year of the date.

- year_end_date
    - Data type: date
    - Description: Last date of the year containing this date.

- year_start_date
    - Data type: date
    - Description: First date of the year containing this date.

- is_weekday
    - Data type: boolean
    - Description: Indicates whether the date is a weekday.

- is_weekend
    - Data type: boolean
    - Description: Indicates whether the date is a weekend.

- yearmonth
    - Data type: varchar
    - Description: Combined year and month representation (e.g., "202301" for January 2023).

- yearmonth_rank
    - Data type: integer
    - Description: Ranking of the year-month combination, useful for chronological sorting.

- record_creation_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of when the record was created in Indian Standard Time.

## Potential JOIN Keys
- `date_key`: Likely the primary key, used for joining with fact tables.
- `date`: Can be used for date-based joins with other tables.

## Business-Critical Columns
- `date_key`
- `date`
- `year`
- `month`
- `quarter`

## Relationships
This dimension table can potentially be joined with any fact table that contains date-related fields for time-based analysis and reporting.