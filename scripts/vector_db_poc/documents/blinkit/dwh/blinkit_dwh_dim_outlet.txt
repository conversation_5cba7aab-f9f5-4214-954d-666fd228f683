# Table: blinkit.dwh.dim_outlet

# blinkit.dwh.dim_outlet

## Description
This table contains detailed information about outlets, including their characteristics, locations, and operational details. It serves as a dimension table for outlet-related data in the data warehouse.

## Partitioning
This table is not partitioned.

## Columns

- outlet_id
    - Data type: integer
    - Description: Unique identifier for each outlet, likely used as a primary key and for joining with fact tables.

- outlet_name
    - Data type: varchar
    - Description: The name of the outlet.

- facility_id
    - Data type: integer
    - Description: Identifier for the facility associated with the outlet, potentially for joining with a facilities table.

- is_outlet_active
    - Data type: integer
    - Description: Indicates whether the outlet is currently active (1) or inactive (0).

- outlet_type
    - Data type: varchar
    - Description: Categorizes the type of outlet.

- business_type_id
    - Data type: integer
    - Description: Identifier for the business type of the outlet.

- business_type_name
    - Data type: varchar
    - Description: Name of the business type associated with the outlet.

- company_type_id
    - Data type: integer
    - Description: Identifier for the company type associated with the outlet.

- live_date
    - Data type: date
    - Description: The date when the outlet went live or became operational.

- geo_location_lat
    - Data type: varchar
    - Description: Latitude coordinate of the outlet's geographical location.

- geo_location_lon
    - Data type: varchar
    - Description: Longitude coordinate of the outlet's geographical location.

- carpet_area_in_square_feet
    - Data type: varchar
    - Description: The carpet area of the outlet in square feet.

- storage_capacity
    - Data type: varchar
    - Description: The storage capacity of the outlet.

- outlet_manager_email
    - Data type: varchar
    - Description: Email address of the outlet manager.

- location_address
    - Data type: varchar
    - Description: Physical address of the outlet.

- is_current
    - Data type: boolean
    - Description: Indicates if this is the current record for the outlet in case of historical tracking.

- install_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of when the outlet was installed or set up in the system.

- update_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of the last update to the outlet record.

- etl_snapshot_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of when this data was last extracted or processed in the ETL pipeline.

- dbt_scd_id
    - Data type: varchar
    - Description: Unique identifier for slowly changing dimension tracking.

- dbt_updated_at
    - Data type: timestamp(6)
    - Description: Timestamp of when this record was last updated by dbt.

- dbt_valid_from
    - Data type: timestamp(6)
    - Description: Timestamp from which this record is considered valid in the slowly changing dimension.

- dbt_valid_to
    - Data type: timestamp(6)
    - Description: Timestamp until which this record is considered valid in the slowly changing dimension.

- is_bistro_outlet
    - Data type: boolean
    - Description: Indicates whether the outlet is a bistro type.

- outlet_ownership
    - Data type: varchar
    - Description: Describes the ownership type of the outlet.

- is_longtail_outlet
    - Data type: boolean
    - Description: Indicates whether the outlet is considered a long-tail outlet.

- is_express_outlet
    - Data type: boolean
    - Description: Indicates whether the outlet is an express type.

## Key Information
- Primary Key: `outlet_id`
- Potential JOIN keys: `outlet_id`, `facility_id`, `business_type_id`, `company_type_id`
- Business-critical columns: `is_outlet_active`, `outlet_type`, `live_date`, `geo_location_lat`, `geo_location_lon`
- This table may be related to other tables containing order, product, or facility information based on the `outlet_id` and `facility_id` columns.