# Table: blinkit.dwh.dim_device

# Data Dictionary: blinkit.dwh.dim_device

## Description
This table appears to be a device dimension table in the Blinkit data warehouse. It stores information about user devices, including their usage history, characteristics, and associated user data.

## Partitioning
This table is partitioned by `at_date_ist`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition.

## Columns

- at_date_ist
    - Data type: date
    - Description: The date for which the device information is recorded, likely used for historical tracking and partitioning.

- device_id
    - Data type: varchar
    - Description: Unique identifier for each device, potential JOIN key with other device-related tables.

- first_seen_date_ist
    - Data type: date
    - Description: The date when the device was first observed in the system.

- first_order_date_ist
    - Data type: date
    - Description: The date of the first order placed using this device.

- last_order_date_ist
    - Data type: date
    - Description: The date of the most recent order placed using this device.

- user_bucket
    - Data type: varchar
    - Description: A categorization or segmentation of users, possibly for analysis or targeting purposes.

- user_type
    - Data type: varchar
    - Description: Classification of the user associated with the device.

- etl_snapshot_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of when this data was last updated in the data warehouse.

- last_seen_city_name
    - Data type: varchar
    - Description: The name of the city where the device was last active.

- first_seen_city_name
    - Data type: varchar
    - Description: The name of the city where the device was first observed.

- last_seen_date_ist
    - Data type: date
    - Description: The most recent date when the device was active.

- device_model
    - Data type: varchar
    - Description: The model name or number of the device.

- platform
    - Data type: varchar
    - Description: The operating system or platform of the device (e.g., iOS, Android).

- device_manufacturer
    - Data type: varchar
    - Description: The manufacturer of the device.

## Key Relationships
- `device_id` is likely a key column for joining with other device-related tables.
- `first_order_date_ist` and `last_order_date_ist` suggest a relationship with an orders table.
- `last_seen_city_name` and `first_seen_city_name` may relate to a locations or cities table.

## Business-Critical Columns
- `device_id`
- `user_type`
- `user_bucket`
- `last_order_date_ist`
- `platform`