# Table: blinkit.dwh.dim_merchant_outlet_facility_mapping

# Table: blinkit.dwh.dim_merchant_outlet_facility_mapping

## Description
This table appears to be a dimensional table mapping various aspects of merchants, outlets, and facilities. It likely serves as a central reference for merchant and outlet information, including frontend and backend details, POS (Point of Sale) outlet data, and facility information.

## Partitioning
This table is not partitioned.

## Columns

- id
    - Data type: integer
    - Description: Unique identifier for each record in the table.

- frontend_merchant_id
    - Data type: bigint
    - Description: Identifier for the frontend merchant.

- frontend_merchant_name
    - Data type: varchar
    - Description: Name of the frontend merchant.

- frontend_merchant_city_id
    - Data type: integer
    - Description: City identifier for the frontend merchant.

- frontend_merchant_city_name
    - Data type: varchar
    - Description: City name of the frontend merchant.

- backend_merchant_id
    - Data type: integer
    - Description: Identifier for the backend merchant.

- backend_merchant_name
    - Data type: varchar
    - Description: Name of the backend merchant.

- backend_merchant_city_id
    - Data type: integer
    - Description: City identifier for the backend merchant.

- backend_merchant_city_name
    - Data type: varchar
    - Description: City name of the backend merchant.

- pos_outlet_id
    - Data type: integer
    - Description: Identifier for the Point of Sale outlet.

- pos_outlet_name
    - Data type: varchar
    - Description: Name of the Point of Sale outlet.

- pos_outlet_city_id
    - Data type: integer
    - Description: City identifier for the Point of Sale outlet.

- pos_outlet_city_name
    - Data type: varchar
    - Description: City name of the Point of Sale outlet.

- facility_id
    - Data type: integer
    - Description: Identifier for the facility.

- facility_name
    - Data type: varchar
    - Description: Name of the facility.

- merchant_business_type_id
    - Data type: integer
    - Description: Identifier for the merchant's business type.

- merchant_business_type_name
    - Data type: varchar
    - Description: Name of the merchant's business type.

- chain_id
    - Data type: integer
    - Description: Identifier for the chain the merchant belongs to.

- is_frontend_merchant_active
    - Data type: boolean
    - Description: Indicates if the frontend merchant is currently active.

- is_backend_merchant_active
    - Data type: boolean
    - Description: Indicates if the backend merchant is currently active.

- is_pos_outlet_active
    - Data type: integer
    - Description: Indicates if the Point of Sale outlet is currently active.

- is_mapping_enabled
    - Data type: boolean
    - Description: Indicates if the mapping for this record is enabled.

- is_express_store
    - Data type: boolean
    - Description: Indicates if the store is an express store.

- install_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of installation in IST (Indian Standard Time).

- update_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of last update in IST.

- merchant_mapping_creation_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of merchant mapping creation in IST.

- merchant_mapping_updation_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of last merchant mapping update in IST.

- is_current_mapping_active
    - Data type: boolean
    - Description: Indicates if the current mapping is active.

- merchant_mapping_rank
    - Data type: bigint
    - Description: Ranking of the merchant mapping.

- is_current
    - Data type: boolean
    - Description: Indicates if this is the current record.

- dbt_scd_id
    - Data type: varchar
    - Description: Unique identifier for Slowly Changing Dimension (SCD) tracking.

- dbt_updated_at
    - Data type: timestamp(6)
    - Description: Timestamp of last update by dbt.

- dbt_valid_from
    - Data type: timestamp(6)
    - Description: Timestamp from which this record is valid.

- dbt_valid_to
    - Data type: timestamp(6)
    - Description: Timestamp until which this record is valid.

- is_bistro_outlet
    - Data type: boolean
    - Description: Indicates if the outlet is a bistro.

## Potential JOIN Keys and Business-Critical Columns
- Potential JOIN keys: id, frontend_merchant_id, backend_merchant_id, pos_outlet_id, facility_id, chain_id
- Business-critical columns: is_frontend_merchant_active, is_backend_merchant_active, is_pos_outlet_active, is_mapping_enabled, is_current

## Relationships to Other Tables
This table may have relationships with other tables based on merchant, outlet, and facility IDs. It could be linked to transaction tables, inventory tables, or other dimensional tables related to merchants and outlets.