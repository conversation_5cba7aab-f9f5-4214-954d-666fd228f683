# Table: blinkit.dwh.dim_customer

# Data Dictionary: blinkit.dwh.dim_customer

## Description
This table serves as a dimension table for customer information in the Blinkit data warehouse. It contains various attributes and flags related to customer status, loyalty, and account details.

## Partitioning
This table is not partitioned.

## Columns

- customer_id
    - Data type: bigint
    - Description: Unique identifier for each customer, likely a primary key and potential JOIN key.

- is_employee
    - Data type: boolean
    - Description: Flag indicating whether the customer is an employee of the company.

- is_merchant
    - Data type: boolean
    - Description: Flag indicating whether the customer is a merchant.

- is_blocked
    - Data type: boolean
    - Description: Flag indicating whether the customer's account is blocked.

- referrer_code
    - Data type: varchar
    - Description: Code used for referral tracking.

- win_win_balance
    - Data type: integer
    - Description: Balance in the customer's win-win program, possibly a loyalty or rewards system.

- loyalty_segment
    - Data type: varchar
    - Description: Segment classification for the customer's loyalty status.

- loyalty_score
    - Data type: varchar
    - Description: Score representing the customer's loyalty level.

- is_active_sbc_membership
    - Data type: boolean
    - Description: Flag indicating whether the customer has an active SBC (possibly Subscription-Based Customer) membership.

- sbc_membership_plan_price
    - Data type: real
    - Description: Price of the customer's SBC membership plan.

- create_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of record creation in Indian Standard Time.

- update_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of last update in Indian Standard Time.

- create_ts_utc
    - Data type: timestamp(6)
    - Description: Timestamp of record creation in Coordinated Universal Time.

- update_ts_utc
    - Data type: timestamp(6)
    - Description: Timestamp of last update in Coordinated Universal Time.

- etl_snapshot_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of the ETL snapshot in Indian Standard Time.

- wallet_updation_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of the last wallet update in Indian Standard Time.

- phone_number
    - Data type: varchar
    - Description: Customer's phone number.

- org_channel_id
    - Data type: integer
    - Description: Identifier for the organizational channel associated with the customer.

- master_customer_id
    - Data type: bigint
    - Description: Identifier for the master customer record, potentially used for consolidating multiple accounts.

- customer_type
    - Data type: varchar
    - Description: Classification of the customer type.

## Notes
- The `customer_id` is likely the primary key and a potential JOIN key for relating to other tables such as orders or transactions.
- `master_customer_id` suggests possible relationships with other customer-related tables for account consolidation.
- Loyalty-related columns (`loyalty_segment`, `loyalty_score`) are business-critical for customer analysis and segmentation.
- Time-related columns with both IST and UTC suggest the system operates across multiple time zones.