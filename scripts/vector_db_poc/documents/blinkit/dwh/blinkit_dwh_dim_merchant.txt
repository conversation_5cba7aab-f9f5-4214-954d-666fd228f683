# Table: blinkit.dwh.dim_merchant

# Data Dictionary: blinkit.dwh.dim_merchant

## Description
This table serves as a dimensional table for merchant information in the Blinkit data warehouse. It contains detailed attributes about merchants, including their identification, location, business type, and operational status.

## Partitioning
This table is not partitioned.

## Columns

- merchant_key
    - Data type: bigint
    - Description: Unique identifier for the merchant, likely used as a primary key.

- merchant_id
    - Data type: bigint
    - Description: Alternative identifier for the merchant, possibly used for joining with transactional tables.

- merchant_name
    - Data type: varchar
    - Description: The name of the merchant.

- chain_id
    - Data type: integer
    - Description: Identifier for the chain the merchant belongs to, if applicable.

- chain_name
    - Data type: varchar
    - Description: Name of the chain the merchant is part of.

- merchant_store_type
    - Data type: varchar
    - Description: Categorizes the type of store (e.g., grocery, pharmacy, etc.).

- merchant_delivery_type
    - Data type: varchar
    - Description: Indicates the delivery method used by the merchant.

- city_id
    - Data type: integer
    - Description: Identifier for the city where the merchant is located.

- city_name
    - Data type: varchar
    - Description: Name of the city where the merchant is located.

- virtual_merchant_type
    - Data type: varchar
    - Description: Categorizes the merchant if it's a virtual or online-only entity.

- is_current
    - Data type: boolean
    - Description: Indicates if this is the current record for the merchant.

- merchant_business_type
    - Data type: varchar
    - Description: Specifies the business category of the merchant.

- merchant_store_latitude
    - Data type: double
    - Description: Latitude coordinate of the merchant's store location.

- merchant_store_longitude
    - Data type: double
    - Description: Longitude coordinate of the merchant's store location.

- dbt_scd_id
    - Data type: varchar
    - Description: Unique identifier for the slowly changing dimension record.

- dbt_updated_at
    - Data type: timestamp(6)
    - Description: Timestamp of when the record was last updated.

- dbt_valid_from
    - Data type: timestamp(6)
    - Description: Timestamp indicating when this version of the record became valid.

- dbt_valid_to
    - Data type: timestamp(6)
    - Description: Timestamp indicating when this version of the record became invalid.

- is_enabled
    - Data type: boolean
    - Description: Indicates if the merchant is currently enabled in the system.

- is_active
    - Data type: boolean
    - Description: Indicates if the merchant is currently active.

- install_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of when the merchant was installed or onboarded to the system.

## Potential JOIN Keys and Business-Critical Columns
- `merchant_key` and `merchant_id` are likely used for joining with fact tables.
- `chain_id` may be used to join with a chain dimension table.
- `city_id` could be used to join with a city or location dimension table.
- `is_current`, `is_enabled`, and `is_active` are critical for filtering current and active merchants.

## Potential Relationships
This table may be related to order fact tables, product dimension tables, and location dimension tables through the merchant identifiers.