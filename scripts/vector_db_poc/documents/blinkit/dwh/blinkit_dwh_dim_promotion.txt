# Table: blinkit.dwh.dim_promotion

# Data Dictionary: blinkit.dwh.dim_promotion

## Description
This table stores information about promotional offers or campaigns in the Blinkit system. It contains details about promotion codes, validity periods, types, reasons, and associated metadata.

## Partitioning
This table is not partitioned.

## Columns

- promotion_id
    - Data type: integer
    - Description: Unique identifier for each promotion.

- promo_code
    - Data type: varchar
    - Description: The code used to apply the promotion.

- promo_description
    - Data type: varchar
    - Description: Detailed description of the promotion.

- valid_from_ts_ist
    - Data type: timestamp(6)
    - Description: Start date and time of the promotion's validity in IST.

- valid_till_ts_ist
    - Data type: timestamp(6)
    - Description: End date and time of the promotion's validity in IST.

- is_active
    - Data type: boolean
    - Description: Indicates whether the promotion is currently active.

- type_id
    - Data type: integer
    - Description: Identifier for the type of promotion.

- type_name
    - Data type: varchar
    - Description: Name or description of the promotion type.

- reason_id
    - Data type: integer
    - Description: Identifier for the reason of the promotion.

- reason_name
    - Data type: varchar
    - Description: Name or description of the promotion reason.

- install_ts_utc
    - Data type: timestamp(6)
    - Description: Timestamp of when the promotion was installed or created in UTC.

- install_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of when the promotion was installed or created in IST.

- update_ts_utc
    - Data type: timestamp(6)
    - Description: Timestamp of the last update to the promotion record in UTC.

- update_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of the last update to the promotion record in IST.

- etl_snapshot_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of when this record was last loaded or updated in the data warehouse.

- blinkit_share
    - Data type: real
    - Description: Percentage or fraction representing Blinkit's share in the promotion cost.

## Key Information
- Primary key: `promotion_id`
- Potential JOIN keys: `promotion_id`, `type_id`, `reason_id`
- Business-critical columns: `promo_code`, `valid_from_ts_ist`, `valid_till_ts_ist`, `is_active`, `blinkit_share`

This table may be related to other tables such as order tables or product tables, where promotions could be applied.