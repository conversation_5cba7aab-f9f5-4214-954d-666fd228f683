# Table: blinkit.dwh.fact_sales_order_item_details

# Data Dictionary: blinkit.dwh.fact_sales_order_item_details

## Description
This table appears to be a fact table containing detailed information about individual items in sales orders. It includes various dimensions, quantities, prices, and statuses related to each order item.

## Partitioning
This table is partitioned by `order_create_dt_ist`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition.

## Columns

- order_item_id
    - Data type: bigint
    - Description: Unique identifier for each order item.

- order_id
    - Data type: bigint
    - Description: Identifier for the parent order.

- suborder_item_id
    - Data type: bigint
    - Description: Identifier for sub-items within an order item.

- suborder_id
    - Data type: bigint
    - Description: Identifier for suborders.

- cart_id
    - Data type: bigint
    - Description: Identifier for the shopping cart associated with the order.

- dim_product_key
    - Data type: bigint
    - Description: Foreign key to the product dimension table.

- dim_customer_key
    - Data type: bigint
    - Description: Foreign key to the customer dimension table.

- dim_customer_address_key
    - Data type: bigint
    - Description: Foreign key to the customer address dimension table.

- dim_frontend_merchant_key
    - Data type: bigint
    - Description: Foreign key to the frontend merchant dimension table.

- dim_backend_merchant_key
    - Data type: bigint
    - Description: Foreign key to the backend merchant dimension table.

- product_quantity
    - Data type: bigint
    - Description: Quantity of the product ordered.

- procured_quantity
    - Data type: bigint
    - Description: Quantity of the product procured.

- total_selling_price
    - Data type: real
    - Description: Total selling price of the order item.

- unit_selling_price
    - Data type: real
    - Description: Selling price per unit of the product.

- total_procurement_price
    - Data type: real
    - Description: Total procurement cost for the order item.

- total_mrp
    - Data type: real
    - Description: Total Maximum Retail Price for the order item.

- unit_mrp
    - Data type: real
    - Description: Maximum Retail Price per unit of the product.

- total_discount_amount
    - Data type: real
    - Description: Total discount applied to the order item.

- unit_discount_amount
    - Data type: real
    - Description: Discount amount per unit of the product.

- total_cashback_amount
    - Data type: real
    - Description: Total cashback amount for the order item.

- unit_cashback_amount
    - Data type: real
    - Description: Cashback amount per unit of the product.

- total_weighted_landing_price
    - Data type: real
    - Description: Total weighted landing price for the order item.

- unit_weighted_landing_price
    - Data type: real
    - Description: Weighted landing price per unit of the product.

- total_brand_fund
    - Data type: real
    - Description: Total brand fund amount for the order item.

- unit_brand_fund
    - Data type: real
    - Description: Brand fund amount per unit of the product.

- total_retained_margin
    - Data type: real
    - Description: Total retained margin for the order item.

- unit_retained_margin
    - Data type: real
    - Description: Retained margin per unit of the product.

- channel
    - Data type: varchar
    - Description: Sales channel through which the order was placed.

- sbc_enabled
    - Data type: boolean
    - Description: Indicates if SBC (possibly Store-Based Checkout) is enabled.

- order_type
    - Data type: varchar
    - Description: Type of the order.

- order_current_status
    - Data type: varchar
    - Description: Current status of the order.

- suborder_current_status
    - Data type: varchar
    - Description: Current status of the suborder.

- cancelled_quantity
    - Data type: bigint
    - Description: Quantity of items cancelled in the order.

- item_cancellation_reason
    - Data type: varchar
    - Description: Reason for item cancellation, if applicable.

- original_order_item_id
    - Data type: varchar
    - Description: Original order item ID, possibly for tracking returns or exchanges.

- order_create_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of order creation in IST.

- order_create_dim_date_key
    - Data type: bigint
    - Description: Foreign key to the date dimension for order creation date.

- order_item_create_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of order item creation in IST.

- order_item_create_dim_date_key
    - Data type: bigint
    - Description: Foreign key to the date dimension for order item creation date.

- etl_oi_update_ts
    - Data type: timestamp(6)
    - Description: Timestamp of the last ETL update for order items.

- etl_oo_update_ts
    - Data type: timestamp(6)
    - Description: Timestamp of the last ETL update for orders.

- etl_s_update_ts
    - Data type: timestamp(6)
    - Description: Timestamp of the last ETL update for suborders.

- etl_si_update_ts
    - Data type: timestamp(6)
    - Description: Timestamp of the last ETL update for suborder items.

- etl_oc_update_ts
    - Data type: timestamp(6)
    - Description: Timestamp of the last ETL update for order cancellations.

- etl_om_update_ts
    - Data type: timestamp(6)
    - Description: Timestamp of the last ETL update for order modifications.

- etl_snapshot_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of the ETL snapshot in IST.

- suborder_rank
    - Data type: bigint
    - Description: Rank of the suborder within the main order.

- cart_checkout_dim_date_key
    - Data type: bigint
    - Description: Foreign key to the date dimension for cart checkout date.

- cart_checkout_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of cart checkout in IST.

- unit_liquid_fund
    - Data type: real
    - Description: Liquid fund amount per unit of the product.

- total_liquid_fund
    - Data type: real
    - Description: Total liquid fund amount for the order item.

- station_name
    - Data type: varchar
    - Description: Name of the station or location associated with the order.

- city_name
    - Data type: varchar
    - Description: Name of the city associated with the order.

- unit_self_fund
    - Data type: real
    - Description: Self-fund amount per unit of the product.

- total_self_fund
    - Data type: real
    - Description: Total self-fund amount for the order item.

- product_id
    - Data type: bigint
    - Description: Identifier for the product.

- frontend_merchant_id
    - Data type: bigint
    - Description: Identifier for the frontend merchant.

- backend_merchant_id
    - Data type: bigint
    - Description: Identifier for the backend merchant.

- outlet_id
    - Data type: bigint
    - Description: Identifier for the outlet associated with the order.

- total_doorstep_return_quantity
    - Data type: real
    - Description: Total quantity of items returned at the doorstep.

- total_doorstep_return_selling_price
    - Data type: real
    - Description: Total selling price of items returned at the doorstep.

- doorstep_return_reason
    - Data type: varchar
    - Description: Reason for doorstep return, if applicable.

- org_channel_id
    - Data type: varchar
    - Description: Identifier for the organizational channel.

- org_channel_name
    - Data type: varchar
    - Description: Name of the organizational channel.

- dim_master_customer_key
    - Data type: bigint
    - Description: Foreign key to the master customer dimension table.

- is_internal_order
    - Data type: boolean
    - Description: Indicates if the order is an internal/test order.

- dim_assortment_type_key
    - Data type: bigint
    - Description: Foreign key to the assortment type dimension table.

- pricing_change_log_id
    - Data type: bigint
    - Description: Identifier for the pricing change log.

- pricing_unit_wlp
    - Data type: real
    - Description: Unit weighted landing price used for pricing.

- pricing_unit_brand_fund
    - Data type: real
    - Description: Unit brand fund amount used for pricing.

- etl_update_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of the last ETL update in IST.

- order_create_dt_ist
    - Data type: date
    - Description: Date of order creation in IST (partition key).

- additional_dwh_info
    - Data type: varchar
    - Description: Additional data warehouse information.

- is_easy_return_order_item
    - Data type: boolean
    - Description: Indicates if the order item is eligible for easy return.

- is_easy_return_order
    - Data type: boolean
    - Description: Indicates if the entire order is eligible for easy return.

- pos_unit_mrp
    - Data type: double
    - Description: Point of Sale unit Maximum Retail Price.

- invoice_details
    - Data type: array(row(invoice_id varchar, invoice_type_id integer, variant_id varchar, unit_mrp double, quantity integer))
    - Description: Array of invoice details including invoice ID, type, variant, MRP, and quantity.

- is_digital_voucher_order
    - Data type: boolean
    - Description: Indicates if the order is for a digital voucher.

- pricing_change_log_id_new
    - Data type: varchar
    - Description: New identifier for the pricing change log.

- is_open_box_delivery
    - Data type: boolean
    - Description: Indicates if the order is for open box delivery.

## Potential JOIN Keys
- dim_product_key
- dim_customer_key
- dim_customer_address_key
- dim_frontend_merchant_key
- dim_backend_merchant_key
- order_create_dim_date_key
- order_item_create_dim_date_key
- cart_checkout_dim_date_key
- dim_master_customer_key
- dim_assortment_type_key

## Business-Critical Columns
- order_item_id
- order_id
- product_quantity
- total_selling_price
- order_current_status
- order_create_ts_ist
- etl_snapshot_ts_ist

## Potential Relationships
This table likely has relationships with dimension tables for products, customers, merchants, dates, and possibly order headers or transaction summaries.