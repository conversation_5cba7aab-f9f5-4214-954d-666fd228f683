# Table: blinkit.dwh.dim_product

# Table: blinkit.dwh.dim_product

## Description
This table serves as a product dimension table in the data warehouse, containing detailed information about products sold on the Blinkit platform. It includes product attributes, categorization, and temporal data for tracking changes over time.

## Partitioning
This table is not partitioned.

## Columns

- product_key
    - Data type: bigint
    - Description: Unique identifier for each product record, likely used as a primary key.

- product_id
    - Data type: bigint
    - Description: Unique identifier for each product, potentially used for joining with other tables.

- product_name
    - Data type: varchar
    - Description: The name of the product as displayed to customers.

- unit
    - Data type: varchar
    - Description: The unit of measurement or packaging for the product.

- product_type
    - Data type: varchar
    - Description: The type or category of the product.

- brand_name
    - Data type: varchar
    - Description: The name of the brand associated with the product.

- manufacturer_name
    - Data type: varchar
    - Description: The name of the manufacturer of the product.

- l0_category, l1_category, l2_category
    - Data type: varchar
    - Description: Hierarchical category levels for product classification.

- l0_category_id, l1_category_id, l2_category_id
    - Data type: bigint
    - Description: Unique identifiers for each category level.

- is_private_label
    - Data type: boolean
    - Description: Indicates if the product is a private label brand.

- is_combo
    - Data type: boolean
    - Description: Indicates if the product is a combination or bundle of multiple items.

- create_ts_ist, update_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamps for record creation and last update in IST timezone.

- is_current
    - Data type: boolean
    - Description: Indicates if this is the current active record for the product.

- product_type_id, brand_id
    - Data type: bigint
    - Description: Unique identifiers for product type and brand, potentially for joining with other dimension tables.

- is_product_enabled
    - Data type: boolean
    - Description: Indicates if the product is currently available for sale.

- etl_snapshot_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of when this record was last processed in the ETL pipeline.

- valid_from_ts_ist, valid_to_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamps indicating the validity period of this product record.

- additional_details
    - Data type: array(row(...))
    - Description: Structured array containing additional product details.

- business_category_id, business_category_name
    - Data type: bigint, varchar
    - Description: Identifier and name of the business category for the product.

- valid_from_ts_ist_enriched, valid_to_ts_ist_enriched
    - Data type: timestamp(6)
    - Description: Enriched timestamps for record validity, possibly after data processing.

- additional_dwh_info
    - Data type: array(varchar)
    - Description: Array of additional information relevant to data warehouse operations.

- list_update_ts_ist
    - Data type: row(...)
    - Description: Structured record of update timestamps for various related entities.

- update_ts_ist_enriched
    - Data type: timestamp(6)
    - Description: Enriched timestamp of the last update, possibly after data processing.

## Key Information
- Potential JOIN keys: product_key, product_id, l0_category_id, l1_category_id, l2_category_id, product_type_id, brand_id, business_category_id
- Business-critical columns: product_name, brand_name, category columns, is_product_enabled
- This table likely has relationships with order tables, inventory tables, and other dimension tables (e.g., brand, category) in the data warehouse.