# Table: blinkit.dwh.fact_sales_order_details

# Data Dictionary: blinkit.dwh.fact_sales_order_details

## Description
This table contains detailed information about sales orders, including customer data, order metrics, financial details, and various timestamps related to order processing. It serves as a comprehensive fact table for analyzing sales transactions and order performance.

## Partitioning
This table is partitioned by `order_create_dt_ist`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in Any condition.

## Columns

- order_id
    - Data type: bigint
    - Description: Unique identifier for each order, likely a primary key and potential JOIN key with other order-related tables.

- cart_id
    - Data type: bigint
    - Description: Identifier for the shopping cart associated with the order.

- dim_customer_key
    - Data type: bigint
    - Description: Foreign key linking to the customer dimension table.

- dim_customer_address_key
    - Data type: bigint
    - Description: Foreign key linking to the customer address dimension table.

- dim_frontend_merchant_key
    - Data type: bigint
    - Description: Foreign key linking to the frontend merchant dimension table.

- total_product_quantity
    - Data type: bigint
    - Description: Total number of products in the order.

- total_procured_quantity
    - Data type: bigint
    - Description: Total quantity of products procured for the order.

- total_mrp
    - Data type: real
    - Description: Total Maximum Retail Price of all products in the order.

- total_selling_price
    - Data type: real
    - Description: Total selling price of all products in the order.

- total_procurement_price
    - Data type: real
    - Description: Total procurement cost of all products in the order.

- total_discount_amount
    - Data type: real
    - Description: Total discount applied to the order.

- total_cashback_amount
    - Data type: real
    - Description: Total cashback amount for the order.

- total_additional_charges_amount
    - Data type: real
    - Description: Total additional charges applied to the order.

- total_delivery_cost
    - Data type: real
    - Description: Total cost of delivery for the order.

- total_weighted_landing_price
    - Data type: real
    - Description: Total weighted landing price for the order.

- total_brand_fund
    - Data type: real
    - Description: Total brand fund amount associated with the order.

- total_retained_margin
    - Data type: real
    - Description: Total retained margin for the order.

- sbc_enabled
    - Data type: boolean
    - Description: Indicates if SBC (possibly Smart Basket Checkout) is enabled for the order.

- promo_code
    - Data type: varchar
    - Description: Promotional code applied to the order, if any.

- channel
    - Data type: varchar
    - Description: Sales channel through which the order was placed.

- payment_method
    - Data type: varchar
    - Description: Method of payment used for the order.

- device_id
    - Data type: varchar
    - Description: Identifier of the device used to place the order.

- app_version
    - Data type: varchar
    - Description: Version of the application used to place the order.

- order_type
    - Data type: varchar
    - Description: Type or category of the order.

- order_current_status
    - Data type: varchar
    - Description: Current status of the order.

- cart_checkout_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of cart checkout in IST timezone.

- cart_checkout_dim_date_key
    - Data type: bigint
    - Description: Foreign key linking to the date dimension for cart checkout.

- order_create_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of order creation in IST timezone.

- order_create_dim_date_key
    - Data type: bigint
    - Description: Foreign key linking to the date dimension for order creation.

- order_schedule_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of order scheduling in IST timezone.

- order_schedule_dim_date_key
    - Data type: bigint
    - Description: Foreign key linking to the date dimension for order scheduling.

- order_schedule_slot
    - Data type: varchar
    - Description: Delivery slot scheduled for the order.

- order_deliver_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of order delivery in IST timezone.

- order_deliver_dim_date_key
    - Data type: bigint
    - Description: Foreign key linking to the date dimension for order delivery.

- order_cancel_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of order cancellation in IST timezone, if applicable.

- order_cancel_dim_date_key
    - Data type: bigint
    - Description: Foreign key linking to the date dimension for order cancellation.

- etl_fact_order_item_snapshot_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of ETL snapshot for order item facts.

- etl_oo_update_ts
    - Data type: timestamp(6)
    - Description: Timestamp of ETL update for order operations.

- etl_oc_update_ts
    - Data type: timestamp(6)
    - Description: Timestamp of ETL update for order cancellations.

- etl_snapshot_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of overall ETL snapshot in IST timezone.

- etl_delivered_event_rank
    - Data type: bigint
    - Description: Rank of the delivered event in ETL processing.

- etl_cancelled_event_rank
    - Data type: bigint
    - Description: Rank of the cancelled event in ETL processing.

- total_liquid_fund
    - Data type: real
    - Description: Total liquid fund amount associated with the order.

- slot_charges
    - Data type: real
    - Description: Charges applied for the delivery slot.

- packaging_cost
    - Data type: real
    - Description: Cost of packaging for the order.

- tip_amount
    - Data type: real
    - Description: Tip amount given by the customer.

- total_self_fund
    - Data type: real
    - Description: Total self-funded amount for the order.

- city_name
    - Data type: varchar
    - Description: Name of the city where the order was placed.

- station_name
    - Data type: varchar
    - Description: Name of the station or location associated with the order.

- cart_range
    - Data type: varchar
    - Description: Range or category of the cart value.

- cart_rank
    - Data type: bigint
    - Description: Rank of the cart based on some criteria.

- delivered_cart_rank
    - Data type: bigint
    - Description: Rank of the delivered cart based on some criteria.

- frontend_merchant_id
    - Data type: bigint
    - Description: Identifier for the frontend merchant associated with the order.

- dim_promotion_key
    - Data type: bigint
    - Description: Foreign key linking to the promotion dimension table.

- is_internal_order
    - Data type: boolean
    - Description: Indicates if the order is for internal purposes.

- order_external_id
    - Data type: varchar
    - Description: External identifier for the order, if applicable.

- total_net_cost
    - Data type: real
    - Description: Total net cost of the order.

- order_rating
    - Data type: bigint
    - Description: Rating given to the order by the customer.

- outlet_id
    - Data type: bigint
    - Description: Identifier for the outlet fulfilling the order.

- total_doorstep_return_quantity
    - Data type: real
    - Description: Total quantity of items returned at the doorstep.

- total_doorstep_return_selling_price
    - Data type: real
    - Description: Total selling price of items returned at the doorstep.

- convenience_charge
    - Data type: real
    - Description: Convenience charge applied to the order.

- total_cart_level_discount_amount
    - Data type: real
    - Description: Total discount amount applied at the cart level.

- total_net_discount_amount
    - Data type: real
    - Description: Total net discount amount for the order.

- org_channel_id
    - Data type: varchar
    - Description: Identifier for the organizational channel.

- org_channel_name
    - Data type: varchar
    - Description: Name of the organizational channel.

- is_gift_order
    - Data type: boolean
    - Description: Indicates if the order is a gift order.

- dim_master_customer_key
    - Data type: bigint
    - Description: Foreign key linking to the master customer dimension table.

- dim_customer_key_cart_rank
    - Data type: bigint
    - Description: Rank of the customer based on cart value.

- dim_customer_key_delivered_cart_rank
    - Data type: bigint
    - Description: Rank of the customer based on delivered cart value.

- night_charge
    - Data type: real
    - Description: Additional charge for night-time delivery, if applicable.

- other_charges
    - Data type: real
    - Description: Any other charges applied to the order.

- polygon_type
    - Data type: varchar
    - Description: Type of polygon or delivery area associated with the order.

- etl_update_ts_ist
    - Data type: timestamp(6)
    - Description: Timestamp of the last ETL update in IST timezone.

- order_create_dt_ist
    - Data type: date
    - Description: Date of order creation in IST timezone, used as the partition key.

- is_surge_order
    - Data type: boolean
    - Description: Indicates if the order was placed during a surge period.

- surge_reason
    - Data type: varchar
    - Description: Reason for the surge, if applicable.

- is_batched_order
    - Data type: boolean
    - Description: Indicates if the order is part of a batch.

- is_gift_wrap_order
    - Data type: boolean
    - Description: Indicates if the order includes gift wrapping.

- additional_dwh_info
    - Data type: varchar
    - Description: Additional information for data warehouse purposes.

- app_flavor
    - Data type: varchar
    - Description: Specific version or flavor of the app used for the order.

- is_easy_return_order
    - Data type: boolean
    - Description: Indicates if the order is eligible for easy returns.

- karma_label
    - Data type: varchar
    - Description: Label associated with the customer's karma or loyalty status.

- karma_score
    - Data type: varchar
    - Description: Score associated with the customer's karma or loyalty status.

- is_b2b_order
    - Data type: boolean
    - Description: Indicates if the order is a business-to-business transaction.

- is_digital_voucher_order
    - Data type: boolean
    - Description: Indicates if the order includes digital vouchers.

- is_archived_order
    - Data type: boolean
    - Description: Indicates if the order has been archived.

- order_archived_dt_ist
    - Data type: date
    - Description: Date when the order was archived, if applicable.

- is_emergency_service_order
    - Data type: boolean
    - Description: Indicates if the order is for emergency services.