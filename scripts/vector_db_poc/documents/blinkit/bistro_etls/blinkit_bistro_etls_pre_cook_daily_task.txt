# Table: blinkit.bistro_etls.pre_cook_daily_task

### Description
The `blinkit.bistro_etls.pre_cook_daily_task` table appears to be used for managing and tracking daily preparation tasks in a culinary setting, likely within a restaurant or food service outlet. It stores detailed information about the preparation requirements for different sub-products or dishes, including projected quantities, preparation times, and adjustments based on sales data.

### Partitioning
This table is not partitioned. Queries on this table may not be optimized for performance unless indexed appropriately on frequently queried columns.

### Columns
- **outlet_id**
  - Data type: bigint
  - Description: The unique identifier for the outlet where the preparation task is scheduled.

- **projection_date**
  - Data type: varchar
  - Description: The date for which the preparation tasks are projected.

- **sub_pid**
  - Data type: bigint
  - Description: The identifier for the sub-product involved in the preparation task.

- **sub_name**
  - Data type: varchar
  - Description: The name of the sub-product involved in the preparation task.

- **transformation_type**
  - Data type: varchar
  - Description: Describes the type of transformation or preparation the sub-product will undergo.

- **prep_hour**
  - Data type: bigint
  - Description: The hour of the day when the preparation is scheduled to start.

- **prep_min**
  - Data type: varchar
  - Description: The minute of the hour when the preparation is scheduled to start.

- **sub_dish_quantity**
  - Data type: bigint
  - Description: The quantity of the sub-product to be prepared.

- **slot_sale**
  - Data type: double
  - Description: Sales data associated with the time slot for which the preparation is scheduled.

- **pre_prep_qty_intermediate_new**
  - Data type: bigint
  - Description: An intermediate calculation of the quantity to be pre-prepared, potentially before final adjustments.

- **ceiling_value**
  - Data type: varchar
  - Description: The maximum limit for the quantity to be prepared, used for capping the final prepared quantity.

- **floor_value**
  - Data type: varchar
  - Description: The minimum limit for the quantity to be prepared, ensuring a base level of availability.

- **pre_prep_qty**
  - Data type: bigint
  - Description: The initially calculated quantity of the sub-product to be pre-prepared.

- **pre_prep_qty_updated_with_floor**
  - Data type: bigint
  - Description: The quantity of the sub-product to be prepared, adjusted according to the floor value.

- **pre_prep_qty_updated_without_floor**
  - Data type: bigint
  - Description: The quantity of the sub-product to be prepared, calculated without considering the floor value.

- **updated_at**
  - Data type: varchar
  - Description: The timestamp indicating the last update made to the record.

### Potential JOIN Keys and Business-Critical Columns
- **outlet_id**: Potential JOIN key with other outlet-related tables.
- **sub_pid**: Could be used to JOIN with product tables to fetch more detailed product information.
- **projection_date**: Important for temporal analysis and could be used to link with sales or inventory data on the same date.

### Potential Relationships
- The `sub_pid` column suggests a relationship with a products table where detailed information about each sub-product is stored.
- The `outlet_id` might relate to an outlets table containing details about each location.

This documentation provides a comprehensive overview of the `blinkit.bistro_etls.pre_cook_daily_task` table, facilitating efficient data retrieval and analysis for business insights.