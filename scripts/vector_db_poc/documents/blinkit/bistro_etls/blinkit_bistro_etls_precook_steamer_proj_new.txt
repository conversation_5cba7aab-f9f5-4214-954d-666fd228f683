# Table: blinkit.bistro_etls.precook_steamer_proj_new

### Description
The `blinkit.bistro_etls.precook_steamer_proj_new` table appears to be used for storing projections related to product sales and performance metrics on a daily basis. It includes detailed information about sales quantities, growth rates, and projections that are critical for inventory and sales strategy planning. The data seems to be used for analyzing trends, making future sales projections, and understanding product performance across different time slots and dates.

### Partitioning
This table is partitioned on the keys `projection_date` and `slot`. These keys are CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **order_date**
  - Data type: date
  - Description: The date on which the order was placed.
  
- **slot**
  - Data type: bigint
  - Description: A time slot identifier for when the order was placed.
  
- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for the merchant.
  
- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant.
  
- **product_id**
  - Data type: bigint
  - Description: Unique identifier for the product.
  
- **product_name**
  - Data type: varchar
  - Description: Name of the product.
  
- **outlet_id**
  - Data type: bigint
  - Description: Unique identifier for the outlet where the product is sold.
  
- **dow**
  - Data type: bigint
  - Description: Day of the week represented as an integer.
  
- **gmv**
  - Data type: real
  - Description: Gross Merchandise Value associated with the product for the specific order.
  
- **quantity**
  - Data type: bigint
  - Description: Quantity of the product sold.
  
- **last_dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last same day of the week.
  
- **last_dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the last same day of the week was a deal of the day.
  
- **last_2dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the second last same day of the week.
  
- **last_2dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the second last same day of the week was a deal of the day.
  
- **last_3dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the third last same day of the week.
  
- **last_3dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the third last same day of the week was a deal of the day.
  
- **last_4dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the fourth last same day of the week.
  
- **last_4dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the fourth last same day of the week was a deal of the day.
  
- **last_5dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the fifth last same day of the week.
  
- **last_5dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the fifth last same day of the week was a deal of the day.
  
- **store_age_in_days**
  - Data type: varchar
  - Description: The age of the store in days.
  
- **opd**
  - Data type: bigint
  - Description: Orders per day.
  
- **projection_date**
  - Data type: date
  - Description: The date for which the sales projection is made.
  
- **dow_proj_date**
  - Data type: bigint
  - Description: Day of the week for the projection date.
  
- **preparation_type**
  - Data type: varchar
  - Description: Type of preparation for the product.
  
- **sub_product_id**
  - Data type: bigint
  - Description: Unique identifier for a sub-product.
  
- **sub_pid_name**
  - Data type: varchar
  - Description: Name of the sub-product.
  
- **pack_size**
  - Data type: varchar
  - Description: Size of the product package.
  
- **deliver_qty**
  - Data type: bigint
  - Description: Quantity of the product delivered.
  
- **last_4_non_dotd_similar_day_type_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold on the last four non-deal days of similar type.
  
- **last_non_dotd_dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last non-deal day of the week.
  
- **last_non_dotd_2dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the second last non-deal day of the week.
  
- **last_non_dotd_3dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the third last non-deal day of the week.
  
- **last_2_dow_growth_rate**
  - Data type: real
  - Description: Growth rate between the last two same days of the week.
  
- **last_dow_growth_rate**
  - Data type: real
  - Description: Growth rate since the last same day of the week.
  
- **row_median_growth**
  - Data type: real
  - Description: Median growth rate across rows.
  
- **last_2_dow_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold across the last two same days of the week.
  
- **last_3_dow_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold across the last three same days of the week.
  
- **last_3_dow_stdev_sold_quantity**
  - Data type: real
  - Description: Standard deviation of quantity sold across the last three same days of the week.
  
- **is_strictly_increasing**
  - Data type: boolean
  - Description: Flag indicating if the sales trend is strictly increasing.
  
- **is_strictly_decreasing**
  - Data type: boolean
  - Description: Flag indicating if the sales trend is strictly decreasing.
  
- **projected_increasing**
  - Data type: real
  - Description: Projected increase in sales.
  
- **increasing_cap1**
  - Data type: real
  - Description: First cap level for projected increasing sales.
  
- **increasing_cap2**
  - Data type: real
  - Description: Second cap level for projected increasing sales.
  
- **increasing_cap3**
  - Data type: real
  - Description: Third cap level for projected increasing sales.
  
- **projected_increasing_updated**
  - Data type: real
  - Description: Updated projection for increasing sales.
  
- **projected_decreasing**
  - Data type: real
  - Description: Projected decrease in sales.
  
- **projected_default**
  - Data type: real
  - Description: Default projected sales.
  
- **final_projected**
  - Data type: real
  - Description: Final sales projection.
  
- **final_projected_updated**
  - Data type: real
  - Description: Updated final sales projection.
  
- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update to the record.
  
- **cap_projected_strictly_increasing**
  - Data type: real
  - Description: Capped projection for strictly increasing sales.
  
- **new_final_projected_updated**
  - Data type: real
  - Description: Newly updated final projected sales.

### Potential JOIN Keys and Business-Critical Columns
- `product_id` and `sub_product_id` can be used to join with other product-related tables.
- `merchant_id` and `outlet_id` can be used to join with merchant or outlet tables.
- Business-critical columns include `gmv`, `quantity`, `projection_date`, and various growth and projection metrics which are essential for sales forecasting and strategic planning.