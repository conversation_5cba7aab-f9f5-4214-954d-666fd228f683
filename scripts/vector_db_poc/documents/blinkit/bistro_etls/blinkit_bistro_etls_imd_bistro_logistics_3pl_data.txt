# Table: blinkit.bistro_etls.imd_bistro_logistics_3pl_data

### Description
The table `imd_bistro_logistics_3pl_data` in the `blinkit.bistro_etls` schema captures detailed logistics information related to third-party logistics (3PL) partners handling deliveries. It includes timestamps of various delivery stages, geolocation data at the point of delivery, and identifiers linking orders, trips, and partners. This table is essential for tracking delivery performance and analyzing logistical efficiency.

### Partitioning
The table is partitioned on the column `insert_ds_ist`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **order_id**
  - Data type: bigint
  - Description: Unique identifier for each order.

- **update_ts**
  - Data type: timestamp(6)
  - Description: Timestamp indicating the last update made to the record.

- **partner_id**
  - Data type: varchar
  - Description: Identifier for the logistics partner responsible for the delivery.

- **gsp_marked_delivered_latitude**
  - Data type: double
  - Description: Latitude coordinate where the order was marked as delivered.

- **gsp_marked_delivered_longitude**
  - Data type: double
  - Description: Longitude coordinate where the order was marked as delivered.

- **order_delivered_ts_utc**
  - Data type: timestamp(6)
  - Description: UTC timestamp when the order was delivered.

- **trip_id**
  - Data type: varchar
  - Description: Identifier for the delivery trip associated with the order.

- **partner_assigned_ts_utc**
  - Data type: timestamp(6)
  - Description: UTC timestamp when the logistics partner was assigned to the order.

- **is_marked_delivery_failed**
  - Data type: boolean
  - Description: Indicates whether the delivery was marked as failed.

- **reached_doorstep_ts_utc**
  - Data type: timestamp(6)
  - Description: UTC timestamp when the delivery personnel reached the customer's doorstep.

- **insert_ds_ist**
  - Data type: varchar
  - Description: Partition key used to segment data by the date of record insertion in IST timezone.

- **accounting_distance**
  - Data type: double
  - Description: Recorded distance traveled for the delivery.

### Potential JOIN Keys and Relationships
- **order_id**: Likely a primary key that can be used to join with other order-related tables such as order details or customer information.
- **trip_id**: Could be used to join with vehicle tracking or trip management tables.
- **partner_id**: May join with partner profile tables to fetch additional details about the logistics partners.

This structured documentation provides a clear overview of the `imd_bistro_logistics_3pl_data` table, ensuring efficient data management and query optimization through the use of partition keys and potential relational links.