# Table: blinkit.bistro_etls.precook_egg_daily_projection_log_new

### Description
The table `blinkit.bistro_etls.precook_egg_daily_projection_log_new` is designed to store daily projections and historical sales data for products, specifically focusing on the food and beverage sector. It appears to be used for analyzing and forecasting product demand, tracking sales performance over different days of the week, and adjusting projections based on past trends.

### Partitioning
The table is partitioned on the keys `projection_date` and `slot`. These keys are CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

### Columns
- **order_date**
  - Data type: date
  - Description: The date on which the order was placed.
  
- **slot**
  - Data type: bigint
  - Description: A time slot identifier for when the order was placed or projected.
  
- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for the merchant.
  
- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant.
  
- **product_id**
  - Data type: bigint
  - Description: Unique identifier for the product.
  
- **product_name**
  - Data type: varchar
  - Description: Name of the product.
  
- **outlet_id**
  - Data type: bigint
  - Description: Unique identifier for the outlet where the product is sold.
  
- **dow**
  - Data type: bigint
  - Description: Day of the week represented as an integer.
  
- **gmv**
  - Data type: real
  - Description: Gross Merchandise Value associated with the product for the specific day.
  
- **quantity**
  - Data type: bigint
  - Description: Quantity of the product sold.
  
- **last_dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last similar day of the week.
  
- **last_dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the last similar day of the week was a deal of the day.
  
- **last_2dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold two similar days of the week ago.
  
- **last_2dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if two similar days of the week ago was a deal of the day.
  
- **last_3dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold three similar days of the week ago.
  
- **last_3dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if three similar days of the week ago was a deal of the day.
  
- **last_4dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold four similar days of the week ago.
  
- **last_4dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if four similar days of the week ago was a deal of the day.
  
- **last_5dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold five similar days of the week ago.
  
- **last_5dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if five similar days of the week ago was a deal of the day.
  
- **store_age_in_days**
  - Data type: varchar
  - Description: Age of the store in days.
  
- **opd**
  - Data type: bigint
  - Description: Orders per day.
  
- **projection_date**
  - Data type: date
  - Description: The date for which sales are being projected.
  
- **dow_proj_date**
  - Data type: bigint
  - Description: Day of the week for the projection date.
  
- **preparation_type**
  - Data type: varchar
  - Description: Type of preparation required for the product.
  
- **sub_product_id**
  - Data type: bigint
  - Description: Identifier for a sub-category of the product.
  
- **sub_pid_name**
  - Data type: varchar
  - Description: Name of the sub-product.
  
- **pack_size**
  - Data type: varchar
  - Description: Packaging size of the product.
  
- **deliver_qty**
  - Data type: bigint
  - Description: Quantity of the product to be delivered.
  
- **last_4_non_dotd_similar_day_type_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold on the last four non-deal days similar to the current day type.
  
- **last_non_dotd_dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last non-deal day of the week.
  
- **last_non_dotd_2dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last two non-deal days of the week.
  
- **last_non_dotd_3dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last three non-deal days of the week.
  
- **last_2_dow_growth_rate**
  - Data type: real
  - Description: Growth rate in sales from the last two similar days of the week.
  
- **last_dow_growth_rate**
  - Data type: real
  - Description: Growth rate in sales from the last similar day of the week.
  
- **row_median_growth**
  - Data type: real
  - Description: Median growth rate across rows.
  
- **last_2_dow_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold across the last two similar days of the week.
  
- **last_3_dow_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold across the last three similar days of the week.
  
- **last_3_dow_stdev_sold_quantity**
  - Data type: real
  - Description: Standard deviation of quantity sold across the last three similar days of the week.
  
- **is_strictly_increasing**
  - Data type: boolean
  - Description: Indicates whether the sales trend is strictly increasing.
  
- **is_strictly_decreasing**
  - Data type: boolean
  - Description: Indicates whether the sales trend is strictly decreasing.
  
- **projected_increasing**
  - Data type: real
  - Description: Projected increase in sales.
  
- **increasing_cap1**
  - Data type: real
  - Description: First cap value for increasing projections.
  
- **increasing_cap2**
  - Data type: real
  - Description: Second cap value for increasing projections.
  
- **increasing_cap3**
  - Data type: real
  - Description: Third cap value for increasing projections.
  
- **projected_increasing_updated**
  - Data type: real
  - Description: Updated projection for increasing sales.
  
- **projected_decreasing**
  - Data type: real
  - Description: Projected decrease in sales.
  
- **projected_default**
  - Data type: real
  - Description: Default projection value.
  
- **final_projected**
  - Data type: real
  - Description: Final sales projection.
  
- **final_projected_updated**
  - Data type: real
  - Description: Updated final sales projection.
  
- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update to the record.
  
- **cap_projected_strictly_increasing**
  - Data type: real
  - Description: Capped projection for strictly increasing sales trends.
  
- **new_final_projected_updated**
  - Data type: real
  - Description: Newly updated final projected sales.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `merchant_id`, `product_id`, `outlet_id` could be used to join with other merchant, product, or outlet related tables.
- **Business-Critical Columns**: `projection_date`, `product_id`, `quantity`, `gmv`, `final_projected`, and `updated_at_ts` are crucial for business analysis and forecasting.

### Potential Relationships
- **product_id** could relate to a `products` table containing detailed product information.
- **merchant_id** could relate to a `merchants` table with merchant details.
- **outlet_id** could relate to an `outlets` table detailing outlet locations and information.