# Table: blinkit.bistro_etls.thaw_item_wise_dow_daily

### Description
The `blinkit.bistro_etls.thaw_item_wise_dow_daily` table stores daily records of thawed items prepared and sold across various merchant outlets. It includes details about the quantities procured, sold, and sales trends over the week, along with merchant and outlet identification.

### Partitioning
The table is partitioned on the `updated_at_ts` column. This column is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **date_**
    - Data type: date
    - Description: The date on which the records were logged.

- **slot**
    - Data type: varchar
    - Description: Time slot of the day for which the data is recorded.

- **merchant_id**
    - Data type: integer
    - Description: Unique identifier for the merchant.

- **outlet_id**
    - Data type: integer
    - Description: Unique identifier for the outlet where the items are sold or prepared.

- **merchant_name**
    - Data type: varchar
    - Description: Name of the merchant.

- **prepared_product_id**
    - Data type: integer
    - Description: Unique identifier for the prepared product.

- **thawed_name**
    - Data type: varchar
    - Description: Name of the thawed product.

- **procured_quantity**
    - Data type: integer
    - Description: Quantity of the product procured for sale.

- **sold_quantity**
    - Data type: integer
    - Description: Quantity of the product sold.

- **last_dow_sold_quantity**
    - Data type: integer
    - Description: Quantity sold on the last day of the week.

- **last_2dow_sold_quantity**
    - Data type: integer
    - Description: Quantity sold two days prior to the last day of the week.

- **last_3dow_sold_quantity**
    - Data type: integer
    - Description: Quantity sold three days prior to the last day of the week.

- **last_4dow_sold_quantity**
    - Data type: integer
    - Description: Quantity sold four days prior to the last day of the week.

- **dow**
    - Data type: integer
    - Description: Day of the week represented as an integer (1 for Monday, 7 for Sunday).

- **updated_at_ts**
    - Data type: timestamp(6)
    - Description: Timestamp indicating the last update time of the record.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id** and **outlet_id** can be used as JOIN keys to link with other tables containing merchant or outlet details.
- **prepared_product_id** might be used to join with product tables for detailed product information.
- Business-critical columns include **procured_quantity**, **sold_quantity**, and the various `last_xdow_sold_quantity` columns as they directly relate to sales performance and inventory management.

### Potential Relationships to Other Tables
- The table could potentially relate to a `products` table via **prepared_product_id**.
- It could also relate to `merchants` or `outlets` tables via **merchant_id** and **outlet_id** respectively.