# Table: blinkit.bistro_etls.thaw_item_wise_bumps_v1

### Documentation for Table `blinkit.bistro_etls.thaw_item_wise_bumps_v1`

#### Description
This table appears to store detailed analytics about product sales and inventory metrics at different outlets over the past week. It includes statistical data on potential and actual sales quantities, deviations in sales, and updates on product performance. This information is likely used for inventory management, sales forecasting, and business intelligence purposes.

#### Partitioning
The table is partitioned by the column `updated_at_ts`. This is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

#### Columns
- **outlet_id**
  - Data type: integer
  - Description: Identifier for the outlet where the sales data is recorded.
  
- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant associated with the outlet.
  
- **slot**
  - Data type: varchar
  - Description: Time slot for which the data is applicable, possibly indicating specific business hours.
  
- **prepared_product_id**
  - Data type: integer
  - Description: Unique identifier for the prepared product being analyzed.
  
- **thawed_name**
  - Data type: varchar
  - Description: Name of the product after preparation or thawing process.
  
- **median_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: Median of the potential sale quantity of the product over the last 7 days.
  
- **avg_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: Average potential sale quantity of the product over the last 7 days.
  
- **max_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: Maximum potential sale quantity recorded for the product over the last 7 days.
  
- **min_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: Minimum potential sale quantity recorded for the product over the last 7 days.
  
- **median_quantity_sold_last_7d**
  - Data type: double
  - Description: Median of the actual quantity sold of the product over the last 7 days.
  
- **avg_quantity_sold_last_7d**
  - Data type: double
  - Description: Average actual quantity sold of the product over the last 7 days.
  
- **max_quantity_sold_last_7d**
  - Data type: double
  - Description: Maximum actual quantity sold of the product over the last 7 days.
  
- **min_quantity_sold_last_7d**
  - Data type: double
  - Description: Minimum actual quantity sold of the product over the last 7 days.
  
- **deviation_days**
  - Data type: double
  - Description: Number of days the actual sales deviated from predicted sales.
  
- **avg_deviation_positive**
  - Data type: double
  - Description: Average positive deviation from the expected sales figures.
  
- **custom_weighted_deviation**
  - Data type: double
  - Description: Custom calculated weighted deviation for the product's sales performance.
  
- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp marking the last update to the record, used for partitioning.
  
- **date_**
  - Data type: date
  - Description: The specific date to which the data pertains.
  
- **std_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: Standard deviation of the potential sale quantity over the last 7 days.
  
- **std_quantity_sold_last_7d**
  - Data type: double
  - Description: Standard deviation of the actual quantity sold over the last 7 days.

#### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `outlet_id`, `prepared_product_id` could be used to join with other tables that contain outlet or product details.
- **Business-Critical Columns**: `median_quantity_sold_last_7d`, `avg_quantity_sold_last_7d`, `updated_at_ts` are critical for analyzing sales performance and inventory management.

#### Potential Relationships
- The `prepared_product_id` might relate to a `products` table containing detailed product information.
- The `outlet_id` could link to an `outlets` table that stores information about each outlet location and characteristics.