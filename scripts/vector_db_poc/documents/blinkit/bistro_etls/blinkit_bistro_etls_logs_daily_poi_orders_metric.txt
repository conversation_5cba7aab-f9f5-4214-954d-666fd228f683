# Table: blinkit.bistro_etls.logs_daily_poi_orders_metric

### Description
The table `blinkit.bistro_etls.logs_daily_poi_orders_metric` is designed to store daily metrics related to point of interest (POI) orders. It captures detailed information about orders placed, operational hours, and performance metrics of walkers (delivery personnel) at each POI. This data is crucial for analyzing the efficiency and effectiveness of order handling and delivery operations at various POIs.

### Partitioning
The table is partitioned on the column `dt`. This column is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **dt**
  - Data type: varchar
  - Description: The date for which the metrics are recorded, used as a partition key.
  
- **week**
  - Data type: varchar
  - Description: The week of the year for which the data is aggregated.
  
- **poi_id**
  - Data type: integer
  - Description: The unique identifier for the point of interest (POI).
  
- **poi_name**
  - Data type: varchar
  - Description: The name of the point of interest (POI).
  
- **placed_orders**
  - Data type: bigint
  - Description: The total number of orders placed at the POI on the given date.
  
- **placed_orders_ops_hours**
  - Data type: bigint
  - Description: The number of orders placed during operational hours at the POI.
  
- **walker_orders**
  - Data type: bigint
  - Description: The number of orders delivered by walkers on the given date.
  
- **new_poi_users**
  - Data type: bigint
  - Description: The count of new users who placed orders at the POI.
  
- **rhs_with_walker**
  - Data type: double
  - Description: The average ready-to-handover time for orders with walker involvement.
  
- **scan_to_pick**
  - Data type: double
  - Description: The average time from order scanning to pick-up by a walker.
  
- **pick_to_deliver**
  - Data type: double
  - Description: The average time from pick-up to delivery completion by a walker.
  
- **walker_chs**
  - Data type: double
  - Description: The cumulative handling score for walkers at the POI.
  
- **non_walker_chs**
  - Data type: double
  - Description: The cumulative handling score for non-walkers at the POI.
  
- **total_login_hrs**
  - Data type: double
  - Description: The total hours logged by walkers at the POI on the given date.
  
- **walker_util**
  - Data type: double
  - Description: The utilization percentage of walkers based on logged hours and active time.
  
- **total_walkers**
  - Data type: bigint
  - Description: The total number of walkers who were active at the POI on the given date.
  
- **dp_time_saved_with_walker**
  - Data type: double
  - Description: The total delivery preparation time saved due to walker involvement.
  
- **avg_login_hrs**
  - Data type: double
  - Description: The average hours logged per walker at the POI on the given date.

### Potential JOIN Keys and Business-Critical Columns
- `poi_id` could be used to join with other tables that contain POI-specific data, such as POI details or geographic information.
- Business-critical columns include `placed_orders`, `walker_orders`, and `walker_util`, which are essential for analyzing operational efficiency and service quality at POIs.

### Potential Relationships
- The `poi_id` column suggests a relationship to other tables that contain detailed information about POIs, such as addresses or additional operational metrics.
- Metrics like `walker_orders` and `total_walkers` could relate to human resource and staffing tables for deeper analysis of workforce utilization and planning.