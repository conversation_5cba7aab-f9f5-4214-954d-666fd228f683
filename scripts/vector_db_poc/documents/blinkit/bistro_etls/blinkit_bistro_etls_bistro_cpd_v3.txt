# Table: blinkit.bistro_etls.bistro_cpd_v3

### Data Dictionary for Table: `blinkit.bistro_etls.bistro_cpd_v3`

#### Description
The `blinkit.bistro_etls.bistro_cpd_v3` table appears to be used for storing projected and actual data related to items and supplies for various outlets and merchants. It likely serves as a central dataset for analyzing supply chain efficiency, inventory management, and order fulfillment metrics.

#### Partitioning
- **Partition Key:** `projection_date`
  - The use of `projection_date` as a partition key is CRITICAL for query performance. Queries against this table MUST always include `projection_date` in the WHERE clause to ensure efficient data retrieval.

#### Columns
- **projection_date**
  - Data type: date
  - Description: The date for which the data projections are made, used as a partition key.

- **creation_date**
  - Data type: date
  - Description: The date on which the record was created in the database.

- **outlet_id**
  - Data type: integer
  - Description: Identifier for the outlet; can be used to join with outlet-related tables.

- **merchant_id**
  - Data type: integer
  - Description: Identifier for the merchant; can be used to join with merchant-related tables.

- **final_item_id**
  - Data type: integer
  - Description: Identifier for the final item; potentially links to item details in another table.

- **final_supply_id**
  - Data type: integer
  - Description: Identifier for the supply; potentially links to supply details in another table.

- **final_supply_name**
  - Data type: varchar
  - Description: The name of the supply; provides human-readable context for the supply ID.

- **ord_qty_v1**
  - Data type: integer
  - Description: The quantity of the item ordered in version 1 of the data; used for tracking and analysis.

#### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys:**
  - `outlet_id`: Could be used to join with other tables containing outlet-specific information.
  - `merchant_id`: Could be used to join with other tables containing merchant-specific information.
  - `final_item_id`: Could be used to join with item detail tables.
  - `final_supply_id`: Could be used to join with supply detail tables.

- **Business-Critical Columns:**
  - `projection_date`: Essential for temporal analysis and partitioning.
  - `ord_qty_v1`: Important for inventory and order quantity analysis.

#### Potential Relationships to Other Tables
- The `final_item_id` and `final_supply_id` suggest that there might be corresponding tables detailing items and supplies, respectively, which could be linked to analyze detailed characteristics or performance metrics of these entities.