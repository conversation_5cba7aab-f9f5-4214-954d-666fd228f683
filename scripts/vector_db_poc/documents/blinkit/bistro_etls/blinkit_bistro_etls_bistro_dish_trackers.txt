# Table: blinkit.bistro_etls.bistro_dish_trackers

### Description
The `blinkit.bistro_etls.bistro_dish_trackers` table is designed to track and analyze the performance of various dishes offered by merchants in different cities over time. It aggregates data on a weekly basis, providing insights into sales volume, product popularity, and financial metrics such as selling price and retained margins. This table is essential for understanding market trends, optimizing product offerings, and strategic decision-making in the food service industry.

### Partitioning
The table is partitioned by the `week_` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **week_**
  - Data type: date
  - Description: The week for which the data is aggregated.

- **city_name**
  - Data type: varchar
  - Description: The name of the city where the merchant is located.

- **merchant_name**
  - Data type: varchar
  - Description: The name of the merchant offering the dish.

- **merchant_id**
  - Data type: bigint
  - Description: A unique identifier for the merchant.

- **product_id**
  - Data type: bigint
  - Description: A unique identifier for the product (dish).

- **product_name**
  - Data type: varchar
  - Description: The name of the product (dish).

- **l2_category**
  - Data type: varchar
  - Description: The second-level category classification of the product.

- **total_orders**
  - Data type: bigint
  - Description: The total number of orders placed for all products by the merchant in the specified week.

- **product_orders**
  - Data type: bigint
  - Description: The number of orders specifically for this product in the specified week.

- **category_orders**
  - Data type: bigint
  - Description: The number of orders for all products within the same category as this product in the specified week.

- **avg_rating**
  - Data type: double
  - Description: The average customer rating for the product.

- **selling_price**
  - Data type: double
  - Description: The average selling price of the product during the specified week.

- **retained_margin**
  - Data type: double
  - Description: The average retained margin per product sold.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `merchant_id`, `product_id`
  - These keys can be used to join this table with other merchant or product-related tables to enrich the data or to perform detailed analyses.
- **Business-Critical Columns**: `total_orders`, `product_orders`, `avg_rating`, `selling_price`, `retained_margin`
  - These columns are crucial for analyzing the performance and profitability of products and merchants.

### Potential Relationships
- This table likely has relationships with other tables containing detailed merchant information (`merchant_id`) and product details (`product_id`). These relationships can be explored to gain deeper insights into the performance metrics across different dimensions.