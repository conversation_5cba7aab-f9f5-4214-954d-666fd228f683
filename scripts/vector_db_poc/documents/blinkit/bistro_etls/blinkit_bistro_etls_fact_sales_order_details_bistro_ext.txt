# Table: blinkit.bistro_etls.fact_sales_order_details_bistro_ext

### Description
The table `blinkit.bistro_etls.fact_sales_order_details_bistro_ext` is designed to store detailed transactional data about sales orders. It includes information on various charges applied to the orders, timestamps related to order creation and cancellation, and details concerning the cancellation process. This table is essential for financial reporting, order tracking, and analyzing customer behavior regarding additional charges.

### Partitioning
The table is partitioned on the column `order_create_dt_ist`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval and query execution.

### Columns
- **order_id**
  - Data type: bigint
  - Description: Unique identifier for each order, likely used as a primary key.
  
- **order_create_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp indicating when the order was created, in IST timezone.
  
- **additional_charges_amount**
  - Data type: double
  - Description: Total amount of additional charges applied to the order.
  
- **tip_amount**
  - Data type: double
  - Description: Amount of tip given by the customer.
  
- **eco_friendly_packaging_cost**
  - Data type: double
  - Description: Cost associated with using eco-friendly packaging for the order.
  
- **night_charges**
  - Data type: double
  - Description: Additional charges applied to orders made during nighttime hours.
  
- **convenience_charge**
  - Data type: double
  - Description: Charge added for the convenience of service or delivery.
  
- **add_charge_name_null**
  - Data type: double
  - Description: Placeholder for future use or currently unused additional charge.
  
- **feeding_india_donation**
  - Data type: double
  - Description: Amount donated to the 'Feeding India' initiative through this order.
  
- **gift_packing_charge**
  - Data type: double
  - Description: Charge applied for gift packing the order.
  
- **small_cart_charge**
  - Data type: double
  - Description: Charge applied when the order does not meet a minimum cart value.
  
- **festival_charge**
  - Data type: double
  - Description: Special charges applied during festival seasons.
  
- **diff_additional_charges**
  - Data type: double
  - Description: Differential amount calculated from additional charges.
  
- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp indicating when the data was captured or processed in the ETL system, in IST timezone.
  
- **order_create_dt_ist**
  - Data type: date
  - Description: Date when the order was created, used for partitioning the table.
  
- **order_cancel_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp indicating when the order was cancelled, in IST timezone.
  
- **cancellation_initiator_role**
  - Data type: varchar
  - Description: Role of the person or system that initiated the order cancellation.
  
- **cancellation_source**
  - Data type: varchar
  - Description: Source from which the cancellation was initiated.
  
- **final_state_before_cancellation**
  - Data type: varchar
  - Description: The final state of the order before it was cancelled.
  
- **cancellation_reason**
  - Data type: varchar
  - Description: Reason provided for the order cancellation.

### Potential Relationships and JOIN Keys
- The `order_id` column is a potential JOIN key with other tables that contain order-related data, such as shipping details, customer information, or product details.
- The `order_create_dt_ist` can be used to join with other date-partitioned tables for comprehensive time-based analysis.

### Business-Critical Columns
- `order_id`: Essential for uniquely identifying and linking orders across the database.
- `additional_charges_amount`, `tip_amount`, and other charge-related columns: Crucial for financial analysis and revenue calculation.
- `order_create_ts_ist` and `order_cancel_ts_ist`: Important for time-series analysis of order trends and cancellation patterns.