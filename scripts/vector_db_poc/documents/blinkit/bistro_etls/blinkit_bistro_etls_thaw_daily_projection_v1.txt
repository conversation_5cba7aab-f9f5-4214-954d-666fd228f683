# Table: blinkit.bistro_etls.thaw_daily_projection_v1

### Description
The `blinkit.bistro_etls.thaw_daily_projection_v1` table is designed to store daily projections for product quantities that need to be thawed by merchants. It includes details about the products, the quantity projected, adjustments, and the maximum thaw amounts. This information is critical for inventory and supply chain management, ensuring that products are available for sale in the required condition.

### Partitioning
The table is partitioned on the `updated_at_ts` column. This is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **projection_date**
  - Data type: date
  - Description: The date for which the product quantity projection is made.
  
- **slot**
  - Data type: bigint
  - Description: A time slot identifier for when the projection applies, possibly representing different shifts or parts of the day.
  
- **merchant_id**
  - Data type: bigint
  - Description: The unique identifier of the merchant for whom the projection is made.
  
- **merchant_name**
  - Data type: varchar
  - Description: The name of the merchant associated with the given merchant_id.
  
- **sub_product_id**
  - Data type: bigint
  - Description: The identifier for a specific sub-product or variant for which the projection is made.
  
- **sub_pid_name**
  - Data type: varchar
  - Description: The name of the sub-product or variant corresponding to sub_product_id.
  
- **pack_size**
  - Data type: real
  - Description: The size of the product package, which could influence the thawing process and quantities.
  
- **projected_qty**
  - Data type: real
  - Description: The quantity of the product projected to be needed for the specified date and slot.
  
- **to_thaw_max**
  - Data type: real
  - Description: The maximum quantity that should be thawed for the specified product to meet projected demands.
  
- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: The timestamp when the record was last updated; used for partitioning the table.
  
- **oos_adjustment_value**
  - Data type: real
  - Description: Adjustment value for out-of-stock situations, modifying the projected quantity based on current stock levels.
  
- **old_projected_qty**
  - Data type: real
  - Description: The previous projection quantity, allowing for comparison and trend analysis over time.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id**: Can be used to join with other merchant-related tables to fetch additional merchant details.
- **sub_product_id**: Likely a key to join with product tables for detailed product information.
- **projection_date** and **updated_at_ts**: Important for time-series analysis and historical data tracking.

### Potential Relationships
- The `merchant_id` could be used to join with a `merchants` table to retrieve more detailed merchant information.
- The `sub_product_id` might link to a `products` or `sub_products` table for detailed descriptions and specifications of the products.

This structured and concise documentation ensures that users can efficiently query and understand the data stored in the `blinkit.bistro_etls.thaw_daily_projection_v1` table.