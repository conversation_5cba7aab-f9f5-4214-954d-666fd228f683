# Table: blinkit.bistro_etls.supply_consumption_mapping

### Description
The `blinkit.bistro_etls.supply_consumption_mapping` table is designed to track the consumption of supplies at various outlets over specific periods. It records details about the quantity of each supply consumed, the duration of consumption, and the stock days. This information is crucial for managing inventory, analyzing supply usage patterns, and planning future procurement.

### Partitioning
The table is partitioned on the `outlet_id` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

### Columns
- **outlet_id**
  - Data type: integer
  - Description: The unique identifier for an outlet. This is a critical column for partitioning and is likely used to join with other tables that contain outlet-specific data.

- **converted_id**
  - Data type: integer
  - Description: Likely a transformed or derived identifier used internally for reference or additional mappings.

- **supply_id**
  - Data type: integer
  - Description: The unique identifier for a supply item. This column is essential for linking to detailed supply information in related tables.

- **consumption_start_dt**
  - Data type: timestamp(6)
  - Description: The start date and time for the consumption period of a supply.

- **consumption_end_dt**
  - Data type: timestamp(6)
  - Description: The end date and time for the consumption period of a supply.

- **quantity_consumed**
  - Data type: real
  - Description: The amount of supply consumed during the specified period. This is a key metric for inventory and supply chain analysis.

- **days_in_stock**
  - Data type: integer
  - Description: The number of days the supply was in stock during the consumption period.

- **insert_dt_ist**
  - Data type: timestamp(6)
  - Description: The timestamp when the record was inserted into the database, in Indian Standard Time (IST).

### Potential JOINs and Relationships
- The `outlet_id` can be used to join this table with other outlet-related tables to gather more comprehensive data about each location.
- The `supply_id` might be used to join with a `supplies` table or similar, which would provide details about the supplies such as name, type, supplier, etc.

### Business-Critical Columns
- **outlet_id**: Essential for partitioning and data segmentation by outlet.
- **supply_id**: Crucial for tracking and analyzing specific supplies.
- **quantity_consumed**: Important for operational and financial metrics related to supply management.
- **consumption_start_dt** and **consumption_end_dt**: Important for time-based analysis of supply consumption.