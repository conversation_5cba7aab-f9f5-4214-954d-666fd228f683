# Table: blinkit.bistro_etls.bistro_dish_tracker_weekly

### Description
The `blinkit.bistro_etls.bistro_dish_tracker_weekly` table is designed to track weekly performance metrics of dishes offered by merchants in various cities. It aggregates data such as order counts, product details, sales performance, and customer ratings on a weekly basis. This table is essential for analyzing trends, merchant performance, and product popularity over time.

### Partitioning
The table is partitioned on the `week_` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **week_**
  - Data type: date
  - Description: The week for which the data is aggregated.

- **city_name**
  - Data type: varchar
  - Description: The name of the city where the merchant is located.

- **merchant_name**
  - Data type: varchar
  - Description: The name of the merchant.

- **merchant_id**
  - Data type: bigint
  - Description: A unique identifier for the merchant.

- **product_id**
  - Data type: bigint
  - Description: A unique identifier for the product.

- **product_name**
  - Data type: varchar
  - Description: The name of the product.

- **l2_category**
  - Data type: varchar
  - Description: The second-level category to which the product belongs.

- **total_orders**
  - Data type: bigint
  - Description: The total number of orders placed for all products from the merchant in the specified week.

- **product_orders**
  - Data type: bigint
  - Description: The number of orders specifically for this product during the specified week.

- **cpd**
  - Data type: decimal(22,2)
  - Description: Cost per dish, representing the cost associated with each order of the product.

- **category_orders**
  - Data type: bigint
  - Description: The total number of orders for all products within the same category.

- **avg_rating**
  - Data type: double
  - Description: The average customer rating for the product.

- **selling_price**
  - Data type: double
  - Description: The selling price of the product.

- **retained_margin**
  - Data type: double
  - Description: The profit margin retained after accounting for the cost of goods sold.

- **city_orders**
  - Data type: bigint
  - Description: The total number of orders placed in the city across all merchants.

- **city_category_orders**
  - Data type: bigint
  - Description: The total number of orders for this category across all merchants in the city.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id**: Can be used to join with other merchant-related tables to fetch additional merchant details.
- **product_id**: Essential for joining with product inventory or product details tables.
- **city_name**: Useful for geographical analysis and can be joined with city demographics or regional sales data tables.

### Inferred Relationships
- The `product_id` column suggests a relationship with a products table where detailed product information is stored.
- The `merchant_id` could link to a merchant details table containing more information about the merchants.
- The `city_name` could be used to join with city or regional tables for broader geographic analysis.

This documentation provides a comprehensive overview of the `blinkit.bistro_etls.bistro_dish_tracker_weekly` table, ensuring efficient use and integration within the broader data ecosystem.