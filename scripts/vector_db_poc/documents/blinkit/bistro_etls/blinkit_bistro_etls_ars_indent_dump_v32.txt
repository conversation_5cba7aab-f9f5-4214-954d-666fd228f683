# Table: blinkit.bistro_etls.ars_indent_dump_v32

### Description
The `blinkit.bistro_etls.ars_indent_dump_v32` table appears to be a comprehensive inventory and demand tracking system for a business's outlets. It stores detailed records about inventory levels, demand quantities, purchase orders, and product details across various outlets, both backend and frontend. This data is crucial for managing supply chain operations, optimizing stock levels, and forecasting demand.

### Partitioning
The table is partitioned on the `sto_date` column. This is CRITICAL for query performance and the `sto_date` MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **sto_date**
  - Data type: date
  - Description: The storage date, used as a partition key, indicating the date of the record.

- **run_id**
  - Data type: varchar
  - Description: A unique identifier for the data processing run.

- **backend_outlet_id**
  - Data type: integer
  - Description: Identifier for the backend outlet.

- **backend_outlet_name**
  - Data type: varchar
  - Description: The name of the backend outlet.

- **frontend_outlet_id**
  - Data type: integer
  - Description: Identifier for the frontend outlet.

- **outlet_name**
  - Data type: varchar
  - Description: The name of the frontend outlet.

- **item_id**
  - Data type: integer
  - Description: Unique identifier for an item, potentially a JOIN key for item-related tables.

- **item_name**
  - Data type: varchar
  - Description: The name of the item.

- **wh_onshelf_inv**
  - Data type: integer
  - Description: Warehouse on-shelf inventory count for the item.

- **cum_demand_qty**
  - Data type: double
  - Description: Cumulative demand quantity for the item.

- **initial_demand_qty**
  - Data type: double
  - Description: Initial demand quantity recorded for the item.

- **pod_inv**
  - Data type: double
  - Description: Inventory level at the point of distribution.

- **open_po**
  - Data type: double
  - Description: Open purchase order quantity.

- **cycel_start_inv**
  - Data type: double
  - Description: Inventory at the start of the cycle.

- **max_defined_qty**
  - Data type: double
  - Description: Maximum defined quantity for the item.

- **cpd**
  - Data type: integer
  - Description: Critical processing detail, possibly a key performance indicator.

- **po_qty**
  - Data type: double
  - Description: Quantity in purchase order.

- **product_type**
  - Data type: varchar
  - Description: Type or category of the product.

- **converted_id**
  - Data type: bigint
  - Description: A converted identifier for system use.

- **suppy_id**
  - Data type: integer
  - Description: Supply identifier, potentially linking to supplier details.

- **hp_prod_num**
  - Data type: integer
  - Description: High-priority product number.

- **conv_factor**
  - Data type: decimal(10,2)
  - Description: Conversion factor used for inventory calculations.

- **base_item_id**
  - Data type: integer
  - Description: Base item identifier, potentially a JOIN key for item-related tables.

- **base_conv_factor**
  - Data type: decimal(10,2)
  - Description: Base conversion factor for inventory calculations.

- **convted_wh_inv**
  - Data type: decimal(20,2)
  - Description: Converted warehouse inventory.

- **convted_cum_demand**
  - Data type: double
  - Description: Converted cumulative demand.

- **wh_inv**
  - Data type: decimal(23,2)
  - Description: Warehouse inventory.

- **cum_demand**
  - Data type: double
  - Description: Cumulative demand for the item.

- **base_po_qty**
  - Data type: double
  - Description: Base quantity in purchase order.

- **shortfall_flag**
  - Data type: varchar
  - Description: Flag indicating if there is a shortfall.

- **shortfall_qty**
  - Data type: double
  - Description: Quantity of shortfall.

- **picked_quantity**
  - Data type: double
  - Description: Quantity of items picked for orders.

- **updated_at**
  - Data type: timestamp(6) with time zone
  - Description: Timestamp of the last update to the record.

- **ars_mode**
  - Data type: varchar
  - Description: Mode of the Automated Replenishment System.

### Potential Relationships
- `item_id` and `base_item_id` could be used to join with item detail tables.
- `backend_outlet_id` and `frontend_outlet_id` might link to outlet-specific tables.

### Business-Critical Columns
- `item_id`, `backend_outlet_id`, `frontend_outlet_id`, `cum_demand_qty`, and `wh_onshelf_inv` are critical for inventory and demand analysis.