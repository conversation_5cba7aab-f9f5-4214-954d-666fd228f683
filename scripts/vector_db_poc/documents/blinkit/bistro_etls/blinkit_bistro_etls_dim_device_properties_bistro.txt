# Table: blinkit.bistro_etls.dim_device_properties_bistro

### Description
The `blinkit.bistro_etls.dim_device_properties_bistro` table stores detailed properties and activity timestamps of devices used in accessing services or applications. This table is primarily used for analyzing device engagement patterns, tracking first and last seen timestamps, and understanding geographical distribution of device usage.

### Partitioning
This table is not partitioned.

### Columns
- **device_uuid**
  - Data type: varchar
  - Description: Unique identifier for the device.

- **first_seen_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp indicating when the device was first seen, in IST timezone.

- **first_seen_date**
  - Data type: date
  - Description: Date when the device was first seen.

- **first_seen_date_hr**
  - Data type: timestamp(6)
  - Description: Timestamp including the hour when the device was first seen.

- **first_seen_date_hr_minute**
  - Data type: timestamp(6)
  - Description: Timestamp including the hour and minute when the device was first seen.

- **day_of_week**
  - Data type: varchar
  - Description: Day of the week when the device was first seen.

- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the ETL process snapshot, in IST timezone.

- **last_seen_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp indicating when the device was last seen, in IST timezone.

- **first_seen_city_name**
  - Data type: varchar
  - Description: Name of the city where the device was first seen.

- **last_seen_city_name**
  - Data type: varchar
  - Description: Name of the city where the device was last seen.

- **device_manufacturer**
  - Data type: varchar
  - Description: Manufacturer of the device.

- **device_model**
  - Data type: varchar
  - Description: Model of the device.

- **platform**
  - Data type: varchar
  - Description: Operating platform of the device (e.g., Android, iOS).

- **advertising_id**
  - Data type: varchar
  - Description: Advertising identifier associated with the device.

### Potential JOIN Keys and Business-Critical Columns
- **device_uuid**: This is a potential JOIN key, as it can be used to link this table with other tables containing device-specific transactions or logs.
- **first_seen_ts_ist** and **last_seen_ts_ist**: These are critical for analyzing the lifecycle and engagement period of the device.
- **first_seen_city_name** and **last_seen_city_name**: These columns can be used to analyze geographical trends and are critical for regional analysis.

### Potential Relationships
- The `device_uuid` could be used to join with user activity logs or transaction tables where device identifiers are recorded.
- The `first_seen_city_name` and `last_seen_city_name` could relate to geographic or demographic tables for enriched location-based analysis.