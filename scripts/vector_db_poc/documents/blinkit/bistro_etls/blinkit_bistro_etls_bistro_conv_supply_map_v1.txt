# Table: blinkit.bistro_etls.bistro_conv_supply_map_v1

### Description
The table `blinkit.bistro_etls.bistro_conv_supply_map_v1` is designed to map products to their corresponding supplies, including conversion factors and the number of supplies mapped to each product. This table likely serves as a reference for inventory management, supply chain operations, or data analysis purposes within a bistro or restaurant management system.

### Partitioning
This table is not partitioned. Queries on this table may not be optimized for performance unless indexed appropriately on critical columns.

### Columns
- **final_product_id**
    - Data type: integer
    - Description: The unique identifier for a product.

- **final_product_name**
    - Data type: varchar
    - Description: The name of the product.

- **final_supply_id**
    - Data type: integer
    - Description: The unique identifier for a supply item linked to the product.

- **final_supply_name**
    - Data type: varchar
    - Description: The name of the supply item.

- **final_conversion_factor**
    - Data type: double
    - Description: The factor used to convert product quantities to supply quantities.

- **number_of_supply_mapped**
    - Data type: integer
    - Description: The total number of supply items mapped to a single product.

- **updated_at_ist**
    - Data type: timestamp(6)
    - Description: The timestamp indicating the last update time in IST (Indian Standard Time).

### Potential JOIN Keys and Business-Critical Columns
- **final_product_id**: Potential JOIN key for linking with other tables that contain product details.
- **final_supply_id**: Potential JOIN key for linking with other tables that contain supply details.

### Potential Relationships to Other Tables
- Tables containing detailed product information (e.g., `products` table) might have a `product_id` that corresponds to `final_product_id`.
- Tables containing supply information (e.g., `supplies` table) might have a `supply_id` that corresponds to `final_supply_id`.

This documentation provides a concise overview of the `blinkit.bistro_etls.bistro_conv_supply_map_v1` table, its structure, and its potential use within the database system.