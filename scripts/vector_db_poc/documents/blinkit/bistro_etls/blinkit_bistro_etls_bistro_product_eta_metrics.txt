# Table: blinkit.bistro_etls.bistro_product_eta_metrics

### Description
The `blinkit.bistro_etls.bistro_product_eta_metrics` table stores metrics related to estimated time of arrival (ETA) for products across different merchants and stations. It includes data on user interactions, product availability, and the impact of surge pricing on product ETAs. This table is likely used for analyzing product delivery efficiency and customer demand patterns.

### Partitioning
This table is not partitioned. Queries on this table may not benefit from partition pruning and could be less performant on large datasets.

### Columns
- **date_**
  - Data type: varchar
  - Description: The date when the data was recorded, likely in YYYY-MM-DD format.

- **hour_**
  - Data type: bigint
  - Description: The hour of the day (0-23) when the data was recorded, used for analyzing hourly trends.

- **merchant_name**
  - Data type: varchar
  - Description: The name of the merchant from which the product is sourced.

- **product_id**
  - Data type: varchar
  - Description: The unique identifier for a product, useful as a JOIN key with other product-related tables.

- **product_name**
  - Data type: varchar
  - Description: The name of the product, useful for human-readable reports and analysis.

- **users**
  - Data type: bigint
  - Description: The number of users who interacted with the product during the specified time.

- **surge_enabled**
  - Data type: bigint
  - Description: Indicates whether surge pricing was enabled (1) or not (0).

- **added_users**
  - Data type: bigint
  - Description: The number of new users who interacted with the product.

- **added_surge_enabled**
  - Data type: bigint
  - Description: The number of times surge pricing was enabled during new user interactions.

- **_15_eta**
  - Data type: bigint
  - Description: Estimated time of arrival within 15 minutes.

- **_20_eta**
  - Data type: bigint
  - Description: Estimated time of arrival within 20 minutes.

- **_25_eta**
  - Data type: bigint
  - Description: Estimated time of arrival within 25 minutes.

- **product_eta**
  - Data type: bigint
  - Description: General column for estimated time of arrival of the product.

- **station_name**
  - Data type: varchar
  - Description: The name of the station involved in the product's logistics.

### Potential JOIN Keys and Business-Critical Columns
- **product_id**: Can be used to join with other product-related tables such as inventory or sales data.
- **Business-Critical Columns**: `product_id`, `users`, `surge_enabled`, and `product_eta` are critical for analyzing product performance and customer engagement.

### Potential Relationships
- The `product_id` column suggests a relationship to tables that contain detailed product information or transaction records.
- The `merchant_name` and `station_name` may link to merchant-specific or logistics-related tables.

This documentation provides an overview of the structure and purpose of the `blinkit.bistro_etls.bistro_product_eta_metrics` table, aiding in efficient data querying and analysis.