# Table: blinkit.bistro_etls.fact_supply_chain_order_details_bistro

### Description
The table `blinkit.bistro_etls.fact_supply_chain_order_details_bistro` is designed to store detailed transactional data related to supply chain orders. It includes comprehensive information about each order, suborder, and cart, tracking their statuses, types, payment methods, timings, and various metrics related to order fulfillment and delivery. This table is crucial for analyzing the performance of the supply chain, understanding customer behavior, and optimizing logistics and delivery processes.

### Partitioning
The table is partitioned on the column `order_checkout_dt_ist`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval and query performance.

### Columns
- **order_id**
  - Data type: bigint
  - Description: Unique identifier for each order.
- **suborder_id**
  - Data type: bigint
  - Description: Unique identifier for each suborder, part of a larger order.
- **cart_id**
  - Data type: bigint
  - Description: Unique identifier for the shopping cart associated with the order.
- **order_type**
  - Data type: varchar
  - Description: Describes the type of the order.
- **frontend_merchant_id**
  - Data type: bigint
  - Description: Identifier for the merchant on the frontend.
- **backend_merchant_id**
  - Data type: bigint
  - Description: Identifier for the merchant on the backend.
- **outlet_id**
  - Data type: integer
  - Description: Identifier for the outlet from which the order is fulfilled.
- **order_weight**
  - Data type: double
  - Description: Total weight of the order.
- **partner_id**
  - Data type: varchar
  - Description: Identifier for the logistics or delivery partner.
- **trip_id**
  - Data type: varchar
  - Description: Identifier for the delivery trip.
- **order_current_status**
  - Data type: varchar
  - Description: Current status of the order.
- **payment_method**
  - Data type: varchar
  - Description: Method of payment used for the order.
- **is_order_on_time**
  - Data type: boolean
  - Description: Indicates whether the order was delivered on time.
- **is_order_cancelled**
  - Data type: boolean
  - Description: Indicates whether the order was cancelled.
- **is_order_delivered**
  - Data type: boolean
  - Description: Indicates whether the order was delivered.
- **is_order_fully_filled**
  - Data type: boolean
  - Description: Indicates whether all items in the order were fulfilled.
- **is_order_with_fnv**
  - Data type: boolean
  - Description: Indicates whether the order includes fruits and vegetables.
- **distinct_items_ordered**
  - Data type: bigint
  - Description: Number of distinct items ordered.
- **total_items_quantity_ordered**
  - Data type: bigint
  - Description: Total quantity of items ordered.
- **total_items_quantity_delivered**
  - Data type: bigint
  - Description: Total quantity of items delivered.
- **total_selling_price**
  - Data type: double
  - Description: Total selling price of the order.
- **total_cold_items_ordered**
  - Data type: bigint
  - Description: Number of cold items ordered.
- **is_rescheduled**
  - Data type: boolean
  - Description: Indicates whether the order was rescheduled.
- **order_checkout_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the order was checked out.
- **suborder_created_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the suborder was created.
- **order_delivery_slot_start_ts_ist**
  - Data type: timestamp(6)
  - Description: Start timestamp of the delivery slot.
- **order_ready_to_assign_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the order was ready to be assigned to a picker.
- **order_picker_assigned_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when a picker was assigned to the order.
- **order_billing_completed_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when billing for the order was completed.
- **order_partner_assigned_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when a delivery partner was assigned to the order.
- **order_scanned_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the order was scanned.
- **order_enroute_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the order was en route to delivery.
- **order_reached_doorstep_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the order reached the customer's doorstep.
- **order_payment_cod_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when payment was made on delivery.
- **order_delivered_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the order was delivered.
- **is_marked_delivery_failed**
  - Data type: boolean
  - Description: Indicates whether the delivery was marked as failed.
- **eta_shown_mins**
  - Data type: double
  - Description: Estimated time of arrival shown to the customer in minutes.
- **eta_computed_mins**
  - Data type: double
  - Description: Computed estimated time of arrival in minutes.
- **eta_override_mins**
  - Data type: double
  - Description: Overridden estimated time of arrival in minutes.
- **est_store_to_customer_location_kms**
  - Data type: double
  - Description: Estimated distance from store to customer location in kilometers.
- **est_partner_speed_kmph**
  - Data type: double
  - Description: Estimated speed of the delivery partner in km/h.
- **est_travel_duration_mins**
  - Data type: double
  - Description: Estimated travel duration in minutes.
- **est_picking_duration_mins**
  - Data type: double
  - Description: Estimated time for picking items in minutes.
- **est_billing_duration_mins**
  - Data type: double
  - Description: Estimated time for billing in minutes.
- **est_buffer_duration_mins**
  - Data type: double
  - Description: Estimated buffer duration in minutes.
- **est_picker_eta_mins**
  - Data type: double
  - Description: Estimated time of arrival for the picker in minutes.
- **est_field_executive_eta_mins**
  - Data type: double
  - Description: Estimated time of arrival for the field executive in minutes.
- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the ETL process snapshot.
- **customer_location_latitude**
  - Data type: double
  - Description: Latitude of the customer's location.
- **customer_location_longitude**
  - Data type: double
  - Description: Longitude of the customer's location.
- **distance_store_to_delivery_marked_kms**
  - Data type: double
  - Description: Distance from the store to the marked delivery location in kilometers.
- **distance_delivery_marked_to_customer_location_kms**
  - Data type: double
  - Description: Distance from the marked delivery location to the customer's location in kilometers.
- **order_checkout_dt_ist**
  - Data type: date
  - Description: Date when the order was checked out.
- **total_items_pna_marked**
  - Data type: integer
  - Description: Total items marked as 'product not available'.
- **is_missed_at_checkout**
  - Data type: boolean
  - Description: Indicates whether any item was missed at checkout.
- **order_cancel_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the order was cancelled.
- **is_surge_order**
  - Data type: boolean
  - Description: Indicates whether the order was placed during a surge period.
- **surge_reason**
  - Data type: varchar
  - Description: Reason for the surge pricing or demand.
- **is_batched_order**
  - Data type: boolean
  - Description: Indicates whether the order was batched with other orders.
- **accounting_distance**
  - Data type: double
  - Description: Distance used for accounting purposes.
- **is_dh_order**
  - Data type: boolean
  - Description: Indicates whether the order is a direct-to-home order.
- **picker_bag_scan_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the picker's bag was scanned.
- **address_id**
  - Data type: bigint
  - Description: Unique identifier for the delivery address.
- **is_order_with_beverage**
  - Data type: boolean
  - Description: Indicates whether the order includes beverages.
- **is_order_with_nonveg**
  - Data type: boolean
  - Description: Indicates whether the order includes non-vegetarian items.
- **min_prep_start_ts_ist**
  - Data type: timestamp(6)
  - Description: Minimum start timestamp for order preparation.
- **max_prep_end_ts_ist**
  - Data type: timestamp(6)
  - Description: Maximum end timestamp for order preparation.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `order_id`, `suborder_id`, `cart_id`, `frontend_merchant_id`, `backend_merchant_id`, `outlet_id`, `partner_id`, `trip_id`, `address_id`
- **Business-Critical Columns**: `order_id`, `order_current_status`, `is_order_delivered`, `total_selling_price`, `order_checkout_dt_ist`

### Potential Relationships
- Likely relationships with tables containing details on customers, merchants, outlets, and logistics partners, based on columns like `frontend_merchant_id`, `backend_merchant_id`, `outlet_id`, and `partner_id`.