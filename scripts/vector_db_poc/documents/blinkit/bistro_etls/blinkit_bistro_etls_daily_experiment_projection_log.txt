# Table: blinkit.bistro_etls.daily_experiment_projection_log

### Description
The `blinkit.bistro_etls.daily_experiment_projection_log` table is designed to store daily projections and historical sales data for products sold by merchants. It includes detailed metrics on sales quantity, growth rates, and projections based on day of the week (DOW) and special offers (DOTD - Deal of the Day). This table is likely used for analyzing sales trends, forecasting demand, and optimizing inventory and marketing strategies.

### Partitioning
- **Partition Key**: `updated_at_ts`
  - This key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval and management.

### Columns
- **order_date**
  - Data type: date
  - Description: The date on which the order was placed.
- **slot**
  - Data type: bigint
  - Description: A numeric identifier for a specific time slot in which the order was placed.
- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for the merchant.
- **merchant_name**
  - Data type: varchar
  - Description: The name of the merchant.
- **product_id**
  - Data type: bigint
  - Description: Unique identifier for the product.
- **product_name**
  - Data type: varchar
  - Description: The name of the product.
- **outlet_id**
  - Data type: bigint
  - Description: Unique identifier for the outlet from which the product was sold.
- **dow**
  - Data type: bigint
  - Description: Day of the week represented as a numeric value.
- **gmv**
  - Data type: real
  - Description: Gross Merchandise Value associated with the product for the given day.
- **quantity**
  - Data type: bigint
  - Description: Quantity of the product sold.
- **last_dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last similar day of the week.
- **last_dow_dotd_flag**
  - Data type: bigint
  - Description: Indicator if the last day of the week was a Deal of the Day.
- **store_age_in_days**
  - Data type: varchar
  - Description: Age of the store in days.
- **projection_date**
  - Data type: date
  - Description: The date for which sales are being projected.
- **preparation_type**
  - Data type: varchar
  - Description: Type of preparation for the product.
- **pack_size**
  - Data type: varchar
  - Description: Packaging size of the product.
- **deliver_qty**
  - Data type: bigint
  - Description: Quantity of the product delivered.
- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp when the record was last updated.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `merchant_id`, `product_id`, `outlet_id`
- **Business-Critical Columns**: `product_id`, `quantity`, `gmv`, `projection_date`

### Potential Relationships
- This table could be joined with other tables containing detailed merchant, product, or outlet information using `merchant_id`, `product_id`, and `outlet_id` respectively.

This documentation provides a structured and concise overview of the `daily_experiment_projection_log` table, facilitating efficient data management and retrieval for analysis and decision-making processes.