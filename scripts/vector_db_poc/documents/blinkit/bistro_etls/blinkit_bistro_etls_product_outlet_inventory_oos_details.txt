# Table: blinkit.bistro_etls.product_outlet_inventory_oos_details

### Description
The table `blinkit.bistro_etls.product_outlet_inventory_oos_details` is designed to store details about the inventory status of products at various outlets, specifically focusing on out-of-stock (OOS) situations. It tracks inventory data across different times and outlets, providing insights into product availability and helping in inventory management.

### Partitioning
The table is partitioned on the column `updated_at_ts`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

### Columns
- **date_**
  - Data type: varchar
  - Description: The date when the inventory status was recorded.
  
- **frontend_merchant_id**
  - Data type: integer
  - Description: The identifier for the merchant on the frontend system.
  
- **outlet_id**
  - Data type: varchar
  - Description: The identifier of the outlet where the inventory status is being tracked.
  
- **frontend_merchant_name**
  - Data type: varchar
  - Description: The name of the merchant as it appears on the frontend system.
  
- **product_id**
  - Data type: integer
  - Description: The identifier for the product whose inventory status is being tracked.
  
- **item_id**
  - Data type: integer
  - Description: A specific identifier for an item, potentially a SKU or a unique product variant.
  
- **sub_pid_name**
  - Data type: varchar
  - Description: The name of the sub-product or variant.
  
- **hour_bucket**
  - Data type: integer
  - Description: A categorization of time into buckets, likely representing specific hours of the day during which the inventory status was checked.
  
- **hour_fraction**
  - Data type: varchar
  - Description: More granular time division within an hour, possibly indicating the exact time when the inventory status was updated.
  
- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: The timestamp when the inventory record was last updated. This is also the partition key.

### Potential JOIN Keys and Business-Critical Columns
- **product_id**: Can be used to join with product tables to get more details about the products.
- **outlet_id**: Can be used to join with outlet tables to fetch location-specific data.
- **frontend_merchant_id**: Can be used to join with merchant tables to derive merchant-specific insights.

### Relationships to Other Tables
- The `product_id` column suggests a relationship to a products table where detailed product information is stored.
- The `outlet_id` column indicates a relationship to an outlet information table, detailing each outlet's location and other characteristics.
- The `frontend_merchant_id` might relate to a merchant table containing details about each merchant operating on the frontend system.

This documentation should serve as a foundational component for querying and understanding the `product_outlet_inventory_oos_details` table within the Blinkit Bistro ETLs schema.