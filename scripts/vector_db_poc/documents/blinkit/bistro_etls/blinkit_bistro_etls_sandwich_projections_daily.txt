# Table: blinkit.bistro_etls.sandwich_projections_daily

### Description
The `blinkit.bistro_etls.sandwich_projections_daily` table is designed to store daily projections and historical sales data for sandwiches at various merchant locations. It includes information on sales quantities, growth rates, and projections based on day of the week, as well as details about the products and merchants.

### Partitioning
The table is partitioned on the `updated_at_ts` column. This column is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **order_date**
  - Data type: varchar
  - Description: The date on which the order was placed.
  
- **merchant_id**
  - Data type: integer
  - Description: Unique identifier for the merchant.
  
- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant.
  
- **product_id**
  - Data type: integer
  - Description: Unique identifier for the product.
  
- **product_name**
  - Data type: varchar
  - Description: Name of the product.
  
- **outlet_id**
  - Data type: integer
  - Description: Unique identifier for the outlet where the product is sold.
  
- **dow**
  - Data type: integer
  - Description: Day of the week represented as an integer.
  
- **gmv**
  - Data type: double
  - Description: Gross Merchandise Value associated with the product for the day.
  
- **quantity**
  - Data type: integer
  - Description: Quantity of the product sold on the given day.
  
- **last_dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold on the last similar day of the week.
  
- **last_dow_dotd_flag**
  - Data type: integer
  - Description: Flag indicating if the product was part of a deal of the day last similar weekday.
  
- **last_2dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold two similar days of the week ago.
  
- **last_2dow_dotd_flag**
  - Data type: integer
  - Description: Flag indicating if the product was part of a deal of the day two similar weekdays ago.
  
- **last_3dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold three similar days of the week ago.
  
- **last_3dow_dotd_flag**
  - Data type: integer
  - Description: Flag indicating if the product was part of a deal of the day three similar weekdays ago.
  
- **last_4dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold four similar days of the week ago.
  
- **last_4dow_dotd_flag**
  - Data type: integer
  - Description: Flag indicating if the product was part of a deal of the day four similar weekdays ago.
  
- **last_5dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold five similar days of the week ago.
  
- **last_5dow_dotd_flag**
  - Data type: integer
  - Description: Flag indicating if the product was part of a deal of the day five similar weekdays ago.
  
- **store_age_in_days**
  - Data type: varchar
  - Description: The age of the store in days since opening.
  
- **opd**
  - Data type: varchar
  - Description: Operational performance data.
  
- **projection_date**
  - Data type: varchar
  - Description: The date for which sales projections are being made.
  
- **dow_proj_date**
  - Data type: integer
  - Description: Day of the week for the projection date.
  
- **expected_start_time**
  - Data type: varchar
  - Description: Expected start time for the product sale.
  
- **expected_end_time**
  - Data type: varchar
  - Description: Expected end time for the product sale.
  
- **preparation_time**
  - Data type: integer
  - Description: Time required to prepare the product.
  
- **preparation_type**
  - Data type: varchar
  - Description: Type of preparation required for the product.
  
- **sub_product_id**
  - Data type: integer
  - Description: Unique identifier for a sub-product or variant of the main product.
  
- **sub_pid_name**
  - Data type: varchar
  - Description: Name of the sub-product or variant.
  
- **last_non_dotd_dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold on the last non-deal day similar weekday.
  
- **last_non_dotd_2dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold two non-deal days similar weekdays ago.
  
- **last_non_dotd_3dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold three non-deal days similar weekdays ago.
  
- **last_2_dow_growth_rate**
  - Data type: double
  - Description: Growth rate comparing the last two similar weekdays.
  
- **last_dow_growth_rate**
  - Data type: double
  - Description: Growth rate since the last similar weekday.
  
- **row_median_growth**
  - Data type: double
  - Description: Median growth rate across rows.
  
- **last_2_dow_median_sold_quantity**
  - Data type: double
  - Description: Median quantity sold over the last two similar weekdays.
  
- **last_3_dow_stdev_sold_quantity**
  - Data type: double
  - Description: Standard deviation of quantities sold over the last three similar weekdays.
  
- **is_strictly_increasing**
  - Data type: boolean
  - Description: Flag indicating if the sales trend is strictly increasing.
  
- **is_strictly_decreasing**
  - Data type: boolean
  - Description: Flag indicating if the sales trend is strictly decreasing.
  
- **projected_increasing**
  - Data type: double
  - Description: Projected increase in sales.
  
- **increasing_cap1**
  - Data type: double
  - Description: First cap or limit for projected sales increase.
  
- **increasing_cap2**
  - Data type: double
  - Description: Second cap or limit for projected sales increase.
  
- **projected_increasing_updated**
  - Data type: double
  - Description: Updated projection for increasing sales.
  
- **projected_decreasing**
  - Data type: double
  - Description: Projected decrease in sales.
  
- **projected_default**
  - Data type: double
  - Description: Default projected sales figure.
  
- **final_projected**
  - Data type: double
  - Description: Final projected sales figure.
  
- **final_projected_updated**
  - Data type: double
  - Description: Updated final projected sales figure.
  
- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update to the record.
  
- **last_3_dow_median_sold_quantity**
  - Data type: double
  - Description: Median quantity sold over the last three similar weekdays.
  
- **last_4_non_dotd_similar_day_type_median_sold_quantity**
  - Data type: double
  - Description: Median quantity sold over the last four non-deal similar day types.
  
- **increasing_cap3**
  - Data type: double
  - Description: Third cap or limit for projected sales increase.
  
- **decreasing_cap1**
  - Data type: double
  - Description: First cap or limit for projected sales decrease.
  
- **projected_decreasing_updated**
  - Data type: double
  - Description: Updated projection for decreasing sales.
  
- **decreasing_cap2**
  - Data type: double
  - Description: Second cap or limit for projected sales decrease.

### Potential Relationships and JOIN Keys
- **merchant_id**, **product_id**, and **outlet_id** are potential JOIN keys, as they likely relate to other tables containing detailed information about merchants, products, and outlets.
- **sub_product_id** could be used to join with a sub-products table for more details on product variants.

This table is crucial for understanding sales trends, making projections, and optimizing product offerings and inventory at various merchant locations.