# Table: blinkit.bistro_etls.agg_daily_kitchen_summary

### Description
The `blinkit.bistro_etls.agg_daily_kitchen_summary` table aggregates daily metrics related to kitchen operations, merchant activities, and customer interactions for a food delivery service. It includes data on orders, sales, ratings, delivery times, and compensations, providing a comprehensive overview of daily business performance.

### Partitioning
This table is not partitioned. Queries on this table may not benefit from partition pruning and could be slower, especially on large datasets.

### Columns
- **date_**
  - Data type: varchar
  - Description: The date for which the data is aggregated.

- **week**
  - Data type: varchar
  - Description: The week of the year for the aggregated data.

- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for the merchant.

- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant.

- **city_name**
  - Data type: varchar
  - Description: Name of the city where the merchant is located.

- **user_type**
  - Data type: varchar
  - Description: Type of user placing the order (e.g., new or returning).

- **orders**
  - Data type: bigint
  - Description: Total number of orders placed.

- **total_selling_price**
  - Data type: double
  - Description: Total revenue generated from orders.

- **promo_orders**
  - Data type: bigint
  - Description: Number of orders placed using a promotional offer.

- **total_discount_amount**
  - Data type: double
  - Description: Total discount amount given in promotional offers.

- **total_procured_quantity**
  - Data type: bigint
  - Description: Total quantity of items procured for the orders.

- **new_user_orders**
  - Data type: bigint
  - Description: Number of orders placed by new users.

- **orders_rated**
  - Data type: bigint
  - Description: Number of orders that received a rating.

- **orders_rated_less_than_4**
  - Data type: bigint
  - Description: Number of orders rated less than 4 on a scale of 1 to 5.

- **orders_rated_less_than_2**
  - Data type: bigint
  - Description: Number of orders rated less than 2 on a scale of 1 to 5.

- **orders_rated_less_than_5**
  - Data type: bigint
  - Description: Number of orders rated less than 5 on a scale of 1 to 5.

- **rating**
  - Data type: double
  - Description: Average rating for the orders.

- **products_rated**
  - Data type: double
  - Description: Average rating received by products.

- **orders_under_15**
  - Data type: bigint
  - Description: Number of orders with a total delivery time under 15 minutes.

- **orders_under_15_total_delivery_time**
  - Data type: bigint
  - Description: Total delivery time for orders completed in under 15 minutes.

- **orders_above_20**
  - Data type: bigint
  - Description: Number of orders with a total delivery time above 20 minutes.

- **orders_above_25**
  - Data type: bigint
  - Description: Number of orders with a total delivery time above 25 minutes.

- **order_to_entoute_time**
  - Data type: varchar
  - Description: Time taken from order placement to the order being en route.

- **act_prep_and_wait_time**
  - Data type: varchar
  - Description: Actual time spent in preparation and waiting.

- **wait_time**
  - Data type: varchar
  - Description: Total wait time experienced by customers.

- **pred_prep**
  - Data type: varchar
  - Description: Predicted preparation time for orders.

- **assembly_time**
  - Data type: varchar
  - Description: Time taken to assemble the order.

- **order_to_doorstep**
  - Data type: varchar
  - Description: Time taken from order placement to delivery at the doorstep.

- **avg_eta**
  - Data type: double
  - Description: Average estimated time of arrival for deliveries.

- **total_del_time**
  - Data type: varchar
  - Description: Total delivery time for all orders.

- **assembly_breach_perc**
  - Data type: bigint
  - Description: Percentage of orders where assembly time exceeded the threshold.

- **eta_breach_5**
  - Data type: bigint
  - Description: Number of orders where the ETA was breached by more than 5 minutes.

- **eta_breach_2**
  - Data type: bigint
  - Description: Number of orders where the ETA was breached by more than 2 minutes.

- **prep_breach**
  - Data type: bigint
  - Description: Number of orders where the preparation time breached the expected time.

- **billing_to_entoute_time**
  - Data type: varchar
  - Description: Time taken from billing to the order being en route.

- **enroute_to_doorstep**
  - Data type: varchar
  - Description: Time taken for an order to move from being en route to reaching the doorstep.

- **doorstep_to_deliver**
  - Data type: varchar
  - Description: Time taken from reaching the doorstep to actual delivery.

- **dh**
  - Data type: bigint
  - Description: Placeholder for potential data, exact purpose not specified.

- **drop_distance**
  - Data type: varchar
  - Description: Distance the order was dropped off from the original delivery point.

- **defer_time_act**
  - Data type: varchar
  - Description: Actual deferment time for delayed orders.

- **comps**
  - Data type: bigint
  - Description: Number of compensations given for service failures.

- **other_comps**
  - Data type: bigint
  - Description: Number of other types of compensations given.

- **rider_att_comps**
  - Data type: bigint
  - Description: Compensations attributed to rider's actions.

- **store_att_comps**
  - Data type: bigint
  - Description: Compensations attributed to store's actions.

- **resol_amount**
  - Data type: double
  - Description: Total amount resolved through compensations.

- **pna_marked**
  - Data type: bigint
  - Description: Number of orders marked as 'Product Not Available'.

- **surge_orders**
  - Data type: bigint
  - Description: Number of orders placed during surge pricing.

- **etd_orders**
  - Data type: bigint
  - Description: Number of orders with estimated time of delivery provided.

- **daus**
  - Data type: bigint
  - Description: Daily active users on the platform.

- **atcs**
  - Data type: bigint
  - Description: Number of add-to-carts by users.

- **cv**
  - Data type: bigint
  - Description: Conversion value, possibly indicating the effectiveness of conversions.

- **payment**
  - Data type: bigint
  - Description: Total payment transactions processed.

- **trans**
  - Data type: bigint
  - Description: Total number of transactions.

- **dau_to_atc**
  - Data type: varchar
  - Description: Ratio of daily active users to add-to-cart actions.

- **dau_to_conversion**
  - Data type: varchar
  - Description: Ratio of daily active users to conversions.

- **negative_wastage_value**
  - Data type: double
  - Description: Value of negative wastage, possibly indicating loss or inefficiency.

- **cec_testing_value**
  - Data type: double
  - Description: Value associated with customer experience center testing.

- **cancelorders**
  - Data type: double
  - Description: Number of orders canceled.

- **store_age_category**
  - Data type: varchar
  - Description: Categorization of stores based on their operational age.

- **block_users**
  - Data type: bigint
  - Description: Number of users blocked from the service.

- **bl_users**
  - Data type: bigint
  - Description: Number of users on a blacklist.

- **customers**
  - Data type: bigint
  - Description: Total number of customers served.

- **_7_day_retentin_cust**
  - Data type: bigint
  - Description: Number of customers retained over a 7-day period.

- **_30_day_retentin_cust**
  - Data type: bigint
  - Description: Number of customers retained over a 30-day period.

- **manual_based_block_users**
  - Data type: double
  - Description: Number of users blocked manually.

- **demand_based_block_users**
  - Data type: double
  - Description: Number of users blocked based on demand patterns.

- **blo_iseres**
  - Data type: double
  - Description: Placeholder for potential data, exact purpose not specified.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id**: Likely used to join with other merchant-related tables.
- **city_name**: Could be used to join with geographical or regional data tables.
- **date_**: Essential for time-series analysis and joins with date-specific data tables.

### Potential Relationships
- **merchant_id** could be used to join with a `merchants` table containing detailed merchant profiles.
- **orders** and related metrics suggest potential joins with `order_details` or `product_catalog` tables for deeper analysis on what was ordered.
- **user_type**, **new_user_orders**, and retention metrics suggest potential analysis or joins with user demographic or behavior tables.