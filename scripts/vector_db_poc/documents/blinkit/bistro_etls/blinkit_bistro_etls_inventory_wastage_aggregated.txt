# Table: blinkit.bistro_etls.inventory_wastage_aggregated

### Description
The `blinkit.bistro_etls.inventory_wastage_aggregated` table aggregates data related to inventory wastage across different outlets. It tracks various types of wastage and their quantities and values, providing insights into areas of loss and potential improvements in inventory management.

### Partitioning
This table is not partitioned.

### Columns
- **date_till_12am**
  - Data type: varchar
  - Description: Represents the date up to which the wastage data is aggregated.

- **outlet_name**
  - Data type: varchar
  - Description: The name of the outlet where the wastage occurred.

- **outlet_id**
  - Data type: integer
  - Description: The identifier for the outlet, useful for joining with other outlet-specific tables.

- **item_id**
  - Data type: bigint
  - Description: The identifier for the item, can be used to join with item details tables.

- **item_name**
  - Data type: varchar
  - Description: The name of the item that was wasted.

- **flag_packaging**
  - Data type: varchar
  - Description: Indicates if the wastage was related to packaging issues.

- **flag**
  - Data type: varchar
  - Description: General flag to indicate other types of conditions or statuses related to the wastage.

- **overall_negative_wastage_quantity**
  - Data type: bigint
  - Description: Total quantity of items wasted negatively affecting inventory.

- **quantity_missing_wastage_quantity**
  - Data type: bigint
  - Description: Quantity of items that went missing, contributing to wastage.

- **mishandled_at_facility_wastage_quantity**
  - Data type: bigint
  - Description: Quantity of items mishandled at the facility.

- **vendor_pdt_damage_wastage_quantity**
  - Data type: bigint
  - Description: Quantity of items damaged by the vendor.

- **expired_wastage_quantity**
  - Data type: bigint
  - Description: Quantity of items that expired and were wasted.

- **cec_testing_quantity**
  - Data type: bigint
  - Description: Quantity of items used for testing and control checks.

- **manual_error_quantity**
  - Data type: bigint
  - Description: Quantity of wastage due to manual errors.

- **system_inaccuracies_quantity**
  - Data type: bigint
  - Description: Quantity of wastage due to system inaccuracies.

- **positive_wastage_missing_found_quantity**
  - Data type: bigint
  - Description: Quantity of items initially reported as wasted but later found.

- **positive_wastage_manual_error_quantity**
  - Data type: bigint
  - Description: Quantity of items wasted due to manual errors that had a positive impact.

- **positive_wastage_system_inaccuracies_quantity**
  - Data type: bigint
  - Description: Quantity of items wasted due to system inaccuracies that had a positive impact.

- **overall_negative_wastage_value**
  - Data type: double
  - Description: Monetary value of the overall negative wastage.

- **quantity_missing_wastage_value**
  - Data type: double
  - Description: Monetary value of the missing wastage.

- **mishandled_at_facility_wastage_value**
  - Data type: double
  - Description: Monetary value of the wastage due to mishandling at the facility.

- **vendor_pdt_damage_wastage_value**
  - Data type: double
  - Description: Monetary value of the wastage due to vendor product damage.

- **expired_wastage_value**
  - Data type: double
  - Description: Monetary value of the expired products that were wasted.

- **cec_testing_value**
  - Data type: double
  - Description: Monetary value of the products used for testing and control checks.

- **manual_error_value**
  - Data type: double
  - Description: Monetary value of wastage due to manual errors.

- **system_inaccuracies_value**
  - Data type: double
  - Description: Monetary value of wastage due to system inaccuracies.

- **positive_wastage_missing_found_value**
  - Data type: double
  - Description: Monetary value of items initially reported as wasted but later found.

- **positive_wastage_manual_error_value**
  - Data type: double
  - Description: Monetary value of items wasted due to manual errors that had a positive impact.

- **positive_wastage_system_inaccuracies_value**
  - Data type: double
  - Description: Monetary value of items wasted due to system inaccuracies that had a positive impact.

### Key Relationships and Joins
- **outlet_id** can be used to join with other outlet-related tables to fetch detailed outlet information.
- **item_id** can be used to join with item catalog tables to fetch detailed item descriptions and categories.

### Business-Critical Columns
- **overall_negative_wastage_quantity** and **overall_negative_wastage_value** are critical for assessing the financial impact of inventory wastage on the business.
- **item_id** and **outlet_id** are crucial for detailed analysis and tracking at the item and outlet levels.