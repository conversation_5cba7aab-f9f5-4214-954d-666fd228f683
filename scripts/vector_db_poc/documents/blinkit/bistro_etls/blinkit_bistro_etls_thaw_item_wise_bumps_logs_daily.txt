# Table: blinkit.bistro_etls.thaw_item_wise_bumps_logs_daily

### Description
The table `blinkit.bistro_etls.thaw_item_wise_bumps_logs_daily` is designed to store daily log data related to the thawing process of items at various outlets. It captures detailed statistics about potential and actual sales quantities of thawed items over the last 7 days, along with deviation metrics that help in analyzing performance and planning future inventory requirements.

### Partitioning
This table is partitioned based on the `updated_at_ts` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **outlet_id**
  - Data type: integer
  - Description: The unique identifier for the outlet where the items are thawed.
  
- **merchant_name**
  - Data type: varchar
  - Description: The name of the merchant owning the outlet.
  
- **slot**
  - Data type: varchar
  - Description: The time slot during which the thawing process was logged.
  
- **prepared_product_id**
  - Data type: integer
  - Description: The unique identifier for the prepared product that was thawed.
  
- **thawed_name**
  - Data type: varchar
  - Description: The name of the product after it has been thawed.
  
- **median_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The median of the potential sale quantity of the thawed product over the last 7 days.
  
- **avg_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The average potential sale quantity of the thawed product over the last 7 days.
  
- **max_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The maximum potential sale quantity recorded for the thawed product over the last 7 days.
  
- **min_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The minimum potential sale quantity recorded for the thawed product over the last 7 days.
  
- **std_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The standard deviation of potential sale quantities for the thawed product over the last 7 days.
  
- **median_quantity_sold_last_7d**
  - Data type: double
  - Description: The median of the actual quantity sold of the thawed product over the last 7 days.
  
- **avg_quantity_sold_last_7d**
  - Data type: double
  - Description: The average actual quantity sold of the thawed product over the last 7 days.
  
- **max_quantity_sold_last_7d**
  - Data type: double
  - Description: The maximum actual quantity sold of the thawed product over the last 7 days.
  
- **min_quantity_sold_last_7d**
  - Data type: double
  - Description: The minimum actual quantity sold of the thawed product over the last 7 days.
  
- **std_quantity_sold_last_7d**
  - Data type: double
  - Description: The standard deviation of actual quantities sold for the thawed product over the last 7 days.
  
- **deviation_days**
  - Data type: double
  - Description: The number of days the actual sales deviated from the predicted sales.
  
- **avg_deviation_positive**
  - Data type: double
  - Description: The average of positive deviations from the expected sales figures.
  
- **custom_weighted_deviation**
  - Data type: double
  - Description: A custom metric that weights deviations based on certain business rules.
  
- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: The timestamp when the record was last updated.
  
- **date_**
  - Data type: date
  - Description: The date for which the log entry is relevant.

### Potential JOIN Keys and Business-Critical Columns
- **outlet_id**: Can be used to join with other tables containing outlet-specific data.
- **prepared_product_id**: Likely a key to join with product tables to get detailed product information.
- **updated_at_ts**: Critical for partitioning; also useful for time-series analysis.
- **date_**: Important for filtering data based on specific days.

### Relationships to Other Tables
- `prepared_product_id` might relate to a `products` table where complete product details are stored.
- `outlet_id` could be used to join with an `outlets` table that contains location and other metadata about the outlets.