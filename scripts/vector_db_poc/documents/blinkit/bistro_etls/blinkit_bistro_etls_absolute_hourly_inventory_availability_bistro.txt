# Table: blinkit.bistro_etls.absolute_hourly_inventory_availability_bistro

### Description
The table `absolute_hourly_inventory_availability_bistro` in the `blinkit.bistro_etls` schema is designed to store hourly inventory availability data for various food items across different outlets. It captures detailed snapshots of inventory status, including availability flags and item details, to help in managing stock levels efficiently and providing insights into inventory trends over time.

### Partitioning
The table is partitioned on the column `snapshot_date_ist`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **snapshot_date_ist**
    - Data type: date
    - Description: The date on which the inventory snapshot was taken, used for partitioning the data.

- **snapshot_hour**
    - Data type: varchar
    - Description: The hour of the day when the snapshot was taken, stored as a string.

- **snapshot_hour_ist**
    - Data type: timestamp(6)
    - Description: The exact timestamp when the snapshot was taken, including date and time.

- **city_name**
    - Data type: varchar
    - Description: The name of the city where the outlet is located.

- **outlet_id**
    - Data type: integer
    - Description: The unique identifier for the outlet, potentially useful for joining with outlet-specific tables.

- **outlet_name**
    - Data type: varchar
    - Description: The name of the outlet from which the inventory data is collected.

- **merchant_id**
    - Data type: bigint
    - Description: The identifier for the merchant associated with the outlet, can be used to join with merchant tables.

- **final_item_id**
    - Data type: integer
    - Description: The unique identifier for the item, crucial for item-level analysis and joins with item tables.

- **item_name**
    - Data type: varchar
    - Description: The name of the item in the inventory.

- **station**
    - Data type: varchar
    - Description: The station or specific area within the outlet where the item is located.

- **l2_category**
    - Data type: varchar
    - Description: The secondary category of the item, providing additional classification detail.

- **dish_type**
    - Data type: varchar
    - Description: The type of dish to which the item belongs, useful for categorizing menu items.

- **avail_flag_oos**
    - Data type: bigint
    - Description: Availability flag indicating if the item is out of stock.

- **avail_flag_back_soon**
    - Data type: bigint
    - Description: Availability flag indicating if the item will be back in stock soon.

- **avail_flag_both**
    - Data type: bigint
    - Description: Combined availability flag for other statuses.

- **total_inv_flag**
    - Data type: bigint
    - Description: Flag representing the total inventory status.

- **etl_snapshot_ts_ist**
    - Data type: timestamp(6)
    - Description: The timestamp for when this ETL snapshot was recorded, useful for tracking data updates.

### Potential JOINs and Relationships
- **outlet_id**: Can be used to join with other outlet-related tables to fetch more detailed outlet information.
- **merchant_id**: Useful for joining with merchant-related tables to gather comprehensive merchant data.
- **final_item_id**: Key column for joining with item tables to retrieve detailed item descriptions or categories.

This documentation provides a clear overview of the table structure and its intended use within the database, facilitating efficient data management and query optimization.