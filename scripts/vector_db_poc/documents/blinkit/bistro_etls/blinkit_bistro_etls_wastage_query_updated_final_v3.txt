# Table: blinkit.bistro_etls.wastage_query_updated_final_v3

### Description
The `blinkit.bistro_etls.wastage_query_updated_final_v3` table is designed to track and analyze various types of wastage across different outlets. It captures both the quantity and value of wastage, categorized by reasons such as mishandling, vendor damage, expiration, and testing. This data helps in understanding the efficiency and operational challenges of the outlets.

### Partitioning
This table is not partitioned. Queries on this table may not benefit from partition pruning and could be slower, especially on large datasets.

### Columns
- **date_till_12am**
  - Data type: varchar
  - Description: Represents the date up to which the wastage data is recorded.
  
- **outlet_name**
  - Data type: varchar
  - Description: The name of the outlet where the wastage occurred.
  
- **outlet_id**
  - Data type: integer
  - Description: A unique identifier for the outlet, useful for joining with other outlet-related tables.
  
- **item_id**
  - Data type: bigint
  - Description: A unique identifier for the item, which can be used to join with item-related tables.
  
- **item_name**
  - Data type: varchar
  - Description: The name of the item that was wasted.
  
- **flag_packaging**
  - Data type: varchar
  - Description: Indicates if the wastage was related to packaging issues.
  
- **flag**
  - Data type: varchar
  - Description: A general flag to indicate other types of conditions or statuses related to the wastage.
  
- **overall_wastage_quantity**
  - Data type: bigint
  - Description: Total quantity of all wastage for the recorded period.
  
- **quantity_missing_wastage_quantity**
  - Data type: bigint
  - Description: Quantity of wastage due to missing items.
  
- **mishandled_at_facility_wastage_quantity**
  - Data type: bigint
  - Description: Quantity of wastage due to mishandling at the facility.
  
- **vendor_pdt_damage_wastage_quantity**
  - Data type: bigint
  - Description: Quantity of wastage due to vendor product damage.
  
- **expired_wastage_quantity**
  - Data type: bigint
  - Description: Quantity of wastage due to expiration of products.
  
- **cec_testing_quantity**
  - Data type: bigint
  - Description: Quantity of products used for testing and quality control (CEC testing).
  
- **overall_wastage_value**
  - Data type: double
  - Description: Total value of all wastage for the recorded period in monetary terms.
  
- **quantity_missing_wastage_value**
  - Data type: double
  - Description: Monetary value of wastage due to missing items.
  
- **mishandled_at_facility_wastage_value**
  - Data type: double
  - Description: Monetary value of wastage due to mishandling at the facility.
  
- **vendor_pdt_damage_wastage_value**
  - Data type: double
  - Description: Monetary value of wastage due to vendor product damage.
  
- **expired_wastage_value**
  - Data type: double
  - Description: Monetary value of wastage due to expiration of products.
  
- **cec_testing_value**
  - Data type: double
  - Description: Monetary value of products used for testing and quality control (CEC testing).

### Potential JOIN Keys and Business-Critical Columns
- **outlet_id**: Can be used to join with other tables that contain outlet-specific data.
- **item_id**: Useful for joining with inventory or product tables to get more details about the items.
- **Business-Critical Columns**: `overall_wastage_quantity`, `overall_wastage_value` - these columns are crucial for assessing the financial impact of wastage on the business.

### Potential Relationships
- This table likely relates to tables containing detailed outlet information (`outlet_id`) and product details (`item_id`). These relationships can be used to enrich the wastage data with more contextual information about the outlets and products.