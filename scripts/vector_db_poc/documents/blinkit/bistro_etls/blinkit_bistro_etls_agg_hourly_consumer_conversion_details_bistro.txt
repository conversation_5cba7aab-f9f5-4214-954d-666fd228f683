# Table: blinkit.bistro_etls.agg_hourly_consumer_conversion_details_bistro

### Description
The `blinkit.bistro_etls.agg_hourly_consumer_conversion_details_bistro` table aggregates hourly consumer conversion metrics for a digital platform. It captures various user interactions and conversion steps, providing insights into user behavior and the effectiveness of the platform's user interface and conversion funnel.

### Partitioning
This table is not partitioned. Queries on this table may experience slower performance due to the lack of partition keys, and it is recommended to optimize queries as much as possible.

### Columns
- **snapshot_date_ist**
  - Data type: date
  - Description: The date of the data snapshot in IST timezone.

- **snapshot_hour_ist**
  - Data type: varchar
  - Description: The specific hour of the data snapshot in IST timezone.

- **channel**
  - Data type: varchar
  - Description: The channel through which the user accessed the platform.

- **platform**
  - Data type: varchar
  - Description: The type of platform (e.g., mobile, desktop) used to access the service.

- **city**
  - Data type: varchar
  - Description: The city from which the user accessed the platform.

- **merchant_id**
  - Data type: varchar
  - Description: Identifier for the merchant involved in the transaction.

- **user_bucket**
  - Data type: varchar
  - Description: Categorization of users into different buckets based on defined criteria.

- **user_type**
  - Data type: varchar
  - Description: Type of user, such as new or returning.

- **daily_active_users**
  - Data type: bigint
  - Description: Count of unique users who were active on the platform on the given day.

- **app_launch**
  - Data type: bigint
  - Description: Number of times the application was launched.

- **home_page_visit**
  - Data type: bigint
  - Description: Number of visits to the home page.

- **home_page_nsa_visit**
  - Data type: bigint
  - Description: Number of non-significant action visits to the home page.

- **add_to_cart**
  - Data type: bigint
  - Description: Number of times users added items to their shopping cart.

- **cart_visit**
  - Data type: bigint
  - Description: Number of visits to the shopping cart page.

- **user_registered**
  - Data type: bigint
  - Description: Number of users who registered on the platform.

- **nsa_pings**
  - Data type: bigint
  - Description: Count of non-significant actions performed by users.

- **login_successful**
  - Data type: bigint
  - Description: Number of successful login attempts.

- **address_added**
  - Data type: bigint
  - Description: Number of times users added a shipping address.

- **payment_page_visit**
  - Data type: bigint
  - Description: Number of visits to the payment page.

- **app_launch_to_home_page_visit**
  - Data type: bigint
  - Description: Conversion metric from app launch to home page visit.

- **dau_to_home_page_visit**
  - Data type: bigint
  - Description: Conversion from daily active users to home page visits.

- **app_launch_to_home_page_visit_nsa**
  - Data type: bigint
  - Description: Conversion from app launch to non-significant action home page visits.

- **dau_to_atc**
  - Data type: bigint
  - Description: Conversion from daily active users to add to cart actions.

- **atc_to_cv**
  - Data type: bigint
  - Description: Conversion from add to cart to cart visit.

- **cv_to_user_registered**
  - Data type: bigint
  - Description: Conversion from cart visit to user registration.

- **cv_to_address_added**
  - Data type: bigint
  - Description: Conversion from cart visit to address addition.

- **cv_to_payment**
  - Data type: bigint
  - Description: Conversion from cart visit to payment page visit.

- **payment_to_checkout**
  - Data type: bigint
  - Description: Conversion from payment initiation to checkout completion.

- **cv_to_checkout**
  - Data type: bigint
  - Description: Conversion from cart visit to checkout completion.

- **overall_conversion**
  - Data type: bigint
  - Description: Overall conversion rate capturing the entire funnel from start to finish.

- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of when the ETL process that generated this record was run, in IST timezone.

- **login_page_visit**
  - Data type: bigint
  - Description: Number of visits to the login page.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id**: Could be used to join with merchant-related tables.
- **city**: May join with geographic or demographic data tables.
- **user_type**, **daily_active_users**, **overall_conversion**: Critical for business analysis and user segmentation.

### Potential Relationships
- Columns like `merchant_id` suggest potential joins with merchant tables.
- User behavior metrics (e.g., `app_launch`, `add_to_cart`) could relate to user session tables or product tables for deeper analysis.