# Table: blinkit.bistro_etls.fact_sales_order_item_details_bistro

### Description

The `blinkit.bistro_etls.fact_sales_order_item_details_bistro` table is designed to store detailed transactional data related to sales orders and items within those orders. It captures a comprehensive set of attributes for each order item, including pricing, discounts, merchant details, product quantities, and status updates. This table is crucial for analyzing sales performance, customer behavior, and operational efficiency.

### Partitioning

The table is partitioned on the `order_create_dt_ist` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns

- **order_item_id**
  - Data type: bigint
  - Description: Unique identifier for each order item.

- **order_id**
  - Data type: bigint
  - Description: Identifier linking to the main order. Potential JOIN key with other order-related tables.

- **suborder_item_id**
  - Data type: bigint
  - Description: Unique identifier for sub-items within an order item.

- **suborder_id**
  - Data type: bigint
  - Description: Identifier for suborders within a main order.

- **cart_id**
  - Data type: bigint
  - Description: Identifier for the shopping cart from which the order originated.

- **dim_product_key**
  - Data type: bigint
  - Description: Dimension key linking to the product details. Potential JOIN key with product dimension tables.

- **dim_customer_key**
  - Data type: bigint
  - Description: Dimension key for customer details. Potential JOIN key with customer dimension tables.

- **dim_customer_address_key**
  - Data type: bigint
  - Description: Dimension key for customer address details.

- **dim_frontend_merchant_key**
  - Data type: bigint
  - Description: Dimension key for frontend merchant details.

- **dim_backend_merchant_key**
  - Data type: bigint
  - Description: Dimension key for backend merchant details.

- **product_quantity**
  - Data type: bigint
  - Description: Quantity of the product ordered.

- **procured_quantity**
  - Data type: bigint
  - Description: Quantity of the product procured for the order.

- **total_selling_price**
  - Data type: real
  - Description: Total price at which the product was sold.

- **unit_selling_price**
  - Data type: real
  - Description: Selling price per unit of the product.

- **total_mrp**
  - Data type: real
  - Description: Total maximum retail price of the product.

- **unit_mrp**
  - Data type: real
  - Description: Maximum retail price per unit of the product.

- **total_discount_amount**
  - Data type: real
  - Description: Total discount given on the product.

- **unit_discount_amount**
  - Data type: real
  - Description: Discount amount per unit of the product.

- **total_cashback_amount**
  - Data type: real
  - Description: Total cashback offered on the product.

- **unit_cashback_amount**
  - Data type: real
  - Description: Cashback amount per unit of the product.

- **total_weighted_landing_price**
  - Data type: double
  - Description: Total cost price of the product including all expenses.

- **unit_weighted_landing_price**
  - Data type: double
  - Description: Cost price per unit of the product including all expenses.

- **total_brand_fund**
  - Data type: double
  - Description: Total funding received from the brand for the product.

- **unit_brand_fund**
  - Data type: double
  - Description: Funding received per unit from the brand.

- **total_retained_margin**
  - Data type: double
  - Description: Total margin retained after selling the product.

- **unit_retained_margin**
  - Data type: double
  - Description: Margin retained per unit after selling the product.

- **channel**
  - Data type: varchar
  - Description: Sales channel through which the order was placed.

- **order_type**
  - Data type: varchar
  - Description: Type of the order, e.g., online, in-store.

- **order_current_status**
  - Data type: varchar
  - Description: Current status of the order.

- **suborder_current_status**
  - Data type: varchar
  - Description: Current status of the suborder.

- **cancelled_quantity**
  - Data type: bigint
  - Description: Quantity of the product that was cancelled.

- **item_cancellation_reason**
  - Data type: varchar
  - Description: Reason for the cancellation of the item.

- **original_order_item_id**
  - Data type: varchar
  - Description: Original identifier for the order item, if it was modified.

- **order_create_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the order was created, in IST.

- **order_create_dim_date_key**
  - Data type: varchar
  - Description: Dimension date key for the order creation date.

- **order_item_create_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the order item was created, in IST.

- **order_item_create_dim_date_key**
  - Data type: varchar
  - Description: Dimension date key for the order item creation date.

- **suborder_rank**
  - Data type: bigint
  - Description: Ranking of the suborder within the main order.

- **cart_checkout_dim_date_key**
  - Data type: varchar
  - Description: Dimension date key for the cart checkout date.

- **cart_checkout_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the cart was checked out, in IST.

- **unit_liquid_fund**
  - Data type: double
  - Description: Liquid fund amount per unit.

- **total_liquid_fund**
  - Data type: double
  - Description: Total liquid fund amount.

- **station_name**
  - Data type: varchar
  - Description: Name of the station or location from where the order was processed.

- **city_name**
  - Data type: varchar
  - Description: Name of the city from where the order was processed.

- **unit_self_fund**
  - Data type: double
  - Description: Self-funded amount per unit.

- **total_self_fund**
  - Data type: double
  - Description: Total self-funded amount.

- **product_id**
  - Data type: bigint
  - Description: Identifier for the product. Potential JOIN key with product tables.

- **frontend_merchant_id**
  - Data type: bigint
  - Description: Identifier for the frontend merchant.

- **backend_merchant_id**
  - Data type: bigint
  - Description: Identifier for the backend merchant.

- **outlet_id**
  - Data type: bigint
  - Description: Identifier for the outlet from which the order was processed.

- **org_channel_id**
  - Data type: varchar
  - Description: Original channel ID through which the order was placed.

- **org_channel_name**
  - Data type: varchar
  - Description: Name of the original channel through which the order was placed.

- **is_internal_order**
  - Data type: boolean
  - Description: Indicates whether the order is internal.

- **dim_assortment_type_key**
  - Data type: bigint
  - Description: Dimension key for the type of assortment of the product.

- **pricing_change_log_id**
  - Data type: bigint
  - Description: Identifier for the log of pricing changes.

- **pricing_unit_wlp**
  - Data type: double
  - Description: Unit weighted landing price at the time of pricing change.

- **etl_update_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the last ETL process update, in IST.

- **order_create_dt_ist**
  - Data type: date
  - Description: Date when the order was created, in IST. Critical for partitioning.

- **is_easy_return_order_item**
  - Data type: boolean
  - Description: Indicates whether the order item qualifies for easy returns.

- **is_easy_return_order**
  - Data type: boolean
  - Description: Indicates whether the entire order qualifies for easy returns.

- **etl_oi_update_ts**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update to this order item in the ETL process.

- **etl_oo_update_ts**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update to this order in the ETL process.

### Potential Relationships

- **order_id** could be used to join with other order-related tables such as `orders`, `order_payments`, etc.
- **dim_product_key**, **product_id** could be used to join with product tables to fetch detailed product information.
- **dim_customer_key** could be used to join with customer dimension tables for customer insights.
- **dim_frontend_merchant_key**, **frontend_merchant_id**, **dim_backend_merchant_key**, **backend_merchant_id** could be used to join with merchant tables for merchant-related analytics.