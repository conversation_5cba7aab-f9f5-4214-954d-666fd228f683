# Table: blinkit.bistro_etls.nu_dau_install_attribution

### Description
The `blinkit.bistro_etls.nu_dau_install_attribution` table is designed to store daily active user (DAU) data linked to marketing attribution, specifically focusing on installations. This table tracks which marketing campaigns and media sources are most effective at driving app installations on a given date.

### Partitioning
The table is partitioned on the `dau_dt` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **dau_dt**
  - Data type: date
  - Description: The date on which the user activity is recorded, used as a partition key.

- **device_uuid**
  - Data type: varchar
  - Description: A unique identifier for the device on which the app is installed.

- **install_dt**
  - Data type: date
  - Description: The date on which the app was installed on the device.

- **appsflyer_id**
  - Data type: varchar
  - Description: A unique identifier provided by <PERSON><PERSON><PERSON><PERSON><PERSON>, used for tracking the source of app installations.

- **event_name**
  - Data type: varchar
  - Description: The name of the event associated with the user activity, typically related to app installation or engagement.

- **media_source**
  - Data type: varchar
  - Description: The source of the media from which the app was installed, indicating the marketing channel.

- **campaign**
  - Data type: varchar
  - Description: The specific marketing campaign associated with the app installation, useful for analyzing the effectiveness of different marketing strategies.

### Potential JOIN Keys and Business-Critical Columns
- **device_uuid**: Can be used to join with other user or device-related tables to enrich the data with more user-specific information.
- **appsflyer_id**: May join with other marketing or attribution tables that use AppsFlyer IDs to track user acquisition channels.
- **install_dt**: Important for analyzing trends over time and can be used to join with other tables that track user activity by installation date.

### Potential Relationships
- The `device_uuid` might relate to a user profile table containing detailed user information.
- The `appsflyer_id` could be used to link to other tables that contain detailed attribution data for other events or user actions.
- The `install_dt` could be used to correlate with tables tracking user activity or engagement post-installation.

This documentation provides a structured and concise overview of the `blinkit.bistro_etls.nu_dau_install_attribution` table, ensuring efficient and effective use in querying and analysis within the bedrock knowledge base.