# Table: blinkit.bistro_etls.fact_atc_checkout_detail_base

### Description
The `blinkit.bistro_etls.fact_atc_checkout_detail_base` table is designed to store detailed event data related to user interactions, such as adding to cart and checkout processes, on different platforms and devices. This table captures a wide range of attributes including user traits, product properties, session details, and page navigation specifics, which are essential for analyzing user behavior and optimizing the e-commerce experience.

### Partitioning
The table is partitioned on the following keys, which are CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition:
- `at_date_ist`: Date of the event.
- `hour_`: Hour of the day when the event occurred.

### Columns
- **at_date_ist**
  - Data type: date
  - Description: The date on which the event was recorded.
  
- **at_ist**
  - Data type: timestamp(6)
  - Description: The exact timestamp when the event occurred.
  
- **platform**
  - Data type: varchar
  - Description: The platform from which the event was generated (e.g., iOS, Android, Web).
  
- **name**
  - Data type: varchar
  - Description: The name associated with the event, possibly the user's name or identifier.
  
- **hour_**
  - Data type: bigint
  - Description: The hour of the day when the event was logged.
  
- **traits__user_id**
  - Data type: varchar
  - Description: Unique identifier for the user involved in the event.
  
- **device_uuid**
  - Data type: varchar
  - Description: Unique identifier for the device used during the event.
  
- **traits__city_name**
  - Data type: varchar
  - Description: The city from which the user accessed the service.
  
- **traits__merchant_id**
  - Data type: integer
  - Description: Identifier for the merchant involved in the transaction.
  
- **properties__product_id**
  - Data type: varchar
  - Description: Unique identifier for the product involved in the event.
  
- **properties__page_name**
  - Data type: varchar
  - Description: The name of the page where the event occurred.
  
- **properties__page_type**
  - Data type: varchar
  - Description: The type of page (e.g., product page, landing page).
  
- **properties__product_position**
  - Data type: integer
  - Description: The position of the product on the page.
  
- **properties__search_keyword_type**
  - Data type: varchar
  - Description: The type of search keyword used.
  
- **properties__search_previous_keyword**
  - Data type: varchar
  - Description: The previous search keyword entered by the user before the current one.
  
- **properties__search_input_keyword**
  - Data type: varchar
  - Description: The search keyword input by the user.
  
- **properties__search_actual_keyword**
  - Data type: varchar
  - Description: The actual search keyword that triggered the event.
  
- **properties__search_keyword_parent**
  - Data type: varchar
  - Description: The parent keyword related to the search keyword used.
  
- **properties__page_visit_id**
  - Data type: varchar
  - Description: Unique identifier for the page visit during which the event occurred.
  
- **properties__parent_product**
  - Data type: varchar
  - Description: Identifier for the parent product related to the product involved in the event.
  
- **properties__price**
  - Data type: double
  - Description: The price of the product involved in the event.
  
- **properties__widget_name**
  - Data type: varchar
  - Description: The name of the widget through which the event was triggered.
  
- **properties__widget_title**
  - Data type: varchar
  - Description: The title of the widget involved in the event.
  
- **properties__sub_page_name**
  - Data type: varchar
  - Description: The name of the sub-page visited during the event.
  
- **properties__sub_page_visit_id**
  - Data type: varchar
  - Description: Unique identifier for the sub-page visit during which the event occurred.
  
- **properties__widget_tracking_id**
  - Data type: varchar
  - Description: Tracking identifier for the widget involved in the event.
  
- **traits__cart_id**
  - Data type: varchar
  - Description: Unique identifier for the user's cart during the event.
  
- **properties__inventory**
  - Data type: varchar
  - Description: Information about the inventory status of the product involved in the event.
  
- **properties__page_title**
  - Data type: varchar
  - Description: The title of the page where the event occurred.
  
- **traits__merchant_name**
  - Data type: varchar
  - Description: Name of the merchant involved in the transaction.
  
- **traits__segment_enabled_features**
  - Data type: varchar
  - Description: Features enabled for the user segment to which the event pertains.
  
- **properties__child_widget_id**
  - Data type: varchar
  - Description: Identifier for a child widget related to the widget involved in the event.
  
- **properties__page_id**
  - Data type: varchar
  - Description: Unique identifier for the page where the event occurred.
  
- **properties__sub_page_title**
  - Data type: varchar
  - Description: The title of the sub-page visited during the event.
  
- **properties__title**
  - Data type: varchar
  - Description: The title associated with the event or the product.
  
- **properties__child_widget_title**
  - Data type: varchar
  - Description: The title of a child widget related to the widget involved in the event.
  
- **app__version**
  - Data type: varchar
  - Description: The version of the application from which the event was generated.
  
- **traits__app_version_code**
  - Data type: bigint
  - Description: The version code of the application corresponding to the event.
  
- **session_uuid**
  - Data type: varchar
  - Description: Unique identifier for the session during which the event occurred.
  
- **properties__badge**
  - Data type: varchar
  - Description: Badge information associated with the product or event.
  
- **properties__collection_id**
  - Data type: varchar
  - Description: Identifier for a collection of products related to the event.
  
- **properties__collection_name**
  - Data type: varchar
  - Description: Name of the product collection involved in the event.
  
- **properties__widget_position**
  - Data type: integer
  - Description: The position of the widget on the page.
  
- **properties__widget_variation_id**
  - Data type: varchar
  - Description: Identifier for a variation of the widget involved in the event.
  
- **properties__child_widget_tracking_id**
  - Data type: varchar
  - Description: Tracking identifier for a child widget related to the widget involved in the event.
  
- **properties__filter**
  - Data type: varchar
  - Description: Filter settings applied during the event.
  
- **properties__child_widget_position**
  - Data type: integer
  - Description: The position of a child widget related to the widget involved in the event.
  
- **id**
  - Data type: varchar
  - Description: Unique identifier for the event record.
  
- **traits__session_launch_source**
  - Data type: varchar
  - Description: Source from which the session was launched.
  
- **properties__product_list_id**
  - Data type: varchar
  - Description: Identifier for a list of products involved in the event.
  
- **properties__last_page_name**
  - Data type: varchar
  - Description: The name of the last page visited before the current page.
  
- **properties__last_page_visit_id**
  - Data type: varchar
  - Description: Unique identifier for the last page visit before the current page visit.

### Potential JOIN Keys and Business-Critical Columns
- **traits__user_id**: Can be used to join with user tables for detailed user profile analysis.
- **properties__product_id**: Essential for joining with product tables to fetch product details.
- **traits__merchant_id**: Useful for joining with merchant tables for merchant-specific analysis.
- **session_uuid**: Key for session analysis, potentially joining with session logs or user session tables.

### Potential Relationships
- **properties__product_id** suggests a relationship with a products table.
- **traits__merchant_id** indicates a possible link to a merchants table.
- **session_uuid** could relate to session or user activity tables.