# Table: blinkit.bistro_etls.thaw_item_wise_bumps

### Description
The `blinkit.bistro_etls.thaw_item_wise_bumps` table stores detailed information about item-level sales performance metrics for products that have been thawed at various outlets. It includes data on potential and actual sales quantities over the last 7 days, along with statistical measures of deviation and performance trends. This table is likely used for analyzing product performance, inventory management, and sales forecasting.

### Partitioning
The table is partitioned on the `updated_at_ts` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **outlet_id**
  - Data type: integer
  - Description: Unique identifier for the outlet where the product was sold or managed.

- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant owning the outlet.

- **slot**
  - Data type: varchar
  - Description: Time slot during which the sales data was recorded.

- **prepared_product_id**
  - Data type: integer
  - Description: Unique identifier for the prepared product.

- **thawed_name**
  - Data type: varchar
  - Description: Name of the product after it has been thawed.

- **median_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: Median of the potential sale quantity of the product over the last 7 days.

- **avg_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: Average potential sale quantity of the product over the last 7 days.

- **max_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: Maximum potential sale quantity of the product over the last 7 days.

- **min_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: Minimum potential sale quantity of the product over the last 7 days.

- **median_quantity_sold_last_7d**
  - Data type: double
  - Description: Median of the actual quantity sold of the product over the last 7 days.

- **avg_quantity_sold_last_7d**
  - Data type: double
  - Description: Average actual quantity sold of the product over the last 7 days.

- **max_quantity_sold_last_7d**
  - Data type: double
  - Description: Maximum actual quantity sold of the product over the last 7 days.

- **min_quantity_sold_last_7d**
  - Data type: double
  - Description: Minimum actual quantity sold of the product over the last 7 days.

- **deviation_days**
  - Data type: double
  - Description: Number of days the actual sales deviated from the forecasted sales.

- **avg_deviation_positive**
  - Data type: double
  - Description: Average of positive deviations from the expected sales figures.

- **custom_weighted_deviation**
  - Data type: double
  - Description: Custom calculated weighted deviation for the product's sales performance.

- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp marking the last update to the record.

- **date_**
  - Data type: date
  - Description: The date corresponding to the sales data.

### Potential JOIN Keys and Relationships
- **prepared_product_id**: Can be used to join with product tables to fetch detailed product information.
- **outlet_id**: May be used to join with outlet tables for location-specific data.

### Business-Critical Columns
- **median_quantity_sold_last_7d**, **avg_quantity_sold_last_7d**, **max_quantity_sold_last_7d**, and **min_quantity_sold_last_7d**: These columns are critical for understanding sales performance and making inventory decisions.