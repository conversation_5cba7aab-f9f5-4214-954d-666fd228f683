# Table: blinkit.bistro_etls.hourly_customer_conv_abs_metrics_bistro

## Data Dictionary for Table: `blinkit.bistro_etls.hourly_customer_conv_abs_metrics_bistro`

### Description
This table appears to be designed for tracking hourly customer engagement metrics across various aspects of a digital platform. Metrics include user activity, page visits, and actions such as logins and registrations, segmented by channel, platform, city, and merchant. This data is likely used for analyzing user behavior, optimizing user experience, and business reporting.

### Partitioning
This table is not partitioned. Queries on this table may experience slower performance due to the lack of partition keys.

### Columns
- **snapshot_date_ist**
  - Data type: date
  - Description: The date of the data snapshot in IST (Indian Standard Time).

- **snapshot_hour_ist**
  - Data type: bigint
  - Description: The hour of the day for the data snapshot in IST.

- **channel**
  - Data type: varchar
  - Description: The channel through which the user accessed the platform (e.g., web, mobile).

- **platform**
  - Data type: varchar
  - Description: The platform (e.g., Android, iOS, Web) used by the customer.

- **city**
  - Data type: varchar
  - Description: The city from which the user accessed the platform.

- **merchant_id**
  - Data type: varchar
  - Description: Identifier for the merchant involved in the transactions or interactions.

- **user_bucket**
  - Data type: varchar
  - Description: Categorization of users into different buckets based on certain criteria (e.g., spending level, frequency of visits).

- **user_type**
  - Data type: varchar
  - Description: Type of user, such as new or returning.

- **daily_active_users**
  - Data type: bigint
  - Description: Count of unique users who were active on the platform on the given day.

- **app_launch**
  - Data type: bigint
  - Description: Number of times the application was launched.

- **home_page_visit**
  - Data type: bigint
  - Description: Number of visits to the home page.

- **home_page_nsa_visit**
  - Data type: bigint
  - Description: Number of non-signed-in visits to the home page.

- **add_to_cart**
  - Data type: bigint
  - Description: Number of times users added items to their shopping cart.

- **cart_visit**
  - Data type: bigint
  - Description: Number of visits to the shopping cart page.

- **login_successful**
  - Data type: bigint
  - Description: Number of successful login attempts.

- **user_registered**
  - Data type: bigint
  - Description: Number of new user registrations.

- **nsa_ping_devices**
  - Data type: bigint
  - Description: Number of non-signed-in devices that pinged the server.

- **address_added**
  - Data type: bigint
  - Description: Number of times users added a new address to their profile.

- **payment_page_visit**
  - Data type: bigint
  - Description: Number of visits to the payment page.

- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of when the ETL process captured this snapshot in IST.

- **login_page_visit**
  - Data type: bigint
  - Description: Number of visits to the login page.

### Potential `JOIN` Keys and Business-Critical Columns
- **merchant_id**: Could potentially be used to join with other merchant-related tables.
- **user_type**, **daily_active_users**, **user_registered**: Critical for analyzing user engagement and growth metrics.

### Potential Relationships
- **merchant_id** suggests a relationship to a merchants table.
- Metrics like **user_registered** and **login_successful** suggest potential joins with user or authentication logs tables.

This documentation provides a structured overview of the table designed for efficient querying and analysis, supporting business intelligence and operational reporting.