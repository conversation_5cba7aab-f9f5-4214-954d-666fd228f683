# Table: blinkit.bistro_etls.thaw_daily_projection_oos_adjustment

### Description
The `blinkit.bistro_etls.thaw_daily_projection_oos_adjustment` table is designed to store daily projections and out-of-stock (OOS) adjustments for products at various merchant outlets. It includes detailed information about product availability, historical out-of-stock hours, and projections based on days of the week, along with adjustments made to inventory levels.

### Partitioning
The table is partitioned on the `updated_at_ts` column. This is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **date_**
  - Data type: date
  - Description: The specific date for the data entry.

- **hour_bucket**
  - Data type: bigint
  - Description: A bucket or range of hours during which data was collected or projected.

- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for the merchant.

- **outlet_id**
  - Data type: varchar
  - Description: Unique identifier for the outlet where the product is sold.

- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant.

- **product_id**
  - Data type: bigint
  - Description: Unique identifier for the product.

- **item_id**
  - Data type: bigint
  - Description: Specific identifier for an item, potentially a SKU.

- **sub_pid_name**
  - Data type: varchar
  - Description: Name of the sub-product or variant.

- **dow**
  - Data type: bigint
  - Description: Day of the week represented as an integer.

- **last_dow_oos_hrs**
  - Data type: real
  - Description: Hours the product was out of stock on the last same weekday.

- **last_dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the product was part of a deal of the day last same weekday.

- **last_2dow_oos_hrs**
  - Data type: real
  - Description: Hours the product was out of stock two same weekdays ago.

- **last_2dow_dotd_flag**
  - Data type: bigint
  - Description: Flag for deal of the day two same weekdays ago.

- **last_3dow_oos_hrs**
  - Data type: real
  - Description: Out of stock hours three same weekdays ago.

- **last_3dow_dotd_flag**
  - Data type: bigint
  - Description: Deal of the day flag for three same weekdays ago.

- **last_4dow_oos_hrs**
  - Data type: real
  - Description: Out of stock hours four same weekdays ago.

- **last_4dow_dotd_flag**
  - Data type: bigint
  - Description: Deal of the day flag for four same weekdays ago.

- **last_5dow_oos_hrs**
  - Data type: real
  - Description: Out of stock hours five same weekdays ago.

- **last_5dow_dotd_flag**
  - Data type: bigint
  - Description: Deal of the day flag for five same weekdays ago.

- **slot**
  - Data type: bigint
  - Description: Time slot for delivery or sales projection.

- **dow_proj_date**
  - Data type: bigint
  - Description: Projected date based on the day of the week.

- **projection_date**
  - Data type: date
  - Description: The date for which projections are made.

- **preparation_type**
  - Data type: varchar
  - Description: Type of preparation required for the product.

- **order_date**
  - Data type: date
  - Description: Date on which the order was placed.

- **pid_to_consider**
  - Data type: bigint
  - Description: Product identifier considered for analysis.

- **pname_to_consider**
  - Data type: varchar
  - Description: Product name considered for analysis.

- **deliver_qty**
  - Data type: bigint
  - Description: Quantity of product delivered.

- **pack_size**
  - Data type: real
  - Description: Size of the product packaging.

- **sold_qty**
  - Data type: real
  - Description: Quantity of product sold.

- **last_non_dotd_dow_oos_hrs**
  - Data type: real
  - Description: Out of stock hours on last non-deal day of the week.

- **last_non_dotd_2dow_oos_hrs**
  - Data type: real
  - Description: Out of stock hours on the second last non-deal day of the week.

- **last_non_dotd_3dow_oos_hrs**
  - Data type: real
  - Description: Out of stock hours on the third last non-deal day of the week.

- **last_non_dotd_4dow_oos_hrs**
  - Data type: real
  - Description: Out of stock hours on the fourth last non-deal day of the week.

- **days_diff**
  - Data type: bigint
  - Description: Difference in days for some context-specific calculation.

- **pid**
  - Data type: bigint
  - Description: Product identifier, potentially used for joining with other tables.

- **sub_pid**
  - Data type: varchar
  - Description: Identifier for a sub-product or variant.

- **min_deliver_qty**
  - Data type: real
  - Description: Minimum quantity that needs to be delivered.

- **adj_quantity**
  - Data type: real
  - Description: Adjusted quantity of the product.

- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update to the record.

### Potential JOIN Keys and Business-Critical Columns
- `merchant_id`, `outlet_id`, `product_id`, `item_id`, `pid` could be used as JOIN keys with other tables that contain merchant, outlet, or product details.
- Business-critical columns include `product_id`, `sold_qty`, `deliver_qty`, `projection_date`, and `updated_at_ts` for tracking inventory and sales performance.

### Potential Relationships
- `product_id` and `pid` might relate to a `products` table.
- `merchant_id` could relate to a `merchants` table.
- `outlet_id` might relate to an `outlets` table.