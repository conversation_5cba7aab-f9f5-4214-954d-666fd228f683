# Table: blinkit.bistro_etls.product_outlet_inventory_oos_details_v1

### Description
The `blinkit.bistro_etls.product_outlet_inventory_oos_details_v1` table stores detailed information about products that are out of stock (OOS) at various outlets on specific dates and times. This data is crucial for analyzing inventory levels, understanding product availability trends, and managing supply chain operations effectively.

### Partitioning
The table is partitioned on the `updated_at_ts` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

### Columns
- **date_**
  - Data type: varchar
  - Description: The date when the inventory status was recorded, likely in YYYY-MM-DD format.

- **frontend_merchant_id**
  - Data type: integer
  - Description: The identifier for the merchant on the frontend system.

- **outlet_id**
  - Data type: varchar
  - Description: The unique identifier of the outlet where the inventory status is being tracked.

- **frontend_merchant_name**
  - Data type: varchar
  - Description: The name of the merchant as it appears on the frontend system.

- **product_id**
  - Data type: integer
  - Description: The identifier for the product whose inventory status is recorded.

- **item_id**
  - Data type: integer
  - Description: A specific identifier for an item, potentially a SKU or a unique item code within the product category.

- **sub_pid_name**
  - Data type: varchar
  - Description: The name or description of a sub-product or variant under the main product identifier.

- **hour_bucket**
  - Data type: integer
  - Description: A categorical representation of the time of day, likely used for analyzing trends during specific hours.

- **hour_fraction**
  - Data type: varchar
  - Description: More granular time detail within the hour, possibly indicating minutes or a part of the hour.

- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: The timestamp when the record was last updated, used for partitioning and ensuring data freshness.

### Key Relationships and JOINs
- **product_id** and **item_id** are potential JOIN keys with other inventory or product tables to fetch detailed product information or historical inventory data.
- **frontend_merchant_id** can be used to join with merchant tables to retrieve additional merchant details.

### Business-Critical Columns
- **product_id**: Essential for identifying which products are frequently out of stock.
- **updated_at_ts**: Critical for temporal analysis and ensuring queries are efficient due to partitioning.
- **outlet_id**: Important for location-based inventory analysis.

This documentation should serve as a foundational component for querying and analysis in the bedrock knowledge base, facilitating efficient data operations and insights.