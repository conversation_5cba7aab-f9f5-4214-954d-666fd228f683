# Table: blinkit.bistro_etls.vendor_master_v1

### Description
The `blinkit.bistro_etls.vendor_master_v1` table stores detailed information about vendors, including their identification, name, storage capacities, and specific ordering days. This table is essential for managing vendor relationships and logistics within the Bistro module of Blinkit.

### Partitioning
The table is not partitioned. This may affect performance when querying large datasets.

### Columns
- **vendor_id**
  - Data type: varchar
  - Description: A unique identifier for each vendor.

- **vendor_name**
  - Data type: varchar
  - Description: The name of the vendor.

- **max_storage_capacity**
  - Data type: varchar
  - Description: The maximum storage capacity that the vendor can handle, likely measured in units relevant to the business.

- **blast_freeze_capacity**
  - Data type: varchar
  - Description: The capacity of the vendor to blast freeze products, indicating a specific type of storage capability.

- **ordering_day1**
  - Data type: varchar
  - Description: One of the days designated for placing orders with the vendor.

- **ordering_day2**
  - Data type: varchar
  - Description: Another day designated for placing orders with the vendor.

- **updated_at**
  - Data type: varchar
  - Description: The timestamp of the last update made to the vendor's record.

### Key Relationships and JOINs
- The `vendor_id` column is a potential JOIN key, as it can be used to link this table with other tables that reference vendors, such as purchase orders or vendor performance evaluations.

### Business-Critical Columns
- **vendor_id**: Essential for uniquely identifying vendors across the database.
- **vendor_name**: Important for human-readable reports and communications.
- **max_storage_capacity** and **blast_freeze_capacity**: Critical for understanding and managing inventory and storage capabilities of vendors.

### Potential Relationships
- This table could be related to tables like `order_details`, `inventory_management`, or `vendor_performance`, where `vendor_id` would serve as a common key to establish these relationships.

This documentation provides a clear overview of the `blinkit.bistro_etls.vendor_master_v1` table, ensuring efficient use and maintenance of the data within the Blinkit infrastructure.