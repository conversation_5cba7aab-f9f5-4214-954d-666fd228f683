# Table: blinkit.bistro_etls.pre_cook_item_wise_dow_daily

### Description
The `blinkit.bistro_etls.pre_cook_item_wise_dow_daily` table appears to be designed for tracking daily sales and task-related data for items sold at different outlets on specific days of the week. It likely serves as an analytical tool to monitor and predict sales trends based on historical data, aiding in inventory and task management.

### Partitioning
The table is partitioned on the column `updated_at_ts`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval and management.

### Columns
- **date_**
  - Data type: date
  - Description: The date on which the data was recorded.

- **outlet_id**
  - Data type: integer
  - Description: The unique identifier for the outlet.

- **outlet_name**
  - Data type: varchar
  - Description: The name of the outlet.

- **id**
  - Data type: integer
  - Description: A unique identifier for the record.

- **sub_name**
  - Data type: varchar
  - Description: The name of the subcategory of items.

- **hr_**
  - Data type: integer
  - Description: The hour of the day when the data was recorded.

- **sold_quantity**
  - Data type: integer
  - Description: The quantity of the item sold on the recorded date.

- **task_qty**
  - Data type: integer
  - Description: The quantity of the item that was assigned for tasks.

- **last_dow_sold_quantity**
  - Data type: integer
  - Description: The quantity sold on the last same day of the week.

- **last_2dow_sold_quantity**
  - Data type: integer
  - Description: The quantity sold two same days of the week ago.

- **last_3dow_sold_quantity**
  - Data type: integer
  - Description: The quantity sold three same days of the week ago.

- **last_4dow_sold_quantity**
  - Data type: integer
  - Description: The quantity sold four same days of the week ago.

- **dow**
  - Data type: integer
  - Description: The day of the week represented as an integer (e.g., 1 for Monday).

- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: The timestamp when the record was last updated.

### Potential JOIN Keys and Business-Critical Columns
- **outlet_id**: Can be used to join with other tables containing outlet-specific information.
- **date_**: Important for trend analysis and can be used to join with other date-specific datasets.
- **id**: Primary key for this table, crucial for unique identification of records.

### Potential Relationships
- The `outlet_id` might be used to join with other tables that contain more detailed information about the outlets, such as location or size.
- The `date_` and `dow` columns suggest potential joins with tables that include promotional or event data to analyze impacts on sales.

This documentation provides a structured overview to facilitate efficient querying and analysis, supporting the maintenance of a robust bedrock knowledge base.