# Table: blinkit.bistro_etls.bistro_supply_category

### Description
The `blinkit.bistro_etls.bistro_supply_category` table stores information about various supply categories within different cities, categorized by levels. This table is likely used for managing and analyzing the distribution and categorization of supplies across different regions.

### Partitioning
The table is partitioned on the `level` column. This column is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

### Columns
- **final_supply_id**
  - Data type: integer
  - Description: A unique identifier for the supply entry.

- **final_supply_name**
  - Data type: varchar
  - Description: The name of the supply item.

- **city_name**
  - Data type: varchar
  - Description: The name of the city where the supply is located.

- **supply_category**
  - Data type: varchar
  - Description: The category of the supply which helps in classifying the type of items.

- **level**
  - Data type: varchar
  - Description: The level of supply which could indicate the priority or scale of distribution.

### Potential Relationships and JOIN Keys
- The `final_supply_id` could be used as a JOIN key if there are other tables in the database that reference this identifier, such as order details or inventory logs.
- The `city_name` might relate to a geographical or regional table that contains more detailed information about each city.
- The `supply_category` could potentially link to a category details table that describes each category more thoroughly.

### Business-Critical Columns
- **final_supply_id**: Essential for uniquely identifying each supply record.
- **supply_category**: Important for categorization and analysis of supply types.
- **level**: Critical for partitioning and possibly for business logic related to supply management strategies.