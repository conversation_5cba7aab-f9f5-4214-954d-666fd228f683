# Table: blinkit.bistro_etls.fact_sales_order_details_bistro

### Description
The `blinkit.bistro_etls.fact_sales_order_details_bistro` table is designed to store detailed transactional data for each sales order processed by the system. It includes comprehensive information about orders, customers, pricing, discounts, payment methods, and logistical details. This table is essential for analyzing sales performance, customer behavior, and operational efficiency.

### Partitioning
The table is partitioned on the `order_create_dt_ist` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

### Columns
- **order_id**
  - Data type: bigint
  - Description: Unique identifier for each order.
  
- **cart_id**
  - Data type: bigint
  - Description: Unique identifier for the shopping cart used in the order.
  
- **dim_customer_key**
  - Data type: bigint
  - Description: Reference key linking to the customer dimension table.
  
- **dim_customer_address_key**
  - Data type: bigint
  - Description: Reference key linking to the customer's address details in a dimension table.
  
- **dim_frontend_merchant_key**
  - Data type: bigint
  - Description: Reference key for identifying the frontend merchant associated with the order.
  
- **total_product_quantity**
  - Data type: bigint
  - Description: Total quantity of products included in the order.
  
- **total_procured_quantity**
  - Data type: bigint
  - Description: Total quantity of products procured for the order.
  
- **total_mrp**
  - Data type: real
  - Description: Total maximum retail price of all products in the order.
  
- **total_selling_price**
  - Data type: real
  - Description: Total selling price of all products after discounts.
  
- **total_procurement_price**
  - Data type: real
  - Description: Total cost price of products procured for the order.
  
- **total_discount_amount**
  - Data type: real
  - Description: Total discount amount applied to the order.
  
- **total_cashback_amount**
  - Data type: real
  - Description: Total cashback amount provided on the order.
  
- **total_additional_charges_amount**
  - Data type: real
  - Description: Total of additional charges applied to the order.
  
- **total_delivery_cost**
  - Data type: real
  - Description: Total cost associated with the delivery of the order.
  
- **total_weighted_landing_price**
  - Data type: real
  - Description: Weighted average landing cost of all products in the order.
  
- **total_brand_fund**
  - Data type: real
  - Description: Total funding received from brands for the order.
  
- **total_retained_margin**
  - Data type: real
  - Description: Total margin retained after all deductions and costs.
  
- **promo_code**
  - Data type: varchar
  - Description: Promotional code used by the customer.
  
- **channel**
  - Data type: varchar
  - Description: Sales channel through which the order was placed.
  
- **payment_method**
  - Data type: varchar
  - Description: Method of payment used for the order.
  
- **device_id**
  - Data type: varchar
  - Description: Device identifier from which the order was placed.
  
- **app_version**
  - Data type: varchar
  - Description: Version of the application used to place the order.
  
- **order_type**
  - Data type: varchar
  - Description: Type of order, such as standard, express, etc.
  
- **order_current_status**
  - Data type: varchar
  - Description: Current status of the order, such as pending, delivered, cancelled.
  
- **cart_checkout_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the cart was checked out in IST timezone.
  
- **cart_checkout_dim_date_key**
  - Data type: varchar
  - Description: Date key for the cart checkout event.
  
- **order_create_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the order was created in IST timezone.
  
- **order_create_dim_date_key**
  - Data type: varchar
  - Description: Date key for the order creation event.
  
- **order_schedule_ts_ist**
  - Data type: timestamp(6)
  - Description: Scheduled timestamp for the order in IST timezone.
  
- **order_schedule_dim_date_key**
  - Data type: varchar
  - Description: Date key for the order scheduling event.
  
- **order_schedule_slot**
  - Data type: varchar
  - Description: Scheduled time slot for order delivery.
  
- **order_deliver_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the order was delivered in IST timezone.
  
- **order_deliver_dim_date_key**
  - Data type: varchar
  - Description: Date key for the order delivery event.
  
- **order_cancel_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the order was cancelled in IST timezone.
  
- **order_cancel_dim_date_key**
  - Data type: varchar
  - Description: Date key for the order cancellation event.
  
- **total_liquid_fund**
  - Data type: real
  - Description: Total liquid funds allocated for the order.
  
- **slot_charges**
  - Data type: real
  - Description: Charges applied for the selected delivery slot.
  
- **packaging_cost**
  - Data type: real
  - Description: Total cost incurred for packaging the order.
  
- **tip_amount**
  - Data type: real
  - Description: Total tip amount given by the customer.
  
- **total_self_fund**
  - Data type: real
  - Description: Total funds provided by the company itself for the order.
  
- **city_name**
  - Data type: varchar
  - Description: Name of the city where the order was placed.
  
- **station_name**
  - Data type: varchar
  - Description: Name of the station or local area from where the order was serviced.
  
- **frontend_merchant_id**
  - Data type: bigint
  - Description: Unique identifier for the frontend merchant.
  
- **dim_promotion_key**
  - Data type: bigint
  - Description: Reference key linking to the promotions applied to the order.
  
- **is_internal_order**
  - Data type: boolean
  - Description: Flag indicating whether the order is internal.
  
- **order_external_id**
  - Data type: varchar
  - Description: External identifier for the order, used for integration with external systems.
  
- **total_net_cost**
  - Data type: real
  - Description: Net cost of the order after all adjustments.
  
- **order_rating**
  - Data type: bigint
  - Description: Customer's rating for the order.
  
- **outlet_id**
  - Data type: bigint
  - Description: Identifier for the outlet from which the order was dispatched.
  
- **total_doorstep_return_quantity**
  - Data type: real
  - Description: Quantity of products returned at the doorstep.
  
- **total_doorstep_return_selling_price**
  - Data type: real
  - Description: Selling price of the products returned at the doorstep.
  
- **convenience_charge**
  - Data type: real
  - Description: Convenience charge applied to the order.
  
- **total_cart_level_discount_amount**
  - Data type: real
  - Description: Total discount amount applied at the cart level.
  
- **total_net_discount_amount**
  - Data type: real
  - Description: Net discount amount after all calculations.
  
- **org_channel_id**
  - Data type: varchar
  - Description: Original channel identifier where the order was initiated.
  
- **org_channel_name**
  - Data type: varchar
  - Description: Name of the original channel where the order was initiated.
  
- **night_charge**
  - Data type: real
  - Description: Additional charge applied for orders delivered at night.
  
- **other_charges**
  - Data type: real
  - Description: Miscellaneous charges applied to the order.
  
- **polygon_type**
  - Data type: varchar
  - Description: Type of geographical area from which the order was placed.
  
- **etl_update_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the ETL process last updated this record.
  
- **order_create_dt_ist**
  - Data type: date
  - Description: Date when the order was created, used for partitioning.
  
- **is_surge_order**
  - Data type: boolean
  - Description: Indicates if the order was placed during a surge pricing time.
  
- **surge_reason**
  - Data type: varchar
  - Description: Reason for surge pricing being applied to the order.
  
- **is_batched_order**
  - Data type: boolean
  - Description: Indicates if the order was part of a batched delivery.
  
- **is_easy_return_order**
  - Data type: boolean
  - Description: Indicates if the order qualifies for easy returns.
  
- **etl_oo_update_ts**
  - Data type: timestamp(6)
  - Description: Timestamp when the order was last updated in the ETL process.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys:** `order_id`, `dim_customer_key`, `dim_customer_address_key`, `dim_frontend_merchant_key`, `dim_promotion_key`
- **Business-Critical Columns:** `order_id`, `total_selling_price`, `total_discount_amount`, `order_current_status`, `payment_method`

### Potential Relationships to Other Tables
- **dim_customer_key** could be used to join with a customer dimension table.
- **dim_customer_address_key** might link to a customer address dimension table.
- **dim_frontend_merchant_key** could join with a merchant dimension table.
- **dim_promotion_key** might link to a promotions table.
- **order_id** could be used to join with other order-related fact tables or logs.