# Table: blinkit.bistro_etls.sandwich_projections_logs_daily

### Description
The `blinkit.bistro_etls.sandwich_projections_logs_daily` table is designed to store daily logs of sandwich sales projections at various merchant outlets. It includes detailed records of sales data, projections, and metrics related to product performance over different days of the week. This table likely supports business intelligence and analytics for inventory management, sales forecasting, and operational planning.

### Partitioning
The table is partitioned on the `updated_at_ts` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **order_date**
  - Data type: varchar
  - Description: The date on which the order was placed.

- **merchant_id**
  - Data type: integer
  - Description: Unique identifier for the merchant.

- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant.

- **product_id**
  - Data type: integer
  - Description: Unique identifier for the product.

- **product_name**
  - Data type: varchar
  - Description: Name of the product.

- **outlet_id**
  - Data type: integer
  - Description: Unique identifier for the outlet where the product is sold.

- **dow**
  - Data type: integer
  - Description: Day of the week represented as an integer.

- **gmv**
  - Data type: double
  - Description: Gross Merchandise Value associated with the product sales.

- **quantity**
  - Data type: integer
  - Description: Quantity of the product sold.

- **last_dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold on the last similar day of the week.

- **last_dow_dotd_flag**
  - Data type: integer
  - Description: Flag indicating if the product was part of a deal of the day last similar weekday.

- **last_2dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold two similar days of the week ago.

- **last_2dow_dotd_flag**
  - Data type: integer
  - Description: Flag indicating if the product was part of a deal of the day two similar weekdays ago.

- **last_3dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold three similar days of the week ago.

- **last_3dow_dotd_flag**
  - Data type: integer
  - Description: Flag indicating if the product was part of a deal of the day three similar weekdays ago.

- **last_4dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold four similar days of the week ago.

- **last_4dow_dotd_flag**
  - Data type: integer
  - Description: Flag indicating if the product was part of a deal of the day four similar weekdays ago.

- **last_5dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold five similar days of the week ago.

- **last_5dow_dotd_flag**
  - Data type: integer
  - Description: Flag indicating if the product was part of a deal of the day five similar weekdays ago.

- **store_age_in_days**
  - Data type: varchar
  - Description: Age of the store in days.

- **opd**
  - Data type: varchar
  - Description: Operational performance data.

- **projection_date**
  - Data type: varchar
  - Description: The date for which sales are projected.

- **dow_proj_date**
  - Data type: integer
  - Description: Day of the week for the projection date.

- **expected_start_time**
  - Data type: varchar
  - Description: Expected start time for sales on the projection date.

- **expected_end_time**
  - Data type: varchar
  - Description: Expected end time for sales on the projection date.

- **preparation_time**
  - Data type: integer
  - Description: Time required to prepare the product.

- **preparation_type**
  - Data type: varchar
  - Description: Type of preparation needed for the product.

- **sub_product_id**
  - Data type: integer
  - Description: Identifier for a sub-category of the product.

- **sub_pid_name**
  - Data type: varchar
  - Description: Name of the sub-product.

- **last_non_dotd_dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold on the last non-deal day similar weekday.

- **last_non_dotd_2dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold two non-deal days similar weekdays ago.

- **last_non_dotd_3dow_sold_quantity**
  - Data type: integer
  - Description: Quantity sold three non-deal days similar weekdays ago.

- **last_2_dow_growth_rate**
  - Data type: double
  - Description: Growth rate in sales from the last two similar days of the week.

- **last_dow_growth_rate**
  - Data type: double
  - Description: Growth rate in sales from the last similar day of the week.

- **row_median_growth**
  - Data type: double
  - Description: Median growth rate across rows.

- **last_2_dow_median_sold_quantity**
  - Data type: double
  - Description: Median quantity sold across the last two similar days of the week.

- **last_3_dow_stdev_sold_quantity**
  - Data type: double
  - Description: Standard deviation of quantities sold across the last three similar days of the week.

- **is_strictly_increasing**
  - Data type: boolean
  - Description: Boolean flag indicating if the sales trend is strictly increasing.

- **is_strictly_decreasing**
  - Data type: boolean
  - Description: Boolean flag indicating if the sales trend is strictly decreasing.

- **projected_increasing**
  - Data type: double
  - Description: Projected increase in sales.

- **increasing_cap1**
  - Data type: double
  - Description: First cap or limit for projected sales increase.

- **increasing_cap2**
  - Data type: double
  - Description: Second cap or limit for projected sales increase.

- **projected_increasing_updated**
  - Data type: double
  - Description: Updated projection for increasing sales.

- **projected_decreasing**
  - Data type: double
  - Description: Projected decrease in sales.

- **projected_default**
  - Data type: integer
  - Description: Default projected sales figure.

- **final_projected**
  - Data type: double
  - Description: Final projected sales figure.

- **final_projected_updated**
  - Data type: double
  - Description: Updated final projected sales figure.

- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update to the record.

- **last_3_dow_median_sold_quantity**
  - Data type: double
  - Description: Median quantity sold across the last three similar days of the week.

- **last_4_non_dotd_similar_day_type_median_sold_quantity**
  - Data type: double
  - Description: Median quantity sold across the last four non-deal similar days.

- **increasing_cap3**
  - Data type: double
  - Description: Third cap or limit for projected sales increase.

- **decreasing_cap1**
  - Data type: double
  - Description: First cap or limit for projected sales decrease.

- **projected_decreasing_updated**
  - Data type: double
  - Description: Updated projection for decreasing sales.

- **decreasing_cap2**
  - Data type: double
  - Description: Second cap or limit for projected sales decrease.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id**, **outlet_id**, and **product_id** are potential JOIN keys for linking with other merchant, outlet, or product-related tables.
- Business-critical columns include **gmv**, **quantity**, **projection_date**, and **final_projected** which are essential for analyzing sales performance and forecasting.

### Potential Relationships
- **product_id** and **sub_product_id** suggest a hierarchical relationship between products and their sub-categories.
- **merchant_id** could be used to join with a merchant table containing more detailed merchant information.
- **outlet_id** might link to an outlet table detailing location and other outlet specifics.