# Table: blinkit.bistro_etls.thaw_item_wise_dow_logs_daily

### Description
The `blinkit.bistro_etls.thaw_item_wise_dow_logs_daily` table is designed to store daily logs of item-wise thawing activities across different merchants and outlets. It captures detailed records of procured and sold quantities of prepared products, along with historical sales data for the same day of the week (DOW) over the last four weeks. This table is likely used for analyzing product performance, inventory management, and forecasting demand based on day-of-week trends.

### Partitioning
The table is partitioned on the `updated_at_ts` column. This is CRITICAL for query performance and this column MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **date_**
  - Data type: date
  - Description: The date for which the log entry is recorded.

- **slot**
  - Data type: varchar
  - Description: Time slot of the day when the log entry was recorded.

- **merchant_id**
  - Data type: integer
  - Description: Unique identifier for the merchant.

- **outlet_id**
  - Data type: integer
  - Description: Unique identifier for the outlet where the product was sold or procured.

- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant.

- **prepared_product_id**
  - Data type: integer
  - Description: Unique identifier for the prepared product.

- **thawed_name**
  - Data type: varchar
  - Description: Name of the thawed product.

- **procured_quantity**
  - Data type: integer
  - Description: Quantity of the product procured.

- **sold_quantity**
  - Data type: integer
  - Description: Quantity of the product sold on the recorded date.

- **last_dow_sold_quantity**
  - Data type: integer
  - Description: Quantity of the product sold on the same day of the week in the previous week.

- **last_2dow_sold_quantity**
  - Data type: integer
  - Description: Quantity of the product sold on the same day of the week two weeks ago.

- **last_3dow_sold_quantity**
  - Data type: integer
  - Description: Quantity of the product sold on the same day of the week three weeks ago.

- **last_4dow_sold_quantity**
  - Data type: integer
  - Description: Quantity of the product sold on the same day of the week four weeks ago.

- **dow**
  - Data type: integer
  - Description: Day of the week represented as an integer (e.g., 1 for Monday, 2 for Tuesday, etc.).

- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update to the log entry.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id** and **outlet_id** can be used as JOIN keys to link with other tables containing merchant or outlet details.
- **prepared_product_id** might be used to join with product tables for detailed product information.
- **date_** and **dow** are critical for time-series analysis and trend observation.

### Potential Relationships to Other Tables
- The `prepared_product_id` could link to a `products` table for detailed product descriptions and specifications.
- `merchant_id` and `outlet_id` could link to `merchants` and `outlets` tables respectively for additional merchant and outlet information.