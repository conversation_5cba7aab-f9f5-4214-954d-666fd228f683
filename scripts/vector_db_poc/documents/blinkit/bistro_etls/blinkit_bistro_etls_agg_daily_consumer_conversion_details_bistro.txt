# Table: blinkit.bistro_etls.agg_daily_consumer_conversion_details_bistro

### Description
The `blinkit.bistro_etls.agg_daily_consumer_conversion_details_bistro` table aggregates daily consumer conversion metrics across various stages of the user journey on the platform. It tracks user interactions from app launch to checkout, segmented by different channels, platforms, and cities. This data is crucial for analyzing user behavior, optimizing user experience, and improving conversion rates.

### Partitioning
This table is not partitioned. Queries on this table may experience slower performance due to the lack of partition keys, and careful indexing and query optimization are recommended.

### Columns
- **snapshot_date_ist**
  - Data type: date
  - Description: The date of the data snapshot in IST timezone.

- **snapshot_hour_ist**
  - Data type: integer
  - Description: The hour of the day for the data snapshot in IST timezone.

- **channel**
  - Data type: varchar
  - Description: The channel through which the user accessed the platform (e.g., web, mobile).

- **platform**
  - Data type: varchar
  - Description: The type of platform used by the user (e.g., Android, iOS).

- **city**
  - Data type: varchar
  - Description: The city from which the user accessed the platform.

- **merchant_id**
  - Data type: varchar
  - Description: Identifier for the merchant involved in the transactions.

- **user_bucket**
  - Data type: varchar
  - Description: Categorization of users into different buckets based on defined criteria.

- **user_type**
  - Data type: varchar
  - Description: Type of user, such as new or returning.

- **daily_active_users**
  - Data type: bigint
  - Description: Count of unique users active on the platform for the given day.

- **app_launch**
  - Data type: bigint
  - Description: Number of times the application was launched.

- **home_page_visit**
  - Data type: bigint
  - Description: Number of visits to the home page.

- **home_page_nsa_visit**
  - Data type: bigint
  - Description: Number of non-significant action visits to the home page.

- **add_to_cart**
  - Data type: bigint
  - Description: Number of times users added items to their shopping cart.

- **cart_visit**
  - Data type: bigint
  - Description: Number of visits to the shopping cart page.

- **user_registered**
  - Data type: bigint
  - Description: Number of users who registered on the platform.

- **nsa_pings**
  - Data type: bigint
  - Description: Count of non-significant actions performed by users.

- **login_successful**
  - Data type: bigint
  - Description: Number of successful login attempts.

- **address_added**
  - Data type: bigint
  - Description: Number of times users added a new address to their profile.

- **payment_page_visit**
  - Data type: bigint
  - Description: Number of visits to the payment page.

- **app_launch_to_home_page_visit**
  - Data type: bigint
  - Description: Conversion metric from app launch to home page visit.

- **dau_to_home_page_visit**
  - Data type: bigint
  - Description: Ratio of daily active users to home page visits.

- **app_launch_to_home_page_visit_nsa**
  - Data type: bigint
  - Description: Conversion metric from app launch to non-significant action home page visits.

- **dau_to_atc**
  - Data type: bigint
  - Description: Ratio of daily active users who added items to cart.

- **atc_to_cv**
  - Data type: bigint
  - Description: Conversion from add to cart to cart visit.

- **cv_to_user_registered**
  - Data type: bigint
  - Description: Conversion from cart visit to user registration.

- **cv_to_address_added**
  - Data type: bigint
  - Description: Conversion from cart visit to address addition.

- **cv_to_payment**
  - Data type: bigint
  - Description: Conversion from cart visit to payment page visit.

- **payment_to_checkout**
  - Data type: bigint
  - Description: Conversion from payment initiation to checkout completion.

- **cv_to_checkout**
  - Data type: bigint
  - Description: Conversion from cart visit to checkout completion.

- **overall_conversion**
  - Data type: bigint
  - Description: Overall conversion rate across all tracked activities.

- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the ETL process snapshot in IST timezone.

- **login_page_visit**
  - Data type: bigint
  - Description: Number of visits to the login page.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id** could be used to join with merchant-related tables.
- Business-critical columns include **daily_active_users**, **app_launch**, **add_to_cart**, **user_registered**, **payment_page_visit**, and **overall_conversion** which are essential for analyzing the effectiveness of the platform in engaging users and driving conversions.

### Potential Relationships
- **merchant_id** suggests a relationship with a merchant table that contains details about each merchant.
- **user_type** could relate to a user demographics table that categorizes user types.
- Conversion metrics (like **app_launch_to_home_page_visit**) might correlate with marketing campaign data to evaluate campaign effectiveness.