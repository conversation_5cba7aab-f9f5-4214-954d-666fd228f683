# Table: blinkit.bistro_etls.inventory_wastage_logs

### Description
The `blinkit.bistro_etls.inventory_wastage_logs` table is designed to log inventory wastage events across various outlets. It records detailed information about items, their variants, and the reasons for wastage, along with quantitative and financial impacts. This table is crucial for analyzing wastage patterns, identifying potential areas for improvement in inventory management, and reducing losses.

### Partitioning
The table is partitioned by the `date` column. This is CRITICAL for query performance and the `date` column MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **date**
  - Data type: varchar
  - Description: The date on which the wastage event was logged.

- **item_id**
  - Data type: bigint
  - Description: Unique identifier for the item.

- **item_name**
  - Data type: varchar
  - Description: Name of the item.

- **variant_id**
  - Data type: varchar
  - Description: Identifier for the variant of the item.

- **converted_pid**
  - Data type: bigint
  - Description: Converted product identifier possibly used for internal tracking.

- **converted_marked_l1**
  - Data type: varchar
  - Description: First level of marked conversion categorization.

- **converted_marked_l2**
  - Data type: varchar
  - Description: Second level of marked conversion categorization.

- **outlet_id**
  - Data type: integer
  - Description: Identifier for the outlet where the wastage occurred.

- **outlet_name**
  - Data type: varchar
  - Description: Name of the outlet.

- **event_name**
  - Data type: varchar
  - Description: Name of the event associated with the wastage.

- **wastage_flag**
  - Data type: varchar
  - Description: Flag indicating if the log entry is marked as wastage.

- **sub_reason_code**
  - Data type: varchar
  - Description: Code for the sub-reason of wastage.

- **inv_state**
  - Data type: varchar
  - Description: State of the inventory at the time of logging.

- **flag_supply_or_conv_mapped**
  - Data type: integer
  - Description: Flag indicating whether the supply or conversion is mapped.

- **system_manual_flag**
  - Data type: varchar
  - Description: Flag indicating whether the entry was system generated or manually entered.

- **grn_wlp**
  - Data type: double
  - Description: Good Receipt Note Weight Loss Percentage.

- **others_quantity**
  - Data type: bigint
  - Description: Quantity of wastage categorized under 'others'.

- **bistro_audit_subreason_code_quantity**
  - Data type: bigint
  - Description: Quantity related to the bistro audit subreason.

- **mishandled_at_facility_subreason_quantity**
  - Data type: bigint
  - Description: Quantity of items mishandled at the facility.

- **vendor_pdt_damage_quantity**
  - Data type: bigint
  - Description: Quantity of products damaged by the vendor.

- **thawing_subreason_code_quantity**
  - Data type: bigint
  - Description: Quantity affected due to thawing issues.

- **missing_qty_found_subreason_quantity**
  - Data type: bigint
  - Description: Quantity found that was previously marked as missing.

- **int_trials_subreason_quantity**
  - Data type: bigint
  - Description: Quantity used in internal trials.

- **sys_inaccu_subreason_quantity**
  - Data type: bigint
  - Description: Quantity discrepancy due to system inaccuracies.

- **manual_error_subreason_quantity**
  - Data type: bigint
  - Description: Quantity affected due to manual errors.

- **qty_missing_subreason_quantity**
  - Data type: bigint
  - Description: Quantity that went missing due to various subreasons.

- **business_req_subreason_quantity**
  - Data type: bigint
  - Description: Quantity adjusted due to business requirements.

- **others_value**
  - Data type: double
  - Description: Financial value associated with 'others' category wastage.

- **bistro_audit_subreason_code_value**
  - Data type: double
  - Description: Financial value related to the bistro audit subreason.

- **mishandled_at_facility_subreason_value**
  - Data type: double
  - Description: Financial value of items mishandled at the facility.

- **vendor_pdt_damage_value**
  - Data type: double
  - Description: Financial value of products damaged by the vendor.

- **thawing_subreason_code_value**
  - Data type: double
  - Description: Financial value affected due to thawing issues.

- **missing_qty_found_subreason_value**
  - Data type: double
  - Description: Financial value of the quantity found that was previously marked as missing.

- **int_trials_subreason_value**
  - Data type: double
  - Description: Financial value used in internal trials.

- **sys_inaccu_subreason_value**
  - Data type: double
  - Description: Financial value discrepancy due to system inaccuracies.

- **manual_error_subreason_value**
  - Data type: double
  - Description: Financial value affected due to manual errors.

- **qty_missing_subreason_value**
  - Data type: double
  - Description: Financial value of the quantity that went missing due to various subreasons.

- **business_req_subreason_value**
  - Data type: double
  - Description: Financial value adjusted due to business requirements.

### Potential JOIN Keys and Business-Critical Columns
- `item_id` and `variant_id` could be used to join with product tables.
- `outlet_id` could be used to join with outlet information tables.
- Business-critical columns include `item_id`, `outlet_id`, `wastage_flag`, and financial values like `others_value`, `vendor_pdt_damage_value`, which are crucial for financial and inventory analysis.

### Potential Relationships
- This table could relate to a products table via `item_id` or `variant_id`.
- It could relate to an outlets table via `outlet_id`.
- Financial and inventory management systems would likely integrate with this table for reporting and analysis purposes.