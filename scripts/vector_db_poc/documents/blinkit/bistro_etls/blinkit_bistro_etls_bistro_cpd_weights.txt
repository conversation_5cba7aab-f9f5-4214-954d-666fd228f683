# Table: blinkit.bistro_etls.bistro_cpd_weights

### Description
The `blinkit.bistro_etls.bistro_cpd_weights` table is designed to store computed weights and sales projections for various dishes across different outlets. It likely serves as a critical component for inventory and sales forecasting in the food service industry, helping to optimize stock levels and understand consumer demand patterns.

### Partitioning
The table is partitioned by the `projection_date` column. This is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

### Columns
- **outlet_id**
  - Data type: integer
  - Description: Identifier for the outlet where the dish is sold.

- **dish_item_id**
  - Data type: integer
  - Description: Unique identifier for a specific dish item.

- **projection_date**
  - Data type: date
  - Description: The date for which the sales projection is calculated.

- **creation_date**
  - Data type: date
  - Description: The date on which the record was created.

- **final_weighted_sum**
  - Data type: double
  - Description: The final computed weight sum for the dish projection.

- **total_qty_sold**
  - Data type: integer
  - Description: Total quantity of the dish sold.

- **dish_pid**
  - Data type: integer
  - Description: Product identifier for the dish, potentially used for linking with product tables.

- **merchant_id**
  - Data type: integer
  - Description: Identifier for the merchant selling the dish.

- **l1**
  - Data type: varchar
  - Description: Level 1 categorization of the dish.

- **l2**
  - Data type: varchar
  - Description: Level 2 categorization of the dish.

- **p_type**
  - Data type: varchar
  - Description: Type of the projection used.

- **item_name**
  - Data type: varchar
  - Description: Name of the dish item.

- **percentile_category**
  - Data type: varchar
  - Description: Category of the dish based on percentile ranking.

- **linear**
  - Data type: double
  - Description: Linear weight calculation for the dish.

- **exponential**
  - Data type: double
  - Description: Exponential weight calculation for the dish.

- **parabolic**
  - Data type: double
  - Description: Parabolic weight calculation for the dish.

- **weighted_avg_30_70**
  - Data type: double
  - Description: Weighted average calculated with a 30/70 ratio.

- **weighted_avg_40_60**
  - Data type: double
  - Description: Weighted average calculated with a 40/60 ratio.

- **weighted_avg_50_50**
  - Data type: double
  - Description: Weighted average calculated with a 50/50 ratio.

- **recency_weight**
  - Data type: double
  - Description: Weight based on the recency of sales data.

- **normalised_recency_weight**
  - Data type: double
  - Description: Normalized weight based on the recency of sales data.

- **dow_weight**
  - Data type: double
  - Description: Weight based on the day of the week.

- **normalized_dow_weight**
  - Data type: double
  - Description: Normalized weight based on the day of the week.

- **recency_dow_weight**
  - Data type: double
  - Description: Combined weight of recency and day of the week factors.

- **normalised_recency_dow_weight**
  - Data type: double
  - Description: Normalized combined weight of recency and day of the week factors.

- **weighted_avg_40_60_rec_dow**
  - Data type: double
  - Description: Weighted average considering both 40/60 ratio and day of the week recency factors.

- **sum_weighted_avg_40_60_rec_dow**
  - Data type: double
  - Description: Sum of weighted averages considering both 40/60 ratio and day of the week recency factors.

- **cpd**
  - Data type: double
  - Description: Cumulative probability distribution value for the dish.

### Potential JOIN Keys and Business-Critical Columns
- `dish_item_id` and `dish_pid` could be used to join with product or inventory tables.
- `merchant_id` might link to merchant or vendor tables.
- Business-critical columns include `final_weighted_sum`, `total_qty_sold`, and `projection_date` for forecasting and inventory management.