# Table: blinkit.bistro_etls.supply_consumption_mapping_temp

### Description
The `blinkit.bistro_etls.supply_consumption_mapping_temp` table is designed to track the consumption of supplies at various outlets over specific periods. It records details about the quantity of supplies consumed, the duration of consumption, and the stock levels during that period. This table is likely used for inventory management, supply chain optimization, and financial forecasting related to supply usage.

### Partitioning
This table is not partitioned. Queries on this table may not be optimized for performance unless indexed appropriately on frequently queried columns.

### Columns
- **outlet_id**
    - Data type: integer
    - Description: The unique identifier for an outlet where supplies are consumed.

- **converted_id**
    - Data type: integer
    - Description: A possibly transformed or adjusted identifier for internal tracking or reference.

- **supply_id**
    - Data type: integer
    - Description: The unique identifier for a type of supply being consumed.

- **consumption_start_dt**
    - Data type: timestamp(6)
    - Description: The start date and time for the consumption period of the supply.

- **consumption_end_dt**
    - Data type: timestamp(6)
    - Description: The end date and time for the consumption period of the supply.

- **quantity_consumed**
    - Data type: real
    - Description: The amount of supply consumed during the specified period, measured in units appropriate to the supply.

- **days_in_stock**
    - Data type: integer
    - Description: The number of days the supply was in stock during the consumption period.

- **insert_dt_ist**
    - Data type: timestamp(6)
    - Description: The timestamp when the consumption data was recorded or inserted into the table, in Indian Standard Time (IST).

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `outlet_id`, `supply_id` could be used to join with other tables that contain detailed information about outlets or supplies, respectively.
- **Business-Critical Columns**: `quantity_consumed`, `consumption_start_dt`, `consumption_end_dt` are critical for analyzing supply consumption patterns and inventory management.

### Potential Relationships
- The `outlet_id` might relate to an `outlets` table that contains more detailed information about each outlet.
- The `supply_id` might relate to a `supplies` table that details each type of supply, including name, type, and supplier information.