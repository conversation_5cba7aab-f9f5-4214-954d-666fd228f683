# Table: blinkit.bistro_etls.logs_daily_impression_bistro_on_blinkit_v2

### Description
The table `blinkit.bistro_etls.logs_daily_impression_bistro_on_blinkit_v2` is designed to store daily logs of user interactions and impressions within the Bistro application on the Blinkit platform. It captures detailed information about user activities, device specifics, and application usage patterns, which are essential for analyzing user behavior, enhancing user experience, and targeted marketing.

### Partitioning
The table is partitioned on the columns `at_date_ist` and `name`. These are CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **at_date_ist**
  - Data type: date
  - Description: The date of the event, used as a primary partition key.
  
- **at_ist**
  - Data type: timestamp(6)
  - Description: The exact timestamp when the event occurred.
  
- **device_uuid**
  - Data type: varchar
  - Description: Unique identifier for the device from which the event was logged.
  
- **traits__city_name**
  - Data type: varchar
  - Description: The city name derived from the user's location traits.
  
- **name**
  - Data type: varchar
  - Description: Name of the event or interaction, used as a secondary partition key.
  
- **properties__widget_name**
  - Data type: varchar
  - Description: The name of the widget involved in the event.
  
- **properties__child_widget_title**
  - Data type: varchar
  - Description: The title of any child widget involved in the event.
  
- **properties__page_title**
  - Data type: varchar
  - Description: The title of the page where the event occurred.
  
- **properties__page_name**
  - Data type: varchar
  - Description: The name of the page where the event occurred.
  
- **properties__widget_title**
  - Data type: varchar
  - Description: The title of the widget involved in the event.
  
- **properties__icon**
  - Data type: varchar
  - Description: Icon associated with the event or widget.
  
- **properties__suggestion_value**
  - Data type: varchar
  - Description: Suggested values or responses related to the event.
  
- **properties__search_actual_keyword**
  - Data type: varchar
  - Description: The actual keyword used in a search related to the event.
  
- **properties__search_input_keyword**
  - Data type: varchar
  - Description: The input keyword provided by the user in a search event.
  
- **app__version**
  - Data type: varchar
  - Description: Version of the Bistro application during the event.
  
- **device__manufacturer**
  - Data type: varchar
  - Description: Manufacturer of the device used.
  
- **device__model**
  - Data type: varchar
  - Description: Model of the device used.
  
- **device__name**
  - Data type: varchar
  - Description: Name of the device used.
  
- **os__version**
  - Data type: varchar
  - Description: Operating system version of the device.
  
- **platform**
  - Data type: varchar
  - Description: The platform (iOS, Android, etc.) on which the event was logged.
  
- **traits__user_id**
  - Data type: varchar
  - Description: A unique identifier for the user, derived from user traits.
  
- **user_id**
  - Data type: varchar
  - Description: The unique identifier of the user involved in the event.

### Potential JOIN Keys and Business-Critical Columns
- **user_id** and **traits__user_id** could be used to join with other user-related tables for a comprehensive user profile analysis.
- **device_uuid** might be used to join with device-specific tables for device-based analytics.
- **properties__page_name** and **properties__widget_name** could relate to tables containing page and widget metadata.

### Potential Relationships
- Columns like **user_id** suggest potential relationships with tables that contain user account details or user activity logs.
- Device-related columns (**device_uuid**, **device__manufacturer**, **device__model**) indicate possible links to tables tracking device metadata or usage statistics.

This documentation provides a structured and detailed overview of the `logs_daily_impression_bistro_on_blinkit_v2` table, facilitating efficient data querying and analysis.