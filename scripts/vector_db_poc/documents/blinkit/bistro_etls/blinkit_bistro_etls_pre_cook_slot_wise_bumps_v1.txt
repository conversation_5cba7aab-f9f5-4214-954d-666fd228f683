# Table: blinkit.bistro_etls.pre_cook_slot_wise_bumps_v1

### Description
The table `blinkit.bistro_etls.pre_cook_slot_wise_bumps_v1` is designed to store detailed analytics about the sales performance of various dishes at different outlet locations, segmented by hourly slots. It captures both potential and actual sales data over the last 7 days, along with deviation metrics to help in forecasting and operational planning.

### Partitioning
The table is partitioned on the column `updated_at_ts`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **outlet_id**
  - Data type: integer
  - Description: The unique identifier for an outlet.

- **hour_slot**
  - Data type: integer
  - Description: Represents a specific hour of the day for which data is recorded.

- **sub_dish_pid**
  - Data type: integer
  - Description: The product identifier for a sub-dish.

- **sub_dish_name**
  - Data type: varchar
  - Description: The name of the sub-dish.

- **median_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The median of the estimated sale quantity for the last 7 days.

- **avg_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The average estimated sale quantity for the last 7 days.

- **max_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The maximum estimated sale quantity for the last 7 days.

- **min_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The minimum estimated sale quantity for the last 7 days.

- **median_quantity_sold_last_7d**
  - Data type: double
  - Description: The median of the actual sale quantity for the last 7 days.

- **avg_quantity_sold_last_7d**
  - Data type: double
  - Description: The average actual sale quantity for the last 7 days.

- **max_quantity_sold_last_7d**
  - Data type: double
  - Description: The maximum actual sale quantity for the last 7 days.

- **min_quantity_sold_last_7d**
  - Data type: double
  - Description: The minimum actual sale quantity for the last 7 days.

- **deviation_days**
  - Data type: double
  - Description: The number of days with significant deviation between potential and actual sales.

- **avg_deviation_positive**
  - Data type: double
  - Description: The average of positive deviations from the expected sales.

- **custom_weighted_deviation**
  - Data type: double
  - Description: A custom metric that weights deviations based on certain business rules.

- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: The timestamp when the record was last updated. Used for partitioning.

- **date_**
  - Data type: date
  - Description: The date for which the data applies.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `outlet_id` and `sub_dish_pid` can be used to join with other tables that contain outlet and dish details respectively.
- **Business-Critical Columns**: `median_quantity_sold_last_7d`, `avg_quantity_sold_last_7d`, `max_quantity_sold_last_7d`, and `min_quantity_sold_last_7d` are crucial for analyzing sales performance.

### Relationships to Other Tables
- The `outlet_id` may relate to an `outlets` table containing location and other outlet-specific information.
- The `sub_dish_pid` might link to a `menu_items` or `dishes` table that details available dishes or menu offerings.