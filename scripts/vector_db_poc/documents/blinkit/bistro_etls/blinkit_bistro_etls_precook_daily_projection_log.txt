# Table: blinkit.bistro_etls.precook_daily_projection_log

### Description
The `blinkit.bistro_etls.precook_daily_projection_log` table is designed to store daily projections for product sales, including historical sales data, growth rates, and projections based on different criteria. This table supports the forecasting of product demand and inventory management for merchants, helping to optimize stock levels and reduce waste.

### Partitioning
This table is partitioned by `projection_date` and `slot`. These partition keys are CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

### Columns
- **order_date**
    - Data type: date
    - Description: The date when the order was placed.
- **slot**
    - Data type: bigint
    - Description: A time slot identifier for when the order is to be fulfilled.
- **merchant_id**
    - Data type: bigint
    - Description: Unique identifier for the merchant.
- **merchant_name**
    - Data type: varchar
    - Description: Name of the merchant.
- **product_id**
    - Data type: bigint
    - Description: Unique identifier for the product.
- **product_name**
    - Data type: varchar
    - Description: Name of the product.
- **outlet_id**
    - Data type: bigint
    - Description: Unique identifier for the outlet where the product is sold.
- **dow**
    - Data type: bigint
    - Description: Day of the week represented as an integer.
- **gmv**
    - Data type: real
    - Description: Gross Merchandise Value associated with the product for the specific order.
- **quantity**
    - Data type: bigint
    - Description: Quantity of the product ordered.
- **last_dow_sold_quantity**
    - Data type: bigint
    - Description: Quantity sold of the product on the last similar day of the week.
- **last_dow_dotd_flag**
    - Data type: bigint
    - Description: Flag indicating if the product was part of a deal of the day last similar weekday.
- **last_2dow_sold_quantity**
    - Data type: bigint
    - Description: Quantity sold of the product two similar days of the week ago.
- **last_2dow_dotd_flag**
    - Data type: bigint
    - Description: Flag indicating if the product was part of a deal of the day two similar weekdays ago.
- **last_3dow_sold_quantity**
    - Data type: bigint
    - Description: Quantity sold of the product three similar days of the week ago.
- **last_3dow_dotd_flag**
    - Data type: bigint
    - Description: Flag indicating if the product was part of a deal of the day three similar weekdays ago.
- **last_4dow_sold_quantity**
    - Data type: bigint
    - Description: Quantity sold of the product four similar days of the week ago.
- **last_4dow_dotd_flag**
    - Data type: bigint
    - Description: Flag indicating if the product was part of a deal of the day four similar weekdays ago.
- **last_5dow_sold_quantity**
    - Data type: bigint
    - Description: Quantity sold of the product five similar days of the week ago.
- **last_5dow_dotd_flag**
    - Data type: bigint
    - Description: Flag indicating if the product was part of a deal of the day five similar weekdays ago.
- **store_age_in_days**
    - Data type: varchar
    - Description: Age of the store in days since opening.
- **opd**
    - Data type: bigint
    - Description: Orders per day, a metric for daily order volume.
- **projection_date**
    - Data type: date
    - Description: The date for which sales projections are being made.
- **dow_proj_date**
    - Data type: bigint
    - Description: Day of the week for the projection date.
- **preparation_type**
    - Data type: varchar
    - Description: Type of preparation required for the product.
- **sub_product_id**
    - Data type: bigint
    - Description: Unique identifier for a sub-product or variant of the main product.
- **sub_pid_name**
    - Data type: varchar
    - Description: Name of the sub-product or variant.
- **pack_size**
    - Data type: varchar
    - Description: Packaging size of the product.
- **deliver_qty**
    - Data type: bigint
    - Description: Quantity of the product to be delivered.
- **last_4_non_dotd_similar_day_type_median_sold_quantity**
    - Data type: real
    - Description: Median quantity sold on the last four non-deal days similar to the current day type.
- **last_non_dotd_dow_sold_quantity**
    - Data type: bigint
    - Description: Quantity sold on the last non-deal similar weekday.
- **last_non_dotd_2dow_sold_quantity**
    - Data type: bigint
    - Description: Quantity sold two non-deal similar weekdays ago.
- **last_non_dotd_3dow_sold_quantity**
    - Data type: bigint
    - Description: Quantity sold three non-deal similar weekdays ago.
- **last_2_dow_growth_rate**
    - Data type: real
    - Description: Growth rate in sales from the last two similar weekdays.
- **last_dow_growth_rate**
    - Data type: real
    - Description: Growth rate in sales from the last similar weekday.
- **row_median_growth**
    - Data type: real
    - Description: Median growth rate across rows.
- **last_2_dow_median_sold_quantity**
    - Data type: real
    - Description: Median quantity sold on the last two similar weekdays.
- **last_3_dow_median_sold_quantity**
    - Data type: real
    - Description: Median quantity sold on the last three similar weekdays.
- **last_3_dow_stdev_sold_quantity**
    - Data type: real
    - Description: Standard deviation of quantity sold on the last three similar weekdays.
- **is_strictly_increasing**
    - Data type: boolean
    - Description: Flag indicating if the sales trend is strictly increasing.
- **is_strictly_decreasing**
    - Data type: boolean
    - Description: Flag indicating if the sales trend is strictly decreasing.
- **projected_increasing**
    - Data type: real
    - Description: Projected increase in sales.
- **increasing_cap1**
    - Data type: real
    - Description: First cap or limit for projected sales increase.
- **increasing_cap2**
    - Data type: real
    - Description: Second cap or limit for projected sales increase.
- **increasing_cap3**
    - Data type: real
    - Description: Third cap or limit for projected sales increase.
- **projected_increasing_updated**
    - Data type: real
    - Description: Updated projection for sales increase.
- **projected_decreasing**
    - Data type: real
    - Description: Projected decrease in sales.
- **projected_default**
    - Data type: real
    - Description: Default projected sales figure.
- **final_projected**
    - Data type: real
    - Description: Final projected sales figure after adjustments.
- **final_projected_updated**
    - Data type: real
    - Description: Updated final projected sales figure.
- **updated_at_ts**
    - Data type: timestamp(6)
    - Description: Timestamp of the last update to the record.
- **cap_projected_strictly_increasing**
    - Data type: real
    - Description: Capped projection for strictly increasing sales trend.
- **new_final_projected_updated**
    - Data type: real
    - Description: Newly updated final projected sales figure.

### Potential JOIN Keys and Business-Critical Columns
- Potential JOIN keys include `merchant_id`, `product_id`, `outlet_id`, and `sub_product_id` which can be used to link to merchant, product, and outlet tables.
- Business-critical columns include `projection_date`, `product_id`, `quantity`, `gmv`, and `final_projected` which are essential for sales forecasting and inventory management.

### Potential Relationships
- The `product_id` and `sub_product_id` columns suggest relationships to a products table where details of each product and its variants are stored.
- The `merchant_id` column suggests a relationship to a merchants table containing merchant details.
- The `outlet_id` column suggests a relationship to an outlets table detailing each sales outlet.

This documentation provides a comprehensive overview of the `blinkit.bistro_etls.precook_daily_projection_log` table, ensuring efficient usage and maintenance of the data stored within.