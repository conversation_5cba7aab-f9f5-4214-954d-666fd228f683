# Table: blinkit.bistro_etls.collection_funnel

# Data Dictionary for `blinkit.bistro_etls.collection_funnel`

## Description
The `collection_funnel` table in the `blinkit.bistro_etls` schema likely serves as a detailed log of user interactions and transactions on specific pages within a merchant's domain. It tracks various metrics related to page visits, impressions, user actions (like add-to-cart), and sales across different dates.

## Partitioning
- **Partition Key:** `at_date_ist`
  - This key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

## Columns
- **at_date_ist**
  - Data type: varchar
  - Description: The date of the event, used as a partition key.

- **page_id**
  - Data type: varchar
  - Description: Identifier for the specific page where the events are tracked.

- **merchant_id**
  - Data type: integer
  - Description: Unique identifier for the merchant whose page activities are being logged.

- **total_page_visitors**
  - Data type: integer
  - Description: Total number of visitors to the page.

- **unique_page_visitors**
  - Data type: integer
  - Description: Count of unique visitors to the page.

- **total_impr**
  - Data type: integer
  - Description: Total number of impressions recorded on the page.

- **ooo_impr**
  - Data type: integer
  - Description: Number of out-of-order impressions on the page.

- **unique_products_shown_per_user**
  - Data type: integer
  - Description: Average number of unique products shown to each user.

- **impr_per_visit**
  - Data type: integer
  - Description: Average number of impressions per visit.

- **users_atced**
  - Data type: integer
  - Description: Number of users who added items to their cart (Add To Cart Event).

- **total_atcs**
  - Data type: integer
  - Description: Total number of add-to-cart actions performed.

- **atc_gmv**
  - Data type: integer
  - Description: Gross Merchandise Value generated from add-to-cart actions.

- **checkout_users**
  - Data type: integer
  - Description: Number of users who proceeded to checkout.

- **sales_gmv**
  - Data type: integer
  - Description: Gross Merchandise Value from completed sales.

- **vertical_cards_unique_users**
  - Data type: integer
  - Description: Number of unique users interacting with vertical cards on the page.

- **vertical_cards_total_clicks**
  - Data type: integer
  - Description: Total clicks on vertical cards by users.

## Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys:**
  - `merchant_id` could be used to join with other merchant-related tables.
  - `page_id` might be used to join with page-specific data tables.
  
- **Business-Critical Columns:**
  - `total_page_visitors`, `unique_page_visitors`, `total_impr`, `sales_gmv`, and `atc_gmv` are critical for analyzing page performance and sales effectiveness.

## Potential Relationships
- The `merchant_id` could link to a `merchants` table containing merchant details.
- The `page_id` might relate to a `pages` table that includes information about specific pages.

This documentation provides a structured and concise overview of the `collection_funnel` table, facilitating efficient data management and analysis within the bedrock knowledge base.