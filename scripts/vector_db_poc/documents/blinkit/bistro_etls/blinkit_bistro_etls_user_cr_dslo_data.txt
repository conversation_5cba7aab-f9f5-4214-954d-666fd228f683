# Table: blinkit.bistro_etls.user_cr_dslo_data

### Description
The `blinkit.bistro_etls.user_cr_dslo_data` table stores daily user-level data related to specific metrics, namely "Customer Retention" (CR) and "Days Since Last Order" (DSLO). This table is likely used for analyzing user engagement and retention patterns over time, segmented by various demographic and temporal dimensions.

### Partitioning
The table is partitioned by the `at_date_ist` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

### Columns
- **at_date_ist**
    - Data type: date
    - Description: The date for which the data is recorded, in IST timezone.

- **dslo_bucket**
    - Data type: varchar
    - Description: Categorical grouping of days since the user's last order.

- **cr_bucket**
    - Data type: varchar
    - Description: Categorical grouping based on the customer retention metrics.

- **cr**
    - Data type: bigint
    - Description: Numeric value representing customer retention.

- **dslo**
    - Data type: bigint
    - Description: Numeric value indicating the number of days since the user's last order.

- **max_dt**
    - Data type: date
    - Description: The maximum date for which data is available in this record.

- **user_id**
    - Data type: varchar
    - Description: Unique identifier for the user.

- **city_id**
    - Data type: bigint
    - Description: Numeric identifier for the city associated with the user.

- **city_name**
    - Data type: varchar
    - Description: Name of the city associated with the user.

### Key Relationships and Joins
- The `user_id` column can be used as a JOIN key to link this table with other user-related tables to gather comprehensive user profiles or activity.
- The `city_id` and `city_name` columns suggest potential joins with geographic or demographic tables to analyze data by location.

### Business-Critical Columns
- `user_id`: Essential for identifying unique users and analyzing user-level data.
- `cr` and `dslo`: Critical for business insights related to user retention and engagement.

This documentation provides a clear overview of the `blinkit.bistro_etls.user_cr_dslo_data` table, ensuring efficient and effective use of the data for analytical purposes.