# Table: blinkit.bistro_etls.order_campaign_attribution

### Description
The `blinkit.bistro_etls.order_campaign_attribution` table is designed to store detailed information about how different advertising campaigns contribute to customer orders. It includes data on customer interactions with advertisements, the device used, and the financial outcomes of these orders. This table is crucial for analyzing the effectiveness of marketing campaigns and optimizing advertising strategies.

### Partitioning
The table is partitioned on the `order_dt` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **order_id**
  - Data type: integer
  - Description: Unique identifier for each order.

- **customer_id**
  - Data type: integer
  - Description: Unique identifier for the customer who placed the order.

- **order_at_ist**
  - Data type: timestamp(6)
  - Description: Timestamp indicating when the order was placed, in Indian Standard Time.

- **cart_rank**
  - Data type: integer
  - Description: Ranking of the cart used in the order process.

- **advertising_id**
  - Data type: varchar
  - Description: Identifier for the specific advertisement shown to the customer.

- **device_uuid**
  - Data type: varchar
  - Description: Unique identifier for the device used to place the order.

- **platform**
  - Data type: varchar
  - Description: Platform through which the order was placed (e.g., mobile app, website).

- **campaign_id**
  - Data type: varchar
  - Description: Identifier for the marketing campaign associated with the order.

- **ad_partner**
  - Data type: varchar
  - Description: Name or identifier of the advertising partner involved in the campaign.

- **touch_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp indicating when the customer interacted with the advertisement, in Indian Standard Time.

- **selling_price**
  - Data type: bigint
  - Description: Total selling price of the order.

- **retained_margin**
  - Data type: bigint
  - Description: Profit margin retained after accounting for costs associated with the order.

- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp indicating when the data was captured into the table, in Indian Standard Time.

- **cart_id**
  - Data type: integer
  - Description: Unique identifier for the shopping cart used in the order.

- **adset_name**
  - Data type: varchar
  - Description: Name of the ad set within the campaign that was targeted to the customer.

- **ad_network_type**
  - Data type: varchar
  - Description: Type of advertising network used for the campaign (e.g., social media, search engine).

- **order_dt**
  - Data type: varchar
  - Description: The date on which the order was placed, used as the partition key.

### Key Relationships and JOINs
- The `order_id` can potentially be used to join with other order-related tables to fetch detailed order information.
- The `customer_id` might be used to join with customer tables to get more insights into customer demographics or behavior.
- The `campaign_id` and `ad_partner` could be linked to campaign management tables for detailed campaign analytics.

### Business-Critical Columns
- `order_id`, `customer_id`, `campaign_id`, and `selling_price` are critical for analyzing sales and marketing performance.
- `retained_margin` is crucial for financial analysis and profitability studies.