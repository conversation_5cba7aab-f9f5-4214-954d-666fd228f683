# Table: blinkit.bistro_etls.ars_indent_dump_v4

### Description
The `blinkit.bistro_etls.ars_indent_dump_v4` table appears to be used for tracking inventory and demand metrics across various outlets. It includes detailed information about inventory levels, product details, and demand quantities, which are critical for supply chain management and order fulfillment processes.

### Partitioning
The table is partitioned on the `sto_date` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **sto_date**
  - Data type: date
  - Description: The stock take date, used as the primary partition key.

- **run_id**
  - Data type: varchar
  - Description: Identifier for the specific run or batch of the data processing job.

- **backend_outlet_id**
  - Data type: integer
  - Description: Unique identifier for an outlet in the backend system.

- **backend_outlet_name**
  - Data type: varchar
  - Description: Name of the outlet as registered in the backend system.

- **frontend_outlet_id**
  - Data type: integer
  - Description: Unique identifier for an outlet in the frontend system.

- **outlet_name**
  - Data type: varchar
  - Description: The public-facing name of the outlet.

- **item_id**
  - Data type: integer
  - Description: Unique identifier for an item; potential JOIN key for item-related tables.

- **item_name**
  - Data type: varchar
  - Description: Name of the item.

- **wh_onshelf_inv**
  - Data type: integer
  - Description: Quantity of the item available on the warehouse shelves.

- **cum_demand_qty**
  - Data type: double
  - Description: Cumulative demand quantity for the item.

- **initial_demand_qty**
  - Data type: double
  - Description: Initial recorded demand quantity for the item.

- **pod_inv**
  - Data type: double
  - Description: Inventory available at the point of distribution.

- **open_po**
  - Data type: double
  - Description: Open purchase orders quantity.

- **cycel_start_inv**
  - Data type: double
  - Description: Inventory at the start of the cycle.

- **max_defined_qty**
  - Data type: double
  - Description: Maximum quantity defined for the item.

- **cpd**
  - Data type: integer
  - Description: Code representing the product's daily processing detail.

- **po_qty**
  - Data type: double
  - Description: Quantity of the item ordered through purchase orders.

- **product_type**
  - Data type: varchar
  - Description: Type or category of the product.

- **converted_id**
  - Data type: bigint
  - Description: A converted or transformed identifier for system use.

- **suppy_id**
  - Data type: integer
  - Description: Identifier for the supply record.

- **hp_prod_num**
  - Data type: integer
  - Description: High-priority product number.

- **conv_factor**
  - Data type: decimal(10,2)
  - Description: Conversion factor used for inventory calculations.

- **base_item_id**
  - Data type: integer
  - Description: Base item identifier for reference.

- **base_conv_factor**
  - Data type: decimal(10,2)
  - Description: Base conversion factor used for standardized calculations.

- **convted_wh_inv**
  - Data type: decimal(20,2)
  - Description: Converted warehouse inventory level.

- **convted_cum_demand**
  - Data type: double
  - Description: Converted cumulative demand.

- **wh_inv**
  - Data type: decimal(23,2)
  - Description: Total warehouse inventory.

- **cum_demand**
  - Data type: double
  - Description: Total cumulative demand for the item.

- **base_po_qty**
  - Data type: double
  - Description: Base quantity of purchase orders.

- **shortfall_flag**
  - Data type: varchar
  - Description: Flag indicating if there is a shortfall in inventory.

- **shortfall_qty**
  - Data type: double
  - Description: Quantity of shortfall in inventory.

- **updated_at**
  - Data type: timestamp(6) with time zone
  - Description: Timestamp of the last update to the record.

### Potential Relationships
- The `item_id` could be used to join with item detail tables.
- `backend_outlet_id` and `frontend_outlet_id` might link to respective outlet detail tables.

### Business-Critical Columns
- `sto_date`, `item_id`, `wh_onshelf_inv`, `cum_demand_qty`, `pod_inv`, and `shortfall_qty` are critical for inventory and demand analysis.