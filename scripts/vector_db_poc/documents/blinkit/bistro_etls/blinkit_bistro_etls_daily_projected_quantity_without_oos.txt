# Table: blinkit.bistro_etls.daily_projected_quantity_without_oos

### Description
The table `blinkit.bistro_etls.daily_projected_quantity_without_oos` appears to be designed for tracking daily projections of product quantities, focusing on metrics related to products being out of stock (OOS) and their visibility on search and non-search platforms. It includes data about user interactions, sales potential, and conversion rates, differentiated by whether products were out of stock or not.

### Partitioning
The table is partitioned on the column `updated_at_ts`. This is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **date_**
  - Data type: date
  - Description: The date for which the data is recorded.
  
- **hr**
  - Data type: bigint
  - Description: The hour of the day to which the data pertains.
  
- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for the merchant.
  
- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant.
  
- **product_id**
  - Data type: bigint
  - Description: Unique identifier for the product.
  
- **oos_search_impr_users**
  - Data type: bigint
  - Description: Number of users who saw the out-of-stock product in search results.
  
- **non_oos_search_impr_users**
  - Data type: bigint
  - Description: Number of users who saw the in-stock product in search results.
  
- **oos_search_impr_users_sell**
  - Data type: bigint
  - Description: Number of sales made from users who saw the out-of-stock product in search results.
  
- **non_oos_search_impr_users_sell**
  - Data type: bigint
  - Description: Number of sales made from users who saw the in-stock product in search results.
  
- **non_oos_search_impr_users_quantity**
  - Data type: bigint
  - Description: Quantity of in-stock products sold through search impressions.
  
- **oos_non_search_impr_users**
  - Data type: bigint
  - Description: Number of users who encountered out-of-stock products outside of search results.
  
- **non_oos_non_search_impr_users**
  - Data type: bigint
  - Description: Number of users who encountered in-stock products outside of search results.
  
- **oos_non_search_impr_users_sell**
  - Data type: bigint
  - Description: Number of sales made from users who saw the out-of-stock product outside of search results.
  
- **non_oos_non_search_impr_users_sell**
  - Data type: bigint
  - Description: Number of sales made from users who saw the in-stock product outside of search results.
  
- **non_oos_non_search_impr_users_quantity**
  - Data type: bigint
  - Description: Quantity of in-stock products sold through non-search impressions.
  
- **avg_hourly_oos_search_conv_rate**
  - Data type: double
  - Description: Average hourly conversion rate for out-of-stock products found via search.
  
- **non_oos_search_quantity_per_user**
  - Data type: double
  - Description: Average quantity of in-stock products purchased per user through search.
  
- **avg_hourly_oos_non_search_conv_rate**
  - Data type: double
  - Description: Average hourly conversion rate for out-of-stock products not found via search.
  
- **non_oos_non_search_quantity_per_user**
  - Data type: double
  - Description: Average quantity of in-stock products purchased per user not through search.
  
- **potential_oos_sale_through_search**
  - Data type: double
  - Description: Potential sales volume if out-of-stock products had been available through search.
  
- **potential_oos_sale_through_non_search**
  - Data type: double
  - Description: Potential sales volume if out-of-stock products had been available outside of search.
  
- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update to the record.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys:** `merchant_id`, `product_id` could be used to join with other tables containing merchant details or product details.
- **Business-Critical Columns:** `product_id`, `oos_search_impr_users`, `non_oos_search_impr_users`, `potential_oos_sale_through_search`, `potential_oos_sale_through_non_search` are critical for analyzing product availability impact on sales.

### Relationships to Other Tables
- The `product_id` column suggests a relationship to a products table where details about the products can be fetched.
- The `merchant_id` column suggests a relationship to a merchants table for fetching merchant details.