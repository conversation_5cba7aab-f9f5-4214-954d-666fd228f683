# Table: blinkit.bistro_etls.weekly_customer_conv_abs_metrics_bistro

### Description
The table `blinkit.bistro_etls.weekly_customer_conv_abs_metrics_bistro` is designed to store weekly metrics related to customer interactions and activities on various platforms and channels. It aggregates data such as app usage, page visits, user registrations, and transaction-related actions, providing insights into user engagement and behavior over time.

### Partitioning
This table is not partitioned. Queries on this table might not be optimized for performance without specific indexing strategies.

### Columns
- **snapshot_week_ist**
    - Data type: date
    - Description: The date indicating the week for which the data is summarized.
  
- **channel**
    - Data type: varchar
    - Description: The channel through which the user accessed the services (e.g., web, mobile).
  
- **platform**
    - Data type: varchar
    - Description: The platform from which the user accessed the services, such as iOS, Android, or web.
  
- **city**
    - Data type: varchar
    - Description: The city from which the user activities are recorded.
  
- **merchant_id**
    - Data type: varchar
    - Description: Identifier for the merchant involved in the transactions or interactions.
  
- **user_bucket**
    - Data type: varchar
    - Description: Categorization of users into different buckets based on predefined criteria.
  
- **user_type**
    - Data type: varchar
    - Description: Type of user, such as new or returning.
  
- **weekly_active_users**
    - Data type: bigint
    - Description: Count of unique users who were active during the week.
  
- **app_launch**
    - Data type: bigint
    - Description: Number of times the application was launched in the week.
  
- **home_page_visit**
    - Data type: bigint
    - Description: Number of visits to the home page during the week.
  
- **home_page_nsa_visit**
    - Data type: bigint
    - Description: Number of non-signed-in visits to the home page.
  
- **add_to_cart**
    - Data type: bigint
    - Description: Number of times items were added to the shopping cart.
  
- **cart_visit**
    - Data type: bigint
    - Description: Number of visits to the shopping cart page.
  
- **login_successful**
    - Data type: bigint
    - Description: Count of successful login attempts.
  
- **user_registered**
    - Data type: bigint
    - Description: Number of new user registrations.
  
- **nsa_ping_devices**
    - Data type: bigint
    - Description: Count of devices pinged without signed-in users.
  
- **address_added**
    - Data type: bigint
    - Description: Number of times users added an address to their profile.
  
- **payment_page_visit**
    - Data type: bigint
    - Description: Number of visits to the payment page.
  
- **etl_snapshot_ts_ist**
    - Data type: timestamp(6)
    - Description: Timestamp marking the time of data extraction, transformation, and loading.
  
- **login_page_visit**
    - Data type: bigint
    - Description: Number of visits to the login page.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id**: Could potentially be used to join with other merchant-related tables.
- **city**: May be used to join with geographic or demographic data tables.
- **user_type**, **weekly_active_users**, **user_registered**: Critical for business analysis focusing on user engagement and growth metrics.

### Potential Relationships
- Columns like `merchant_id` suggest possible relationships with tables containing detailed merchant information.
- User-related metrics (`user_type`, `user_registered`) could be linked with user demographic tables for deeper user analysis.