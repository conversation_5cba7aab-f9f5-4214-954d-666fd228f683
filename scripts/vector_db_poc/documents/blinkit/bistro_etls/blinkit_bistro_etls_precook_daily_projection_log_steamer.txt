# Table: blinkit.bistro_etls.precook_daily_projection_log_steamer

### Description
The table `blinkit.bistro_etls.precook_daily_projection_log_steamer` is designed to store daily projections for product sales, including historical sales data and various metrics to predict future sales. It likely serves as a critical component for inventory and supply chain management, helping to optimize product availability and prepare for demand fluctuations.

### Partitioning
The table is partitioned by `projection_date` and `slot`. These keys are CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval and management.

### Columns
- **order_date**
  - Data type: date
  - Description: The date on which the order was placed.

- **slot**
  - Data type: bigint
  - Description: A time slot identifier for when the order is placed, likely used for scheduling or logistics.

- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for the merchant from whom the product is sourced.

- **merchant_name**
  - Data type: varchar
  - Description: The name of the merchant associated with the merchant ID.

- **product_id**
  - Data type: bigint
  - Description: Unique identifier for a specific product.

- **product_name**
  - Data type: varchar
  - Description: The name of the product.

- **outlet_id**
  - Data type: bigint
  - Description: Identifier for the outlet where the product is sold.

- **dow**
  - Data type: bigint
  - Description: Day of the week represented as a number (e.g., 1 for Monday).

- **gmv**
  - Data type: real
  - Description: Gross Merchandise Value associated with the product for a specific time period.

- **quantity**
  - Data type: bigint
  - Description: Quantity of the product sold.

- **last_dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last same day of the week.

- **last_dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the last same day of the week was a 'deal of the day'.

- **last_2dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the second last same day of the week.

- **last_2dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the second last same day of the week was a 'deal of the day'.

- **last_3dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the third last same day of the week.

- **last_3dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the third last same day of the week was a 'deal of the day'.

- **last_4dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the fourth last same day of the week.

- **last_4dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the fourth last same day of the week was a 'deal of the day'.

- **last_5dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the fifth last same day of the week.

- **last_5dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the fifth last same day of the week was a 'deal of the day'.

- **store_age_in_days**
  - Data type: varchar
  - Description: The age of the store in days.

- **opd**
  - Data type: bigint
  - Description: Orders per day, a metric indicating the average number of orders received per day.

- **projection_date**
  - Data type: date
  - Description: The date for which sales projections are being made.

- **dow_proj_date**
  - Data type: bigint
  - Description: Day of the week for the projection date.

- **preparation_type**
  - Data type: varchar
  - Description: Type of preparation required for the product, if applicable.

- **sub_product_id**
  - Data type: bigint
  - Description: Identifier for a sub-category or variant of the main product.

- **sub_pid_name**
  - Data type: varchar
  - Description: Name of the sub-product or variant.

- **pack_size**
  - Data type: varchar
  - Description: Packaging size of the product.

- **deliver_qty**
  - Data type: bigint
  - Description: Quantity of the product to be delivered.

- **last_4_non_dotd_similar_day_type_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold on the last four non-deal days of similar type.

- **last_non_dotd_dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last non-deal day of the week.

- **last_non_dotd_2dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the second last non-deal day of the week.

- **last_non_dotd_3dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the third last non-deal day of the week.

- **last_2_dow_growth_rate**
  - Data type: real
  - Description: Growth rate in sales from the second last day of the week.

- **last_dow_growth_rate**
  - Data type: real
  - Description: Growth rate in sales from the last day of the week.

- **row_median_growth**
  - Data type: real
  - Description: Median growth rate across multiple days.

- **last_2_dow_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold across the last two days of the week.

- **last_3_dow_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold across the last three days of the week.

- **last_3_dow_stdev_sold_quantity**
  - Data type: real
  - Description: Standard deviation of quantity sold across the last three days of the week.

- **is_strictly_increasing**
  - Data type: boolean
  - Description: Indicates whether the sales trend is strictly increasing.

- **is_strictly_decreasing**
  - Data type: boolean
  - Description: Indicates whether the sales trend is strictly decreasing.

- **projected_increasing**
  - Data type: real
  - Description: Projected increase in sales.

- **increasing_cap1**
  - Data type: real
  - Description: First cap or limit for projected increasing sales.

- **increasing_cap2**
  - Data type: real
  - Description: Second cap or limit for projected increasing sales.

- **increasing_cap3**
  - Data type: real
  - Description: Third cap or limit for projected increasing sales.

- **projected_increasing_updated**
  - Data type: real
  - Description: Updated projection for increasing sales.

- **projected_decreasing**
  - Data type: real
  - Description: Projected decrease in sales.

- **projected_default**
  - Data type: real
  - Description: Default projection for sales.

- **final_projected**
  - Data type: real
  - Description: Final sales projection.

- **final_projected_updated**
  - Data type: real
  - Description: Updated final sales projection.

- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update to this record.

- **cap_projected_strictly_increasing**
  - Data type: real
  - Description: Capped projection for strictly increasing sales trend.

- **new_final_projected_updated**
  - Data type: real
  - Description: Newly updated final projected sales.

### Potential JOIN Keys and Business-Critical Columns
- `product_id` and `sub_product_id` can be used to join with product tables for detailed product information.
- `merchant_id` can be used to join with merchant tables to fetch merchant details.
- `outlet_id` might be used to join with outlet tables for location-based analysis.
- Business-critical columns include `projection_date`, `product_id`, `quantity`, `gmv`, and `final_projected`, which are essential for sales forecasting and inventory management.

### Potential Relationships
- This table could be related to other tables containing detailed product, merchant, or outlet information, using keys like `product_id`, `merchant_id`, and `outlet_id`.