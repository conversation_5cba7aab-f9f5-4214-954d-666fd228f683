# Table: blinkit.bistro_etls.pre_cook_item_wise_dow_logs_daily

### Description
The `blinkit.bistro_etls.pre_cook_item_wise_dow_logs_daily` table is designed to store daily logs of pre-cooked items sold at various outlets, segmented by day of the week (DOW). It captures sales data over multiple weeks to facilitate analysis of trends and patterns in item demand based on the day of the week.

### Partitioning
The table is partitioned on the `updated_at_ts` column. This is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **date_**
  - Data type: date
  - Description: The date on which the data was recorded.
  
- **outlet_id**
  - Data type: integer
  - Description: The unique identifier for the outlet where the items were sold.
  
- **outlet_name**
  - Data type: varchar
  - Description: The name of the outlet where the items were sold.
  
- **id**
  - Data type: integer
  - Description: A unique identifier for the log entry.
  
- **sub_name**
  - Data type: varchar
  - Description: The name of the sub-category of the item sold.
  
- **hr_**
  - Data type: integer
  - Description: The hour of the day when the sales data was recorded.
  
- **sold_quantity**
  - Data type: integer
  - Description: The quantity of the item sold on the current day.
  
- **task_qty**
  - Data type: integer
  - Description: The quantity of the item that was planned to be sold (tasked quantity).
  
- **last_dow_sold_quantity**
  - Data type: integer
  - Description: The quantity of the item sold on the last similar day of the week.
  
- **last_2dow_sold_quantity**
  - Data type: integer
  - Description: The quantity of the item sold two similar days of the week ago.
  
- **last_3dow_sold_quantity**
  - Data type: integer
  - Description: The quantity of the item sold three similar days of the week ago.
  
- **last_4dow_sold_quantity**
  - Data type: integer
  - Description: The quantity of the item sold four similar days of the week ago.
  
- **dow**
  - Data type: integer
  - Description: The day of the week represented as an integer (e.g., 1 for Monday, 2 for Tuesday, etc.).
  
- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: The timestamp when the record was last updated. This is also the partition key.

### Key Relationships and JOINs
- The `outlet_id` can be used to join with other tables that contain outlet-specific information, such as outlet locations or manager details.
- The `id` column, being a unique identifier for log entries, may serve as a JOIN key if there are detailed sub-logs or related records in other tables.

### Business-Critical Columns
- `sold_quantity` and `task_qty` are critical for analyzing the performance against sales targets.
- `dow` is essential for trend analysis based on the day of the week.
- `updated_at_ts` is crucial for tracking the latest data updates and maintaining data integrity across partitions.