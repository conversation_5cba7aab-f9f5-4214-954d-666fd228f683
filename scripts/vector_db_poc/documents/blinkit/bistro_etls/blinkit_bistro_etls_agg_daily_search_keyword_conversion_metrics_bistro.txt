# Table: blinkit.bistro_etls.agg_daily_search_keyword_conversion_metrics_bistro

### Description
The table `agg_daily_search_keyword_conversion_metrics_bistro` in the `blinkit.bistro_etls` schema aggregates daily metrics related to search keywords and their conversion outcomes on the Blinkit platform. It likely serves as a critical analytical tool for understanding customer search behavior, keyword effectiveness, and the overall impact on sales and product visibility.

### Partitioning
The table is partitioned on the column `at_date_ist`. This column is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **at_date_ist**
  - Data type: date
  - Description: The date for which the aggregated data is relevant.

- **platform**
  - Data type: varchar
  - Description: The platform (e.g., mobile, web) from which the search was made.

- **city_id**
  - Data type: integer
  - Description: The identifier for the city where the search originated.

- **city_name**
  - Data type: varchar
  - Description: The name of the city where the search originated.

- **merchant_id**
  - Data type: integer
  - Description: The identifier for the merchant related to the search.

- **merchant_name**
  - Data type: varchar
  - Description: The name of the merchant related to the search.

- **keyword**
  - Data type: varchar
  - Description: The search keyword entered by the user.

- **search_dau**
  - Data type: bigint
  - Description: Daily active users who performed the search.

- **total_searches_count**
  - Data type: bigint
  - Description: Total number of searches performed.

- **dau_atc**
  - Data type: bigint
  - Description: Daily active users who added items to the cart after searching.

- **total_atc**
  - Data type: bigint
  - Description: Total number of add-to-cart actions after searches.

- **total_quantity_atc**
  - Data type: bigint
  - Description: Total quantity of items added to the cart.

- **gmv_atc**
  - Data type: double
  - Description: Gross merchandise value of items added to the cart.

- **first_page_converted**
  - Data type: bigint
  - Description: Number of conversions that occurred directly from the first page of search results.

- **impressions**
  - Data type: bigint
  - Description: Total number of times any search result was viewed.

- **oos_impressions**
  - Data type: bigint
  - Description: Impressions for items that were out of stock.

- **coming_soon_impressions**
  - Data type: bigint
  - Description: Impressions for items marked as coming soon.

- **similar_only_searches**
  - Data type: bigint
  - Description: Searches that resulted only in similar product suggestions.

- **total_similar_searches**
  - Data type: bigint
  - Description: Total number of searches resulting in similar product suggestions.

- **null_searches**
  - Data type: bigint
  - Description: Searches that resulted in no product matches.

- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: The timestamp when the ETL process that generated this record was completed.

- **additional_dwh_info**
  - Data type: array(varchar)
  - Description: Array of additional data warehousing information related to the record.

- **veg_nonveg_mode**
  - Data type: varchar
  - Description: Indicates whether the search was filtered for vegetarian or non-vegetarian products.

### Potential JOIN Keys and Business-Critical Columns
- **city_id** and **merchant_id** can be used as JOIN keys to link with other geographical or merchant-related tables.
- Business-critical columns include **keyword**, **total_searches_count**, **dau_atc**, **total_atc**, **gmv_atc**, and **first_page_converted** which are essential for analyzing the effectiveness of keywords and the conversion funnel.

### Potential Relationships
- **city_id** may relate to a `cities` table.
- **merchant_id** may relate to a `merchants` table.
- **keyword** could be used to join with other tables that log individual search details or keyword performance metrics.

This documentation provides a comprehensive overview of the `agg_daily_search_keyword_conversion_metrics_bistro` table, ensuring efficient and effective use for data analysis and business intelligence.