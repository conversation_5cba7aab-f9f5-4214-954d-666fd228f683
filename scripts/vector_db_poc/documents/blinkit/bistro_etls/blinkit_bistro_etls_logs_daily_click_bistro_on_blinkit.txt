# Table: blinkit.bistro_etls.logs_daily_click_bistro_on_blinkit

### Description
The table `blinkit.bistro_etls.logs_daily_click_bistro_on_blinkit` appears to be designed for storing daily logs of user interactions with various UI elements (widgets) on the Blinkit platform. This data is likely used for analyzing user behavior, optimizing user experience, and tracking engagement metrics across different features and pages within the application.

### Partitioning
The table is partitioned on the keys `at_date_ist` and `name`. These keys are CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **at_date_ist**
  - Data type: date
  - Description: The date of the event, in IST timezone.

- **at_ist**
  - Data type: timestamp(6)
  - Description: The exact timestamp of the event, in IST timezone.

- **device_uuid**
  - Data type: varchar
  - Description: A unique identifier for the device from which the event was logged.

- **traits__city_name**
  - Data type: varchar
  - Description: The city name associated with the user's location.

- **name**
  - Data type: varchar
  - Description: The name of the event or interaction logged.

- **properties__widget_name**
  - Data type: varchar
  - Description: The name of the widget involved in the interaction.

- **properties__child_widget_title**
  - Data type: varchar
  - Description: The title of any child widget involved in the interaction.

- **properties__page_title**
  - Data type: varchar
  - Description: The title of the page where the interaction occurred.

- **properties__page_name**
  - Data type: varchar
  - Description: The name of the page where the interaction occurred.

- **properties__widget_title**
  - Data type: varchar
  - Description: The title of the widget involved in the interaction.

- **properties__icon**
  - Data type: varchar
  - Description: The icon associated with the widget or feature.

- **properties__suggestion_value**
  - Data type: varchar
  - Description: The value of any suggestion made by the widget or feature.

- **properties__search_actual_keyword**
  - Data type: varchar
  - Description: The actual keyword entered by the user in a search interaction.

- **properties__search_input_keyword**
  - Data type: varchar
  - Description: The input keyword suggested or modified before the final search.

- **app__version**
  - Data type: varchar
  - Description: The version of the Blinkit application being used.

- **device__manufacturer**
  - Data type: varchar
  - Description: The manufacturer of the device.

- **device__model**
  - Data type: varchar
  - Description: The model of the device.

- **device__name**
  - Data type: varchar
  - Description: The name of the device.

- **os__version**
  - Data type: varchar
  - Description: The operating system version of the device.

- **platform**
  - Data type: varchar
  - Description: The platform (iOS, Android, web, etc.) through which the user accessed the application.

- **traits__user_id**
  - Data type: varchar
  - Description: A unique identifier for the user, likely used for tracking user behavior across sessions.

- **user_id**
  - Data type: varchar
  - Description: Another unique identifier for the user, potentially used for internal tracking or analytics.

### Potential JOIN Keys and Business-Critical Columns
- **user_id** and **traits__user_id** could be used to join with other user-related tables for a comprehensive analysis of user behavior.
- **device_uuid** might be used to join with device-specific tables for device-based analysis.
- Business-critical columns likely include **at_date_ist**, **name**, **properties__page_name**, and **properties__widget_name** as they directly relate to user interactions and engagement metrics.