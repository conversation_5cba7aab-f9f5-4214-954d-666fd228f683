# Table: blinkit.bistro_etls.pre_cook_logs_daily

### Description
The `blinkit.bistro_etls.pre_cook_logs_daily` table appears to store daily logs related to the pre-cooking stages in a food preparation process at various outlets. It likely includes data on projected quantities, preparation times, and sales information for different sub-products or dishes, facilitating inventory and preparation management.

### Partitioning
The table is partitioned on the `updated_at` column. This is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **outlet_id**
  - Data type: bigint
  - Description: The unique identifier for an outlet where the food preparation takes place.

- **projection_date**
  - Data type: varchar
  - Description: The date for which the food preparation projections are made.

- **sub_pid**
  - Data type: bigint
  - Description: The identifier for a sub-product or dish.

- **sub_name**
  - Data type: varchar
  - Description: The name of the sub-product or dish being prepared.

- **transformation_type**
  - Data type: varchar
  - Description: The type of food preparation or transformation process applied.

- **prep_hour**
  - Data type: bigint
  - Description: The hour of the day when the preparation is scheduled to start.

- **prep_min**
  - Data type: varchar
  - Description: The minute of the hour when the preparation is scheduled to start.

- **sub_dish_quantity**
  - Data type: bigint
  - Description: The quantity of the sub-product or dish prepared.

- **slot_sale**
  - Data type: double
  - Description: Sales generated during a specific time slot.

- **pre_prep_qty_intermediate_new**
  - Data type: bigint
  - Description: Newly calculated quantity needed for pre-preparation.

- **ceiling_value**
  - Data type: varchar
  - Description: The maximum limit set for the quantity to be prepared.

- **floor_value**
  - Data type: varchar
  - Description: The minimum limit set for the quantity to be prepared.

- **pre_prep_qty**
  - Data type: bigint
  - Description: The quantity determined necessary for pre-preparation.

- **pre_prep_qty_updated_with_floor**
  - Data type: bigint
  - Description: The pre-preparation quantity adjusted according to the floor value.

- **pre_prep_qty_updated_without_floor**
  - Data type: bigint
  - Description: The pre-preparation quantity calculated without considering the floor value.

- **updated_at**
  - Data type: varchar
  - Description: The timestamp when the record was last updated, used for partitioning.

### Potential JOIN Keys and Business-Critical Columns
- **outlet_id** could be used to join with other tables containing outlet-specific information.
- **sub_pid** might link to detailed product or dish tables.
- Business-critical columns include **projection_date**, **sub_dish_quantity**, **slot_sale**, and **updated_at** for tracking sales and inventory needs over time.

### Potential Relationships
- The `sub_pid` column suggests a relationship to a products or dishes table where each unique ID corresponds to a specific item.
- The `outlet_id` may relate to an outlet or location table detailing each physical location's attributes.