# Table: blinkit.bistro_etls.thaw_daily_projection_log_v1

### Description
The `blinkit.bistro_etls.thaw_daily_projection_log_v1` table is designed to store daily projections and historical sales data for products across various merchants and outlets. It includes detailed metrics on sales quantities, growth rates, and projections, which are critical for inventory and sales forecasting.

### Partitioning
The table is partitioned by the `updated_at_ts` column. This is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **order_date**
  - Data type: date
  - Description: The date on which the order was placed.
  
- **slot**
  - Data type: bigint
  - Description: A numerical identifier for the time slot in which the order was placed.
  
- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for the merchant.
  
- **merchant_name**
  - Data type: varchar
  - Description: The name of the merchant.
  
- **product_id**
  - Data type: bigint
  - Description: Unique identifier for the product.
  
- **product_name**
  - Data type: varchar
  - Description: The name of the product.
  
- **outlet_id**
  - Data type: bigint
  - Description: Unique identifier for the outlet where the product is sold.
  
- **dow**
  - Data type: bigint
  - Description: Day of the week represented as a number.
  
- **gmv**
  - Data type: real
  - Description: Gross Merchandise Value associated with the product for the specific day.
  
- **quantity**
  - Data type: bigint
  - Description: Quantity of the product sold.
  
- **last_dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last similar day of the week.
  
- **last_dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the last similar day of the week was a 'deal of the day'.
  
- **last_2dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold two similar days of the week ago.
  
- **last_2dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if two similar days of the week ago was a 'deal of the day'.
  
- **last_3dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold three similar days of the week ago.
  
- **last_3dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if three similar days of the week ago was a 'deal of the day'.
  
- **last_4dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold four similar days of the week ago.
  
- **last_4dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if four similar days of the week ago was a 'deal of the day'.
  
- **last_5dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold five similar days of the week ago.
  
- **last_5dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if five similar days of the week ago was a 'deal of the day'.
  
- **store_age_in_days**
  - Data type: varchar
  - Description: The age of the store in days.
  
- **opd**
  - Data type: bigint
  - Description: Orders per day metric.
  
- **projection_date**
  - Data type: date
  - Description: The date for which sales are being projected.
  
- **dow_proj_date**
  - Data type: bigint
  - Description: Day of the week for the projection date.
  
- **preparation_type**
  - Data type: varchar
  - Description: Type of preparation required for the product.
  
- **sub_product_id**
  - Data type: bigint
  - Description: Unique identifier for a sub-category of the product.
  
- **sub_pid_name**
  - Data type: varchar
  - Description: Name of the sub-product.
  
- **pack_size**
  - Data type: varchar
  - Description: Packaging size of the product.
  
- **deliver_qty**
  - Data type: bigint
  - Description: Quantity of the product delivered.
  
- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update to the record.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys:** `merchant_id`, `product_id`, `outlet_id` can be used to join with other tables containing merchant, product, or outlet details.
- **Business-Critical Columns:** `order_date`, `product_id`, `quantity`, `gmv`, `projection_date` are crucial for analyzing sales performance and forecasting.

### Potential Relationships
- **`product_id`** could be used to join with a product catalog table.
- **`merchant_id`** might link to a merchant information table.
- **`outlet_id`** could relate to an outlet details table.

This structured documentation ensures efficient querying and data analysis, supporting business operations and strategic decision-making.