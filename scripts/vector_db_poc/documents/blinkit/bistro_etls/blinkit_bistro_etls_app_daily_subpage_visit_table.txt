# Table: blinkit.bistro_etls.app_daily_subpage_visit_table

# Data Dictionary for Table: `blinkit.bistro_etls.app_daily_subpage_visit_table`

## Description
This table records daily visits to various subpages within an application, capturing user interactions across different platforms and devices. It is designed to help analyze user behavior and engagement with specific features or sections of the app.

## Partitioning
The table is partitioned on the column `at_date_ist`. This is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

## Columns
- **at_date_ist**
  - Data type: date
  - Description: The date of the user's visit, in IST timezone.

- **platform**
  - Data type: varchar
  - Description: The platform from which the user accessed the app (e.g., iOS, Android, Web).

- **device_uuid**
  - Data type: varchar
  - Description: A unique identifier for the device used during the visit.

- **traits__user_id**
  - Data type: varchar
  - Description: The user identifier, linking the visit to a specific user.

- **properties__sub_page_visit_id**
  - Data type: varchar
  - Description: A unique identifier for the subpage visit event.

- **source_tab**
  - Data type: varchar
  - Description: The tab from which the subpage was accessed, indicating the user's navigation path.

- **widget_title**
  - Data type: varchar
  - Description: The title of the widget interacted with on the subpage, useful for understanding which features attract more engagement.

- **name**
  - Data type: varchar
  - Description: The name of the subpage visited, providing context about the content or feature being interacted with.

## Key Relationships and Joins
- The `traits__user_id` column can potentially be used to JOIN this table with other user-related tables to enrich the data with more user-specific attributes.
- The `properties__sub_page_visit_id` may be used to link multiple records of interactions within the same subpage visit, or to JOIN with event tables that detail more granular actions taken during the visit.

## Business-Critical Columns
- **traits__user_id**: Essential for user-level analysis and personalization insights.
- **properties__sub_page_visit_id**: Critical for tracking individual engagement events and understanding user journey within the app.

This documentation provides a structured overview to facilitate efficient and effective querying and analysis of the `app_daily_subpage_visit_table` in the `blinkit.bistro_etls` schema.