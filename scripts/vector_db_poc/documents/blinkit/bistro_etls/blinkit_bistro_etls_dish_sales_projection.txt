# Table: blinkit.bistro_etls.dish_sales_projection

### Description
The `blinkit.bistro_etls.dish_sales_projection` table is designed to store projected sales data for dishes across different outlets. It likely serves as a planning tool for inventory and supply chain management by predicting future sales volumes on a daily basis.

### Partitioning
The table is partitioned by the `projection_date` column. This is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **outlet_id**
  - Data type: integer
  - Description: A unique identifier for each outlet.

- **outlet_name**
  - Data type: varchar
  - Description: The name of the outlet.

- **dish_pid**
  - Data type: integer
  - Description: A unique identifier for each dish.

- **dish_name**
  - Data type: varchar
  - Description: The name of the dish.

- **projection_date**
  - Data type: date
  - Description: The date for which the sales projection is made.

- **dow_adj_scaled_qty**
  - Data type: integer
  - Description: The projected quantity of the dish sold, adjusted for the day of the week.

- **update_ts_ist**
  - Data type: varchar
  - Description: The timestamp indicating the last update time in Indian Standard Time (IST).

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: 
  - `outlet_id` can be used to join with other tables that contain outlet-specific information.
  - `dish_pid` might be used to join with tables containing detailed dish information or inventory data.
  
- **Business-Critical Columns**:
  - `projection_date` is critical for understanding the timing of projections and for partitioning.
  - `dow_adj_scaled_qty` is crucial for assessing projected sales and planning accordingly.

### Potential Relationships to Other Tables
- The `outlet_id` column suggests a potential relationship with an `outlets` table that would contain more detailed information about each outlet.
- The `dish_pid` column suggests a potential relationship with a `dishes` or `menu_items` table that would detail each dish's characteristics or ingredients.