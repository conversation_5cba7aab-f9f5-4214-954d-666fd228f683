# Table: blinkit.bistro_etls.dim_map_dish_subdish_ingredient_bistro

### Description
The table `blinkit.bistro_etls.dim_map_dish_subdish_ingredient_bistro` is designed to map dishes to their sub-dishes and ingredients within a bistro setting. It likely serves as a dimensional table in a data warehouse, used for analyzing the relationships between dishes and their components, as well as inventory and supply chain management.

### Partitioning
This table is not partitioned. Queries on this table may experience performance variations depending on the dataset's size and the query's complexity.

### Columns
- **dish_pid**
  - Data type: bigint
  - Description: Unique identifier for the dish.

- **store_id**
  - Data type: bigint
  - Description: Identifier of the store where the dish is available.

- **pid_combined**
  - Data type: bigint
  - Description: Combined identifier possibly representing a unique combination of dish and sub-dish or ingredient.

- **supply_pid_combined**
  - Data type: bigint
  - Description: Combined identifier for the supply chain, possibly linking to external supply or inventory data.

- **ing_sub_flag**
  - Data type: varchar
  - Description: Flag to indicate whether the item is an ingredient or a sub-dish.

- **combined_qty**
  - Data type: bigint
  - Description: Quantity of the ingredient or sub-dish combined with the main dish.

- **conversion_factor**
  - Data type: decimal(12,3)
  - Description: Factor used to convert quantities from one unit to another.

- **is_current**
  - Data type: boolean
  - Description: Indicates whether the mapping is current and active.

- **dbt_scd_id**
  - Data type: varchar
  - Description: Slowly Changing Dimension (SCD) identifier used for tracking changes over time.

- **dbt_updated_at**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update made to this record.

- **dbt_valid_from**
  - Data type: timestamp(6)
  - Description: Start timestamp from which the record is considered valid.

- **dbt_valid_to**
  - Data type: timestamp(6)
  - Description: End timestamp until which the record is considered valid.

- **station**
  - Data type: varchar
  - Description: Station within the bistro where the dish is prepared or the ingredient is used.

### Potential JOIN Keys and Business-Critical Columns
- **dish_pid**: Likely a primary key and potential JOIN key with other tables related to dish details.
- **store_id**: Potential JOIN key with other tables containing store information.
- **supply_pid_combined**: Could be used to join with supply chain or inventory management tables.

### Potential Relationships
- **dish_pid** could be related to a `dishes` table that contains detailed information about each dish.
- **store_id** might relate to a `stores` table that holds location and operational details of each bistro store.
- **supply_pid_combined** suggests a relationship with supply chain data, potentially linking to a `suppliers` or `inventory` table.

This documentation provides a structured overview to assist in querying and analyzing the data effectively, especially in understanding the relationships between dishes, sub-dishes, and ingredients within bistros.