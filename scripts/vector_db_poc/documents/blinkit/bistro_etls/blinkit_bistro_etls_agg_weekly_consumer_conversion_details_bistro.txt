# Table: blinkit.bistro_etls.agg_weekly_consumer_conversion_details_bistro

### Description
The table `blinkit.bistro_etls.agg_weekly_consumer_conversion_details_bistro` aggregates weekly consumer conversion metrics across various stages of the user journey on a platform. It tracks user interactions from app launches to checkouts, segmented by different channels, platforms, and cities. This data is crucial for analyzing user behavior, optimizing user experience, and improving conversion rates.

### Partitioning
This table is not partitioned. Queries on this table may experience performance impacts due to the lack of partition keys. It is recommended to consider future partitioning strategies based on frequently queried columns to enhance performance.

### Columns
- **snapshot_week_ist**
  - Data type: date
  - Description: The week for which the data snapshot was taken.
  
- **channel**
  - Data type: varchar
  - Description: The channel through which the user accessed the platform (e.g., web, mobile).
  
- **platform**
  - Data type: varchar
  - Description: The platform (e.g., Android, iOS) used by the consumer.
  
- **city**
  - Data type: varchar
  - Description: The city from which the user accessed the platform.
  
- **merchant_id**
  - Data type: varchar
  - Description: Identifier for the merchant involved in the transactions.
  
- **user_bucket**
  - Data type: varchar
  - Description: Categorization of users into different buckets based on defined criteria.
  
- **user_type**
  - Data type: varchar
  - Description: Type of user, such as new or returning.
  
- **weekly_active_users**
  - Data type: bigint
  - Description: Count of users active during the week.
  
- **app_launch**
  - Data type: bigint
  - Description: Number of times the app was launched in the week.
  
- **home_page_visit**
  - Data type: bigint
  - Description: Number of visits to the home page.
  
- **home_page_nsa_visit**
  - Data type: bigint
  - Description: Number of non-signature-authenticated visits to the home page.
  
- **add_to_cart**
  - Data type: bigint
  - Description: Number of times items were added to the cart.
  
- **cart_visit**
  - Data type: bigint
  - Description: Number of visits to the cart page.
  
- **user_registered**
  - Data type: bigint
  - Description: Number of users who registered during the week.
  
- **nsa_pings**
  - Data type: bigint
  - Description: Number of non-signature-authenticated pings to the server.
  
- **login_successful**
  - Data type: bigint
  - Description: Number of successful login attempts.
  
- **address_added**
  - Data type: bigint
  - Description: Number of times users added an address to their profile.
  
- **payment_page_visit**
  - Data type: bigint
  - Description: Number of visits to the payment page.
  
- **app_launch_to_home_page_visit**
  - Data type: bigint
  - Description: Conversion metric from app launch to home page visit.
  
- **wau_to_home_page_visit**
  - Data type: bigint
  - Description: Weekly active users who visited the home page.
  
- **app_launch_to_home_page_visit_nsa**
  - Data type: bigint
  - Description: Non-signature-authenticated conversions from app launch to home page visit.
  
- **wau_to_atc**
  - Data type: bigint
  - Description: Weekly active users who added items to the cart.
  
- **atc_to_cv**
  - Data type: bigint
  - Description: Conversion from add to cart to cart visit.
  
- **cv_to_user_registered**
  - Data type: bigint
  - Description: Conversion from cart visit to user registration.
  
- **cv_to_address_added**
  - Data type: bigint
  - Description: Conversion from cart visit to address addition.
  
- **cv_to_payment**
  - Data type: bigint
  - Description: Conversion from cart visit to payment page visit.
  
- **payment_to_checkout**
  - Data type: bigint
  - Description: Conversion from payment initiation to checkout completion.
  
- **cv_to_checkout**
  - Data type: bigint
  - Description: Conversion from cart visit to checkout completion.
  
- **overall_conversion**
  - Data type: bigint
  - Description: Overall conversion rate, likely from initial visit to final purchase.
  
- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of when the ETL process for this data snapshot was completed.
  
- **login_page_visit**
  - Data type: bigint
  - Description: Number of visits to the login page.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id** could be used to join with other merchant-related tables.
- **city** might be used to join with geographic or demographic data tables.
- Business-critical columns include **weekly_active_users**, **overall_conversion**, and metrics related to user actions like **add_to_cart**, **payment_page_visit**, and **checkout**.

### Potential Relationships
- Columns like **merchant_id** suggest potential relationships with tables containing detailed merchant information.
- User-related metrics (e.g., **user_registered**, **login_successful**) might relate to user profile tables.

This documentation provides a comprehensive overview of the `agg_weekly_consumer_conversion_details_bistro` table, essential for understanding user conversion pathways and improving platform strategies.