# Table: blinkit.bistro_etls.daily_customer_conv_abs_metrics_bistro

### Description
The table `blinkit.bistro_etls.daily_customer_conv_abs_metrics_bistro` is designed to store daily metrics related to customer engagement and activity on a platform. This includes data on app usage, user interactions, and transactional activities. The metrics are segmented by various dimensions such as channel, platform, city, and merchant, which helps in analyzing user behavior across different segments.

### Partitioning
This table is not partitioned. It is important to consider performance implications when querying large datasets from this table.

### Columns
- **snapshot_date_ist**
  - Data type: date
  - Description: The date of the data snapshot in IST timezone.

- **snapshot_hour_ist**
  - Data type: integer
  - Description: The hour of the day for the data snapshot in IST timezone.

- **channel**
  - Data type: varchar
  - Description: The channel through which the user accessed the platform (e.g., web, mobile).

- **platform**
  - Data type: varchar
  - Description: The platform (e.g., Android, iOS) used by the customer.

- **city**
  - Data type: varchar
  - Description: The city from which the user accessed the platform.

- **merchant_id**
  - Data type: varchar
  - Description: Identifier for the merchant associated with the transactions or interactions.

- **user_bucket**
  - Data type: varchar
  - Description: Categorization of users into different buckets based on defined criteria.

- **user_type**
  - Data type: varchar
  - Description: Type of user (e.g., new, returning).

- **daily_active_users**
  - Data type: bigint
  - Description: Count of unique users active on the platform on a given day.

- **app_launch**
  - Data type: bigint
  - Description: Number of times the application was launched.

- **home_page_visit**
  - Data type: bigint
  - Description: Number of visits to the home page.

- **home_page_nsa_visit**
  - Data type: bigint
  - Description: Number of non-signed-in visits to the home page.

- **add_to_cart**
  - Data type: bigint
  - Description: Number of times items were added to the shopping cart.

- **cart_visit**
  - Data type: bigint
  - Description: Number of visits to the shopping cart page.

- **login_successful**
  - Data type: bigint
  - Description: Number of successful login attempts.

- **user_registered**
  - Data type: bigint
  - Description: Number of new users registered.

- **nsa_ping_devices**
  - Data type: bigint
  - Description: Count of devices pinged without signed-in users.

- **address_added**
  - Data type: bigint
  - Description: Number of times users added an address to their profile.

- **payment_page_visit**
  - Data type: bigint
  - Description: Number of visits to the payment page.

- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the ETL process snapshot in IST timezone.

- **login_page_visit**
  - Data type: bigint
  - Description: Number of visits to the login page.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id**: Could potentially be used to join with other tables containing merchant-specific data.
- **city**: May be joined with geographic or demographic data tables.
- **user_type**, **daily_active_users**, **app_launch**: These are business-critical columns as they directly relate to user engagement and platform activity metrics.

### Potential Relationships
- Columns like `merchant_id` suggest possible relationships with other merchant-related tables.
- User activity columns (`daily_active_users`, `login_successful`, etc.) could relate to user demographic tables or session logs.

This documentation provides a comprehensive overview of the table structure and its intended use for performance analysis and user engagement tracking.