# Table: blinkit.bistro_etls.thaw_daily_projection_log

### Description
The `blinkit.bistro_etls.thaw_daily_projection_log` table appears to be designed for storing daily projections of sales and inventory metrics for products across different merchants and outlets. It includes detailed historical sales data, growth rates, and projections which are crucial for inventory management and forecasting demand.

### Partitioning
The table is partitioned on the following keys:
- `projection_date` (date): The date for which the projection is made.
- `slot` (varchar): The specific time slot of the projection.

These keys are CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition.

### Columns
- **order_date**
  - Data type: date
  - Description: The date on which the order was placed.

- **slot**
  - Data type: varchar
  - Description: Time slot for which the projection log is recorded.

- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for the merchant.

- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant.

- **product_id**
  - Data type: bigint
  - Description: Unique identifier for the product.

- **product_name**
  - Data type: varchar
  - Description: Name of the product.

- **outlet_id**
  - Data type: bigint
  - Description: Unique identifier for the outlet.

- **dow**
  - Data type: bigint
  - Description: Day of the week represented as an integer.

- **gmv**
  - Data type: real
  - Description: Gross Merchandise Value associated with the product for the given slot.

- **quantity**
  - Data type: bigint
  - Description: Quantity of the product ordered.

- **last_dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last similar day of the week.

- **last_dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the last similar day of the week was a deal of the day.

- **last_2dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold two similar days of the week ago.

- **last_2dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if two similar days of the week ago was a deal of the day.

- **last_3dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold three similar days of the week ago.

- **last_3dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if three similar days of the week ago was a deal of the day.

- **last_4dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold four similar days of the week ago.

- **last_4dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if four similar days of the week ago was a deal of the day.

- **last_5dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold five similar days of the week ago.

- **last_5dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if five similar days of the week ago was a deal of the day.

- **store_age_in_days**
  - Data type: varchar
  - Description: Age of the store in days.

- **opd**
  - Data type: bigint
  - Description: Orders per day.

- **projection_date**
  - Data type: date
  - Description: The date for which sales and inventory metrics are projected.

- **dow_proj_date**
  - Data type: bigint
  - Description: Day of the week for the projection date.

- **preparation_type**
  - Data type: varchar
  - Description: Type of preparation required for the product.

- **sub_product_id**
  - Data type: bigint
  - Description: Sub-identifier for products that have variations.

- **sub_pid_name**
  - Data type: varchar
  - Description: Name of the sub-product.

- **pack_size**
  - Data type: varchar
  - Description: Packaging size of the product.

- **deliver_qty**
  - Data type: bigint
  - Description: Quantity of the product to be delivered.

- **last_4_non_dotd_similar_day_type_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold on the last four non-deal similar day types.

- **last_non_dotd_dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last non-deal day of the week.

- **last_non_dotd_2dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold two non-deal days of the week ago.

- **last_non_dotd_3dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold three non-deal days of the week ago.

- **last_2_dow_growth_rate**
  - Data type: real
  - Description: Growth rate between the last two similar days of the week.

- **last_dow_growth_rate**
  - Data type: real
  - Description: Growth rate since the last similar day of the week.

- **row_median_growth**
  - Data type: real
  - Description: Median growth rate across rows.

- **last_2_dow_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold over the last two similar days of the week.

- **last_3_dow_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold over the last three similar days of the week.

- **last_3_dow_stdev_sold_quantity**
  - Data type: real
  - Description: Standard deviation of quantity sold over the last three similar days of the week.

- **is_strictly_increasing**
  - Data type: boolean
  - Description: Indicates if the sales trend is strictly increasing.

- **is_strictly_decreasing**
  - Data type: boolean
  - Description: Indicates if the sales trend is strictly decreasing.

- **projected_increasing**
  - Data type: real
  - Description: Projected increase in sales.

- **increasing_cap1**
  - Data type: real
  - Description: First cap value for projected increasing sales.

- **increasing_cap2**
  - Data type: real
  - Description: Second cap value for projected increasing sales.

- **increasing_cap3**
  - Data type: real
  - Description: Third cap value for projected increasing sales.

- **projected_increasing_updated**
  - Data type: real
  - Description: Updated projection for increasing sales.

- **projected_decreasing**
  - Data type: real
  - Description: Projected decrease in sales.

- **projected_default**
  - Data type: real
  - Description: Default projected sales value.

- **final_projected**
  - Data type: real
  - Description: Final projected sales quantity.

- **final_projected_updated**
  - Data type: real
  - Description: Updated final projected sales quantity.

- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp of the last update to the record.

- **cap_projected_strictly_increasing**
  - Data type: real
  - Description: Capped projection for strictly increasing sales.

- **new_final_projected_updated**
  - Data type: real
  - Description: Newly updated final projected sales quantity.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `merchant_id`, `product_id`, `outlet_id` could be used to join with other tables containing merchant, product, or outlet details.
- **Business-Critical Columns**: `projection_date`, `product_id`, `gmv`, `quantity`, `final_projected` are critical for understanding sales performance and projections.

### Potential Relationships
- **product_id** could relate to a `products` table.
- **merchant_id** could relate to a `merchants` table.
- **outlet_id** could relate to an `outlets` table.