# Table: blinkit.bistro_etls.fact_sales_cart_rank_details_bistro

### Description
The `blinkit.bistro_etls.fact_sales_cart_rank_details_bistro` table stores detailed information about sales orders, focusing on the ranking of carts based on various criteria such as delivery status. This table is used for analyzing customer behavior, order processing efficiency, and sales performance over time.

### Partitioning
This table is not partitioned. Queries on this table should be carefully optimized to ensure performance, as there are no partition keys to assist in data segregation.

### Columns
- **order_id**
  - Data type: bigint
  - Description: Unique identifier for each order.

- **dim_customer_key**
  - Data type: bigint
  - Description: Reference key to the customer dimension table, linking to detailed customer information.

- **order_create_dt_ist**
  - Data type: date
  - Description: The date when the order was created, in Indian Standard Time (IST).

- **order_current_status**
  - Data type: varchar
  - Description: Current status of the order (e.g., pending, completed, cancelled).

- **cart_rank**
  - Data type: bigint
  - Description: Ranking of the cart at the time of order creation based on predefined criteria.

- **delivered_cart_rank**
  - Data type: bigint
  - Description: Ranking of the cart at the time of delivery, which may differ from the initial cart rank due to changes during order processing.

- **update_dt_ist**
  - Data type: date
  - Description: The date when the record was last updated, in Indian Standard Time (IST).

- **update_reason**
  - Data type: varchar
  - Description: Reason for the last update made to the record.

- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp indicating when the data was last extracted, transformed, and loaded into the table, in Indian Standard Time (IST).

### Key Relationships and JOINs
- **Potential JOIN keys:**
  - `order_id` can be used to join with other sales or order-related tables that contain order details.
  - `dim_customer_key` is likely used to join with a customer dimension table, allowing for enriched customer analytics.

- **Business-critical columns:**
  - `order_id` and `dim_customer_key` are critical for linking sales data to specific orders and customers.
  - `order_current_status`, `cart_rank`, and `delivered_cart_rank` are essential for analyzing sales performance and order fulfillment efficiency.

### Inferential Relationships
- The presence of `dim_customer_key` suggests a relationship to a `customer` dimension table, which would store detailed demographic and behavioral data about customers.
- `order_id` might relate to other transactional tables like `order_details`, `payment_transactions`, or `shipping_information`, providing a comprehensive view of the order lifecycle.

This documentation provides a structured overview of the `fact_sales_cart_rank_details_bistro` table, essential for data analysis and business intelligence tasks within the organization.