# Table: blinkit.bistro_etls.bistro_agg_sales_excluding_dotd_v1

### Description
The `blinkit.bistro_etls.bistro_agg_sales_excluding_dotd_v1` table aggregates sales data for various products sold by different outlets, excluding those products that are part of the 'Deal of the Day' (DOTD). This table is likely used for analyzing sales performance, managing inventory, and understanding customer buying patterns on a daily and hourly basis.

### Partitioning
This table is not partitioned. Queries on this table may not benefit from partition pruning and could be slower, depending on the data volume and query complexity.

### Columns
- **dt_**
  - Data type: date
  - Description: The date on which the sales transactions occurred.

- **frontend_merchant_id**
  - Data type: bigint
  - Description: The identifier for the merchant on the frontend system.

- **product_id**
  - Data type: bigint
  - Description: The identifier for the product sold.

- **hr**
  - Data type: bigint
  - Description: The hour of the day when the sales transactions were recorded.

- **outlet_id**
  - Data type: integer
  - Description: The identifier for the outlet where the product was sold.

- **outlet_name**
  - Data type: varchar
  - Description: The name of the outlet where the product was sold.

- **quantity**
  - Data type: bigint
  - Description: The quantity of the product sold during the specified date and hour.

- **total_selling_price**
  - Data type: real
  - Description: The total revenue generated from the product sales, excluding DOTD items.

- **procured_quantity**
  - Data type: bigint
  - Description: The quantity of the product procured for sale.

- **dotd_pid**
  - Data type: integer
  - Description: The product identifier for items that are part of the 'Deal of the Day', used to exclude these from the aggregate calculations.

- **rnk**
  - Data type: bigint
  - Description: A ranking or order identifier, possibly used for sorting or prioritization within the dataset.

### Potential JOIN Keys and Business-Critical Columns
- **product_id**: Can be used to join with product tables to fetch product details.
- **outlet_id**: Can be used to join with outlet tables to fetch outlet details.
- **frontend_merchant_id**: Important for linking sales data with merchant information.

### Potential Relationships
- The `product_id` column suggests a relationship with a products table, which would contain details like product name, category, etc.
- The `outlet_id` column suggests a relationship with an outlets table, which would contain information like location, size, etc.
- The `frontend_merchant_id` might relate to a merchants table detailing merchant profiles and operations.

This documentation provides a comprehensive overview of the `blinkit.bistro_etls.bistro_agg_sales_excluding_dotd_v1` table, facilitating efficient data querying and management.