# Table: blinkit.bistro_etls.mapping_sub_dish_ing

### Description
The `blinkit.bistro_etls.mapping_sub_dish_ing` table maps dishes to their sub-dishes and ingredients, detailing the quantities and conversion metrics used for each ingredient. This table likely serves as a central repository for managing recipes and ingredient details in a culinary or restaurant management context.

### Partitioning
This table is not partitioned.

### Columns
- **dish_id**
    - Data type: bigint
    - Description: Unique identifier for the main dish.

- **dish_name**
    - Data type: varchar
    - Description: Name of the main dish.

- **sub_dish_id**
    - Data type: bigint
    - Description: Unique identifier for the sub-dish associated with the main dish.

- **subdish_name**
    - Data type: varchar
    - Description: Name of the sub-dish.

- **ing_id**
    - Data type: bigint
    - Description: Unique identifier for an ingredient used in the sub-dish.

- **ing_name**
    - Data type: varchar
    - Description: Name of the ingredient.

- **flag**
    - Data type: varchar
    - Description: A flag to indicate specific attributes or states related to the dish or ingredient (exact usage should be defined by business rules).

- **conv_id**
    - Data type: bigint
    - Description: Identifier for a conversion metric applicable to the ingredient.

- **qty**
    - Data type: double
    - Description: Quantity of the ingredient used in the sub-dish.

- **conv_name**
    - Data type: varchar
    - Description: Name or description of the conversion metric.

- **supply_id**
    - Data type: bigint
    - Description: Identifier for the supplier providing the ingredient.

- **cf**
    - Data type: varchar
    - Description: Conversion factor used for converting ingredient units, if applicable.

- **supply_name**
    - Data type: varchar
    - Description: Name of the supplier providing the ingredient.

- **conv_item_id**
    - Data type: bigint
    - Description: Unique identifier for a specific item or unit in the conversion metric system.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `dish_id`, `sub_dish_id`, `ing_id`, `conv_id`, `supply_id`, `conv_item_id` can be used to join with other tables that contain detailed information about dishes, ingredients, suppliers, or conversions.
- **Business-Critical Columns**: `dish_id`, `sub_dish_id`, `ing_id`, `qty`, and `supply_id` are critical for understanding the composition of dishes and managing inventory and supplier relationships.

### Potential Relationships
- The `dish_id` and `sub_dish_id` might relate to a `dishes` table that contains detailed descriptions of each dish.
- The `ing_id` could relate to an `ingredients` table detailing nutritional information or sourcing details.
- The `supply_id` might link to a `suppliers` table that includes contact information and terms.
- The `conv_id` and `conv_item_id` might connect to a `conversions` table that standardizes measurements across recipes.