# Table: blinkit.bistro_etls.user_properties_daily_bistro

### Description
The table `blinkit.bistro_etls.user_properties_daily_bistro` is designed to store daily snapshots of user properties related to their activity and financial metrics on the Blinkit platform. This table includes comprehensive details about customer interactions, financial transactions, and engagement metrics over different periods, making it a valuable resource for analyzing customer behavior, financial performance, and retention strategies.

### Partitioning
The table is partitioned by the key `bucket` with 10 partitions. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval and query performance.

### Columns
- **customer_id**
  - Data type: bigint
  - Description: Unique identifier for the customer.

- **snapshot_date_ist**
  - Data type: date
  - Description: The date when the snapshot of user properties was taken.

- **lifetime_gmv**
  - Data type: real
  - Description: The lifetime gross merchandise value generated by the customer.

- **lifetime_carts**
  - Data type: bigint
  - Description: Total number of shopping carts created by the customer over their lifetime.

- **lifetime_retained_margin**
  - Data type: real
  - Description: Total retained margin from the customer over their lifetime.

- **lifetime_delivery_cost**
  - Data type: real
  - Description: Total delivery costs associated with the customer's orders over their lifetime.

- **lifetime_slot_charges**
  - Data type: real
  - Description: Total slot charges incurred by the customer over their lifetime.

- **lifetime_packaging_cost**
  - Data type: real
  - Description: Total packaging costs for the customer's orders over their lifetime.

- **lifetime_tip_amount**
  - Data type: real
  - Description: Total tip amount given by the customer over their lifetime.

- **lifetime_promo_discount**
  - Data type: real
  - Description: Total promotional discounts availed by the customer over their lifetime.

- **lifetime_promo_cashback**
  - Data type: real
  - Description: Total promotional cashback received by the customer over their lifetime.

- **first_cart_checkout_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the first cart checkout by the customer.

- **second_last_cart_checkout_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the second to last cart checkout by the customer.

- **last_cart_checkout_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the most recent cart checkout by the customer.

- **customer_age_in_months**
  - Data type: bigint
  - Description: Age of the customer account in months.

- **customer_age_in_days**
  - Data type: bigint
  - Description: Age of the customer account in days.

- **recent_city_ordered_from**
  - Data type: varchar
  - Description: Most recent city from which the customer has ordered.

- **last_2_orders_recency_in_months**
  - Data type: bigint
  - Description: Recency of the last two orders in months.

- **total_retained_margin_6_months**
  - Data type: bigint
  - Description: Total retained margin from the customer in the last 6 months.

- **total_selling_price_6_months**
  - Data type: bigint
  - Description: Total selling price of items purchased by the customer in the last 6 months.

- **total_retained_margin_3_month**
  - Data type: bigint
  - Description: Total retained margin from the customer in the last 3 months.

- **total_selling_price_3_month**
  - Data type: bigint
  - Description: Total selling price of items purchased by the customer in the last 3 months.

- **total_gmv_last_3_month**
  - Data type: bigint
  - Description: Gross merchandise value generated by the customer in the last 3 months.

- **count_cart_last_3_month**
  - Data type: bigint
  - Description: Number of carts created by the customer in the last 3 months.

- **total_gmv_last_6_month**
  - Data type: bigint
  - Description: Gross merchandise value generated by the customer in the last 6 months.

- **count_cart_last_6_month**
  - Data type: bigint
  - Description: Number of carts created by the customer in the last 6 months.

- **count_cart_last_1_month**
  - Data type: bigint
  - Description: Number of carts created by the customer in the last month.

- **count_cart_last_2_month**
  - Data type: bigint
  - Description: Number of carts created by the customer in the last 2 months.

- **total_gmv_last_1_month**
  - Data type: bigint
  - Description: Gross merchandise value generated by the customer in the last month.

- **total_gmv_last_2_month**
  - Data type: bigint
  - Description: Gross merchandise value generated by the customer in the last 2 months.

- **top_payment_method_last_6_months**
  - Data type: varchar
  - Description: Most frequently used payment method by the customer in the last 6 months.

- **top_city_ordered_from_last_6_months**
  - Data type: varchar
  - Description: City from which the customer most frequently ordered in the last 6 months.

- **average_order_value**
  - Data type: double
  - Description: Average order value of the customer over their lifetime.

- **average_order_value_last_3_months**
  - Data type: double
  - Description: Average order value of the customer in the last 3 months.

- **average_order_value_last_6_months**
  - Data type: double
  - Description: Average order value of the customer in the last 6 months.

- **lifetime_retained_margin_percentage**
  - Data type: double
  - Description: Percentage of retained margin relative to total GMV over the customer's lifetime.

- **retained_margin_last_6_months_percentage**
  - Data type: double
  - Description: Percentage of retained margin relative to total GMV in the last 6 months.

- **retained_margin_last_3_months_percentage**
  - Data type: double
  - Description: Percentage of retained margin relative to total GMV in the last 3 months.

- **monthly_average_cart_count_last_6_months**
  - Data type: double
  - Description: Monthly average number of carts created by the customer in the last 6 months.

- **weekly_average_cart_count_last_6_months**
  - Data type: double
  - Description: Weekly average number of carts created by the customer in the last 6 months.

- **monthly_average_gmv_last_3_months**
  - Data type: double
  - Description: Monthly average GMV generated by the customer in the last 3 months.

- **weekly_average_gmv_last_3_months**
  - Data type: double
  - Description: Weekly average GMV generated by the customer in the last 3 months.

- **prim_city_last_6m**
  - Data type: varchar
  - Description: Primary city from which the customer ordered in the last 6 months.

- **max_order_merchant_id**
  - Data type: bigint
  - Description: Merchant ID that received the maximum orders from the customer.

- **prim_merchant_last_6m**
  - Data type: varchar
  - Description: Primary merchant from whom the customer ordered in the last 6 months.

- **cities_ordered_from_in_last_6_months**
  - Data type: varchar
  - Description: List of cities from which the customer has ordered in the last 6 months.

### Potential JOIN Keys and Business-Critical Columns
- **customer_id**: Likely JOIN key with other customer-related tables.
- **snapshot_date_ist**: Useful for time-series analysis.
- **lifetime_gmv**, **lifetime_carts**, **lifetime_retained_margin**: Critical for financial and customer lifetime value analysis.

### Potential Relationships
- **customer_id** could be used to join with other tables containing detailed order, payment, or customer demographic information.
- **max_order_merchant_id** could be linked to a merchant table for detailed merchant analysis.
- **recent_city_ordered_from**, **top_city_ordered_from_last_6_months**, **prim_city_last_6m**, and **cities_ordered_from_in_last_6_months** suggest potential joins with geographic or city-based tables for regional analysis.