# Table: blinkit.bistro_etls.weighted_landing_price_logs_bistro_bck_new

### Description
The `blinkit.bistro_etls.weighted_landing_price_logs_bistro_bck_new` table is designed to store historical logs of weighted landing prices for products at different outlets. It likely serves as a tracking mechanism for price changes over time and helps in analyzing the pricing strategy across various locations.

### Partitioning
The table is not partitioned. This may impact performance on large datasets, as partitioning can help optimize query execution by narrowing down the data scanned.

### Columns
- **product_id**
  - Data type: integer
  - Description: Unique identifier for a product.

- **outlet_id**
  - Data type: integer
  - Description: Unique identifier for an outlet where the product is sold.

- **wlp**
  - Data type: double
  - Description: Weighted landing price of the product at the specified outlet.

- **is_current**
  - Data type: boolean
  - Description: Indicates whether the recorded price is the current active price.

- **dbt_scd_id**
  - Data type: varchar
  - Description: Slowly Changing Dimension (SCD) identifier used for tracking changes in data over time.

- **dbt_updated_at**
  - Data type: timestamp(6)
  - Description: Timestamp indicating when the record was last updated.

- **dbt_valid_from**
  - Data type: timestamp(6)
  - Description: Timestamp indicating the start time from which the record is considered valid.

- **dbt_valid_to**
  - Data type: timestamp(6)
  - Description: Timestamp indicating the end time until which the record is considered valid.

### Key Relationships and JOINs
- Potential JOIN keys include `product_id` and `outlet_id`, which can be used to link this table with other tables containing detailed product information or outlet details.
- Business-critical columns include `product_id`, `outlet_id`, and `wlp` as they directly relate to the pricing data which is crucial for business analysis.

### Potential Relationships
- The `product_id` can be used to join with a `products` table to fetch product details.
- The `outlet_id` might be used to join with an `outlets` table to get more information about the location or characteristics of the outlet.

This documentation provides a concise overview of the structure and purpose of the `weighted_landing_price_logs_bistro_bck_new` table, which is essential for efficient data management and utilization in business analysis tasks.