# Table: blinkit.bistro_etls.order_poi_mapping

### Description
The table `blinkit.bistro_etls.order_poi_mapping` is designed to store mappings between orders and points of interest (POIs), along with additional order-related details. This table likely serves as a critical component for analyzing order distribution, customer behavior, and operational metrics across different geographic locations and times.

### Partitioning
The table is partitioned on the column `order_checkout_dt_ist`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **week**
  - Data type: date
  - Description: Represents the week when the order was placed.

- **poi_id**
  - Data type: integer
  - Description: Unique identifier for the point of interest associated with the order.

- **poi_name**
  - Data type: varchar
  - Description: Name of the point of interest associated with the order.

- **order_checkout_dt_ist**
  - Data type: date
  - Description: The date and time when the order was checked out, in Indian Standard Time.

- **merchant_id**
  - Data type: integer
  - Description: Identifier for the merchant from whom the order was placed.

- **city_name**
  - Data type: varchar
  - Description: Name of the city where the order was placed.

- **order_id**
  - Data type: integer
  - Description: Unique identifier for the order.

- **dim_customer_key**
  - Data type: integer
  - Description: Dimension key that links to the customer who placed the order.

- **total_selling_price**
  - Data type: real
  - Description: Total selling price of the order.

- **cart_rank**
  - Data type: integer
  - Description: Ranking of the cart used in the order based on some criteria (e.g., size, value).

- **delivered_time_sec**
  - Data type: integer
  - Description: Time taken for the order to be delivered, in seconds.

- **total_product_quantity**
  - Data type: integer
  - Description: Total quantity of products included in the order.

### Key Relationships and Joins
- The `order_id` can be used to join this table with other order-related tables to fetch detailed order information.
- The `dim_customer_key` might be used to join with a customer dimension table to retrieve detailed customer profiles.
- The `merchant_id` could link to a merchant table, providing additional details about the merchants.

### Business-Critical Columns
- **order_id**: Essential for tracking and analyzing individual orders.
- **total_selling_price**: Important for financial and sales performance analysis.
- **order_checkout_dt_ist**: Critical for partitioning and temporal analysis.

This documentation provides a structured overview of the `blinkit.bistro_etls.order_poi_mapping` table, ensuring optimal use and integration within the broader data ecosystem.