# Table: blinkit.bistro_etls.merchant_ingredient_mapping

### Description
The `blinkit.bistro_etls.merchant_ingredient_mapping` table is designed to map ingredients to dishes and subdishes provided by various merchants. It includes details about the ingredients, dishes, and their relationships, as well as some conversion metrics and flags for further categorization or processing.

### Partitioning
The table is partitioned on the column `updated_at_ist`. This column is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition.

### Columns
- **updated_at_ist**
    - Data type: timestamp(6)
    - Description: The timestamp indicating when the record was last updated; used for partitioning the table.
    
- **merchant_id**
    - Data type: bigint
    - Description: Unique identifier for the merchant.
    
- **merchant_name**
    - Data type: varchar
    - Description: The name of the merchant.
    
- **dish_id**
    - Data type: bigint
    - Description: Unique identifier for the dish.
    
- **dish_name**
    - Data type: varchar
    - Description: The name of the dish.
    
- **subdish_id**
    - Data type: bigint
    - Description: Unique identifier for the subdish, which is a part of a main dish.
    
- **subdish_name**
    - Data type: varchar
    - Description: The name of the subdish.
    
- **ing_id**
    - Data type: bigint
    - Description: Unique identifier for the ingredient.
    
- **ing_name**
    - Data type: varchar
    - Description: The name of the ingredient.
    
- **mapping_type**
    - Data type: varchar
    - Description: The type of mapping between the ingredient and the dish or subdish.
    
- **is_condiment**
    - Data type: boolean
    - Description: Flag to indicate whether the ingredient is considered a condiment.
    
- **flag**
    - Data type: varchar
    - Description: A general-purpose flag that can be used for additional categorization or status marking.
    
- **conv_name**
    - Data type: varchar
    - Description: Name of the conversion metric or category.
    
- **conv_id**
    - Data type: bigint
    - Description: Unique identifier for the conversion metric or category.
    
- **conv_item_id**
    - Data type: bigint
    - Description: Identifier linking conversion items.
    
- **ing_item_id**
    - Data type: bigint
    - Description: Identifier for specific ingredient items.
    
- **subdish_item_id**
    - Data type: bigint
    - Description: Identifier for specific subdish items.
    
- **cf**
    - Data type: real
    - Description: Conversion factor used in ingredient quantity calculations.
    
- **deliver_qty**
    - Data type: bigint
    - Description: The quantity of the ingredient to be delivered.
    
- **prep_qty**
    - Data type: bigint
    - Description: The quantity of the ingredient prepared.
    
- **pid_to_consider**
    - Data type: bigint
    - Description: Product ID that should be considered for some specific operations or calculations.
    
- **pname_to_consider**
    - Data type: varchar
    - Description: Product name that should be considered for specific operations or calculations.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys:** `merchant_id`, `dish_id`, `subdish_id`, `ing_id` could be used to join with other tables that contain merchant, dish, subdish, or ingredient details.
- **Business-Critical Columns:** `merchant_id`, `dish_id`, `ing_id`, `mapping_type`, `is_condiment` are critical for understanding the relationships and characteristics of the ingredients in the context of the dishes they belong to.

### Potential Relationships
- This table could potentially relate to other tables like `merchants`, `dishes`, `ingredients`, or `subdishes` based on the IDs provided (`merchant_id`, `dish_id`, `subdish_id`, `ing_id`). These relationships are crucial for comprehensive analysis across different dimensions of food service data.