# Table: blinkit.bistro_etls.bistro_dish_category

### Description
The `blinkit.bistro_etls.bistro_dish_category` table stores detailed information about dishes offered by various merchants at different outlets, categorized by various levels and types. It includes data on dish identification, sales performance, and categorization, which can be used for menu analysis, sales forecasting, and strategic business decisions.

### Partitioning
- **Partition Key:** `level`
  - The table is partitioned on the `level` column. This is CRITICAL for query performance and the `level` MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **merchant_id**
  - Data type: varchar
  - Description: Unique identifier for the merchant offering the dish.
  
- **outlet_id**
  - Data type: varchar
  - Description: Unique identifier for the outlet where the dish is available.
  
- **city_name**
  - Data type: varchar
  - Description: Name of the city where the outlet is located.
  
- **dish_pid**
  - Data type: integer
  - Description: A unique product identifier for the dish.
  
- **dish_item_id**
  - Data type: integer
  - Description: Specific item identifier for the dish.
  
- **dish_name_**
  - Data type: varchar
  - Description: The name of the dish.
  
- **l1**
  - Data type: varchar
  - Description: The top-level category for the dish.
  
- **l2**
  - Data type: varchar
  - Description: The sub-level category under the top-level category.
  
- **p_type**
  - Data type: varchar
  - Description: Type of the dish, possibly indicating preparation style or other attributes.
  
- **qpd**
  - Data type: double
  - Description: Quantity per day, indicating how many units of the dish are sold on average per day.
  
- **gmv**
  - Data type: double
  - Description: Gross Merchandise Value, indicating total sales value of the dish.
  
- **category**
  - Data type: varchar
  - Description: General category of the dish.
  
- **replication_flag**
  - Data type: integer
  - Description: Flag indicating if the dish data is replicated across different data stores or systems.
  
- **level**
  - Data type: varchar
  - Description: The level of detail or hierarchy at which the dish data is categorized.
  
- **replication_city**
  - Data type: varchar
  - Description: City name where the dish data is replicated, if applicable.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys:** `merchant_id`, `outlet_id`, `dish_pid`, `dish_item_id` could be used to join with other tables that contain merchant, outlet, or dish details.
- **Business-Critical Columns:** `dish_pid`, `qpd`, `gmv` are crucial for analyzing the performance and popularity of dishes.

### Potential Relationships
- The `merchant_id` and `outlet_id` columns suggest a relationship to tables containing details about merchants and outlets.
- The `dish_pid` and `dish_item_id` columns suggest a relationship to inventory or product tables where detailed dish specifications are stored.