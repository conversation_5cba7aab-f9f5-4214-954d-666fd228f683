# Table: blinkit.bistro_etls.daily_item_inventory_log

### Description
The `blinkit.bistro_etls.daily_item_inventory_log` table is designed to log daily inventory levels for items at various outlets. It tracks inventory changes, including additions and reductions, and records these events with timestamps for specific products and merchants. This table is crucial for managing stock levels, analyzing inventory trends, and optimizing supply chain operations.

### Partitioning
The table is partitioned by the `date_` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **outlet_id**
  - Data type: bigint
  - Description: The unique identifier for the outlet where the inventory is recorded.

- **merchant_id**
  - Data type: bigint
  - Description: The unique identifier for the merchant associated with the inventory record.

- **merchant_name**
  - Data type: varchar
  - Description: The name of the merchant associated with the inventory record.

- **item_id**
  - Data type: bigint
  - Description: The unique identifier for the item whose inventory is being logged.

- **inventory**
  - Data type: bigint
  - Description: The quantity of the item available in inventory at the time of logging.

- **location**
  - Data type: varchar
  - Description: The physical location within the outlet where the item is stored.

- **date_**
  - Data type: date
  - Description: The date when the inventory record was logged.

- **hour_**
  - Data type: bigint
  - Description: The hour of the day when the inventory change was recorded.

- **update_at_ist**
  - Data type: timestamp(6)
  - Description: The timestamp, in Indian Standard Time, when the inventory record was last updated.

- **product_id**
  - Data type: bigint
  - Description: The unique identifier for the product related to the item.

- **product_name**
  - Data type: varchar
  - Description: The name of the product related to the item.

- **delta_qty**
  - Data type: bigint
  - Description: The change in quantity of the item, indicating how much was added or removed during the update.

- **state**
  - Data type: varchar
  - Description: The state of the inventory, possibly indicating conditions like 'available', 'low stock', or 'out of stock'.

- **operation_type**
  - Data type: varchar
  - Description: The type of operation performed on the inventory, such as 'addition', 'deletion', or 'modification'.

- **sort_key_**
  - Data type: varchar
  - Description: A sorting key used internally for query optimization.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: 
  - `outlet_id` can be used to join with outlet-related tables.
  - `merchant_id` can be used to join with merchant-related tables.
  - `item_id` and `product_id` can be used to join with product or item catalog tables.

- **Business-Critical Columns**: 
  - `inventory`, `delta_qty`, and `update_at_ist` are critical for real-time inventory management and trend analysis.
  - `date_` is essential for historical data analysis and must be used in partitioning queries.

### Potential Relationships
- The `product_id` and `item_id` suggest a relationship with a products or items table, which would store detailed information about the products.
- The `merchant_id` indicates a possible link to a merchants table, containing details about the merchants.
- The `outlet_id` may relate to an outlets table with information on each physical store location.