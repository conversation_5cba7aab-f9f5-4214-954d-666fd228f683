# Table: blinkit.bistro_etls.user_properties_daily_base_bistro

### Description
The `blinkit.bistro_etls.user_properties_daily_base_bistro` table is designed to store comprehensive daily snapshots of customer metrics and behaviors within the Bistro platform. It includes a variety of lifetime and recent activity metrics such as gross merchandise value (GMV), cart counts, retained margins, and costs associated with each customer. This data is essential for analyzing customer value, retention, and purchasing patterns over different periods.

### Partitioning
The table is partitioned on the `snapshot_date_ist` column with a bucketing strategy of 10. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

### Columns
- **customer_id**
  - Data type: bigint
  - Description: Unique identifier for each customer.

- **snapshot_date_ist**
  - Data type: date
  - Description: The date of the data snapshot, reflecting the customer's state at this point.

- **lifetime_gmv**
  - Data type: real
  - Description: Total gross merchandise value generated by the customer over their lifetime.

- **lifetime_carts**
  - Data type: bigint
  - Description: Total number of shopping carts created by the customer over their lifetime.

- **lifetime_retained_margin**
  - Data type: real
  - Description: Total profit retained after accounting for costs, over the customer's lifetime.

- **lifetime_delivery_cost**
  - Data type: real
  - Description: Total delivery costs associated with the customer's orders over their lifetime.

- **lifetime_slot_charges**
  - Data type: real
  - Description: Total charges for delivery slots over the customer's lifetime.

- **lifetime_packaging_cost**
  - Data type: real
  - Description: Total cost of packaging for the customer's orders over their lifetime.

- **lifetime_tip_amount**
  - Data type: real
  - Description: Total tip amount given by the customer over their lifetime.

- **lifetime_promo_discount**
  - Data type: real
  - Description: Total promotional discounts availed by the customer over their lifetime.

- **lifetime_promo_cashback**
  - Data type: real
  - Description: Total cashback received by the customer through promotions over their lifetime.

- **first_cart_checkout_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the customer's first cart checkout.

- **second_last_cart_checkout_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the customer's second to last cart checkout.

- **last_cart_checkout_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the customer's most recent cart checkout.

- **customer_age_in_months**
  - Data type: bigint
  - Description: Age of the customer account in months.

- **customer_age_in_days**
  - Data type: bigint
  - Description: Age of the customer account in days.

- **recent_city_ordered_from**
  - Data type: varchar
  - Description: Most recent city from which the customer has ordered.

- **last_2_orders_recency_in_months**
  - Data type: bigint
  - Description: Recency of the last two orders in months.

- **total_retained_margin_6_months**
  - Data type: bigint
  - Description: Total retained margin from the customer in the last 6 months.

- **total_selling_price_6_months**
  - Data type: bigint
  - Description: Total selling price of goods purchased by the customer in the last 6 months.

- **total_retained_margin_3_month**
  - Data type: bigint
  - Description: Total retained margin from the customer in the last 3 months.

- **total_selling_price_3_month**
  - Data type: bigint
  - Description: Total selling price of goods purchased by the customer in the last 3 months.

- **total_gmv_last_3_month**
  - Data type: bigint
  - Description: Total GMV generated by the customer in the last 3 months.

- **count_cart_last_3_month**
  - Data type: bigint
  - Description: Count of shopping carts created by the customer in the last 3 months.

- **total_gmv_last_6_month**
  - Data type: bigint
  - Description: Total GMV generated by the customer in the last 6 months.

- **count_cart_last_6_month**
  - Data type: bigint
  - Description: Count of shopping carts created by the customer in the last 6 months.

- **count_cart_last_1_month**
  - Data type: bigint
  - Description: Count of shopping carts created by the customer in the last month.

- **count_cart_last_2_month**
  - Data type: bigint
  - Description: Count of shopping carts created by the customer in the last 2 months.

- **total_gmv_last_1_month**
  - Data type: bigint
  - Description: Total GMV generated by the customer in the last month.

- **total_gmv_last_2_month**
  - Data type: bigint
  - Description: Total GMV generated by the customer in the last 2 months.

- **top_payment_method_last_6_months**
  - Data type: varchar
  - Description: Most frequently used payment method by the customer in the last 6 months.

- **top_city_ordered_from_last_6_months**
  - Data type: varchar
  - Description: City from which the customer most frequently ordered in the last 6 months.

- **average_order_value**
  - Data type: double
  - Description: Average value of orders placed by the customer over their lifetime.

- **average_order_value_last_3_months**
  - Data type: double
  - Description: Average value of orders placed by the customer in the last 3 months.

- **average_order_value_last_6_months**
  - Data type: double
  - Description: Average value of orders placed by the customer in the last 6 months.

- **lifetime_retained_margin_percentage**
  - Data type: double
  - Description: Percentage of retained margin relative to total GMV over the customer's lifetime.

- **retained_margin_last_6_months_percentage**
  - Data type: double
  - Description: Percentage of retained margin relative to total GMV in the last 6 months.

- **retained_margin_last_3_months_percentage**
  - Data type: double
  - Description: Percentage of retained margin relative to total GMV in the last 3 months.

- **monthly_average_cart_count_last_6_months**
  - Data type: double
  - Description: Monthly average number of carts created by the customer in the last 6 months.

- **weekly_average_cart_count_last_6_months**
  - Data type: double
  - Description: Weekly average number of carts created by the customer in the last 6 months.

- **monthly_average_gmv_last_3_months**
  - Data type: double
  - Description: Monthly average GMV generated by the customer in the last 3 months.

- **weekly_average_gmv_last_3_months**
  - Data type: double
  - Description: Weekly average GMV generated by the customer in the last 3 months.

- **prim_city_last_6m**
  - Data type: varchar
  - Description: Primary city from which the customer ordered in the last 6 months.

- **max_order_merchant_id**
  - Data type: bigint
  - Description: Merchant ID that received the highest order value from the customer.

- **prim_merchant_last_6m**
  - Data type: varchar
  - Description: Primary merchant from whom the customer ordered in the last 6 months.

- **cities_ordered_from_in_last_6_months**
  - Data type: varchar
  - Description: List of cities from which the customer has ordered in the last 6 months.

### Potential JOIN Keys and Business-Critical Columns
- **customer_id**: Likely JOIN key with other customer-related tables.
- **snapshot_date_ist**: Essential for time-based analysis and JOINs with date-partitioned tables.
- **recent_city_ordered_from**, **top_city_ordered_from_last_6_months**, **prim_city_last_6m**: Potential JOINs with geographic or city-based tables.
- **max_order_merchant_id**, **prim_merchant_last_6m**: Potential JOINs with merchant-related tables.

### Potential Relationships
- Columns like `customer_id` suggest relationships with tables containing detailed customer information or transaction records.
- `max_order_merchant_id` and `prim_merchant_last_6m` suggest potential links to merchant or vendor tables.

This documentation provides a structured and detailed view of the `blinkit.bistro_etls.user_properties_daily_base_bistro` table, facilitating efficient data queries and analysis within the Bistro data ecosystem.