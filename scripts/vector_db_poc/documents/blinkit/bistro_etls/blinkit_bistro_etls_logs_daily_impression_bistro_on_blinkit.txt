# Table: blinkit.bistro_etls.logs_daily_impression_bistro_on_blinkit

### Description
The table `blinkit.bistro_etls.logs_daily_impression_bistro_on_blinkit` is designed to store daily logs of user interactions with various widgets and features within the Blinkit application. It captures detailed information about the user's device, application version, and specific actions taken within the app, such as page views and search keywords. This data is crucial for analyzing user behavior, improving user experience, and tracking the performance of different app features over time.

### Partitioning
The table is partitioned on the following keys, which are CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition:
- `at_date_ist`: The date of the event, stored as a varchar.
- `name`: The name of the event or interaction, stored as a varchar.

### Columns
- **at_date_ist**
  - Data type: varchar
  - Description: The date on which the event was logged, in IST timezone.

- **at_ist**
  - Data type: timestamp(6)
  - Description: The exact timestamp of the event in IST timezone.

- **device_uuid**
  - Data type: varchar
  - Description: A unique identifier for the device from which the event was logged.

- **traits__city_name**
  - Data type: varchar
  - Description: The city name associated with the user's location.

- **name**
  - Data type: varchar
  - Description: The name of the event or interaction logged.

- **properties__widget_name**
  - Data type: varchar
  - Description: The name of the widget involved in the event.

- **properties__child_widget_title**
  - Data type: varchar
  - Description: The title of any child widget involved in the event.

- **properties__page_title**
  - Data type: varchar
  - Description: The title of the page where the event occurred.

- **properties__page_name**
  - Data type: varchar
  - Description: The name of the page where the event occurred.

- **properties__widget_title**
  - Data type: varchar
  - Description: The title of the widget involved in the event.

- **properties__icon**
  - Data type: varchar
  - Description: The icon associated with the widget or event.

- **properties__suggestion_value**
  - Data type: varchar
  - Description: Any suggestion value provided during the event.

- **properties__search_actual_keyword**
  - Data type: varchar
  - Description: The actual keyword used in a search during the event.

- **properties__search_input_keyword**
  - Data type: varchar
  - Description: The input keyword provided by the user during a search.

- **app__version**
  - Data type: varchar
  - Description: The version of the Blinkit app being used when the event was logged.

- **device__manufacturer**
  - Data type: varchar
  - Description: The manufacturer of the device.

- **device__model**
  - Data type: varchar
  - Description: The model of the device.

- **device__name**
  - Data type: varchar
  - Description: The name of the device.

- **os__version**
  - Data type: varchar
  - Description: The operating system version of the device.

- **platform**
  - Data type: varchar
  - Description: The platform (iOS, Android, etc.) of the device.

### Potential JOIN Keys and Business-Critical Columns
- `device_uuid` could be used to join with other tables that contain device-specific information.
- Business-critical columns include `at_date_ist`, `name`, `properties__page_name`, and `properties__search_actual_keyword` as they provide direct insights into user engagement and behavior.

### Potential Relationships
- The columns prefixed with `properties__` suggest that this table could be related to other tables that store detailed properties of pages or widgets.
- `app__version`, `device__manufacturer`, `device__model`, `device__name`, and `os__version` suggest potential joins with tables containing app or device metadata for more detailed analysis.