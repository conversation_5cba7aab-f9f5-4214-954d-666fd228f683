# Table: blinkit.bistro_etls.search_keyword_city_product_additions

### Description
The `blinkit.bistro_etls.search_keyword_city_product_additions` table stores data related to user search queries for products on different platforms, segmented by city and date. It likely serves to analyze the performance of search terms in driving product additions to carts (ATCs) and generating gross merchandise value (GMV) across various cities.

### Partitioning
The table is partitioned on the column `at_date_ist`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **at_date_ist**
  - Data type: date
  - Description: The date in IST (Indian Standard Time) on which the search and product additions occurred.

- **city_id**
  - Data type: bigint
  - Description: Identifier for the city where the search query was made.

- **search_query**
  - Data type: varchar
  - Description: The actual text of the search query input by the user.

- **platform**
  - Data type: varchar
  - Description: The platform (e.g., mobile app, website) on which the search was performed.

- **product_id**
  - Data type: bigint
  - Description: Identifier for the product that was added to the cart following the search query.

- **atcs**
  - Data type: bigint
  - Description: The number of times the product was added to carts as a result of the search query.

- **gmv**
  - Data type: double
  - Description: Gross Merchandise Value generated from the product additions to the cart.

- **atc_dau**
  - Data type: bigint
  - Description: Daily active users who added the product to their cart on the specific date.

### Key Relationships and Joins
- The `product_id` column can be used to join this table with product tables to fetch detailed product information.
- The `city_id` could potentially link to a city or geographical location table for more detailed demographic or geographic analysis.

### Business-Critical Columns
- **product_id**: Crucial for understanding product-specific performance.
- **gmv**: Important for financial analysis and measuring economic impact.
- **atcs**: Key metric for marketing and sales conversion analysis.

This documentation provides a structured view of the `blinkit.bistro_etls.search_keyword_city_product_additions` table, facilitating efficient and effective data querying and analysis.