# Table: blinkit.bistro_etls.dish_ingredient_pack_info

### Description
The `blinkit.bistro_etls.dish_ingredient_pack_info` table stores detailed information about dishes, their sub-dishes, and associated ingredients used in a bistro setting. It includes data on ingredient names, quantities, and conversion factors, which are essential for inventory and recipe management.

### Partitioning
- **Partition Key**: `dish_id`
  - The table is partitioned by `dish_id`. This key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

### Columns
- **dish_id**
  - Data type: bigint
  - Description: Unique identifier for the main dish.

- **dish_name**
  - Data type: varchar
  - Description: Name of the main dish.

- **sub_dish_id**
  - Data type: bigint
  - Description: Unique identifier for a sub-dish related to the main dish.

- **subdish_name**
  - Data type: varchar
  - Description: Name of the sub-dish.

- **ing_id**
  - Data type: bigint
  - Description: Unique identifier for an ingredient used in the dish or sub-dish.

- **ing_name**
  - Data type: varchar
  - Description: Name of the ingredient.

- **flag**
  - Data type: varchar
  - Description: A flag to indicate specific states or conditions related to the dish or ingredient (details unspecified).

- **conv_name**
  - Data type: varchar
  - Description: Name of the unit conversion applicable to the ingredient.

- **conv_id**
  - Data type: bigint
  - Description: Unique identifier for the unit conversion.

- **item_id**
  - Data type: integer
  - Description: Identifier for an item, potentially linking to inventory or catalog items.

- **cf**
  - Data type: decimal(13,4)
  - Description: Conversion factor used for converting ingredient quantities.

- **deliver_qty**
  - Data type: bigint
  - Description: Quantity of the ingredient to be delivered or used.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: 
  - `dish_id` can be used to join with other tables that contain dish-related information.
  - `ing_id` might be used to join with tables containing detailed ingredient data.
  - `item_id` could link to inventory or product tables.

- **Business-Critical Columns**: 
  - `dish_id`, `ing_id`, and `deliver_qty` are critical for inventory management and recipe costing.

### Potential Relationships to Other Tables
- Tables containing detailed dish descriptions, nutritional information, or pricing might join on `dish_id`.
- Ingredient sourcing, cost, and supplier information might be related through `ing_id`.
- Inventory management or product catalog tables might relate through `item_id`.