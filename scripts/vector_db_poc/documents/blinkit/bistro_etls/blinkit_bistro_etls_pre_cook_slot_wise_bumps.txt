# Table: blinkit.bistro_etls.pre_cook_slot_wise_bumps

### Description
The `blinkit.bistro_etls.pre_cook_slot_wise_bumps` table is designed to store statistical data related to the sales performance of various dishes at different time slots across outlets. It aggregates sales data over the last 7 days, providing insights into potential and actual sales quantities, as well as deviations in sales performance. This information is crucial for inventory and sales strategy planning.

### Partitioning
The table is partitioned by the `updated_at_ts` column. This is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **outlet_id**
  - Data type: integer
  - Description: The unique identifier for an outlet.

- **hour_slot**
  - Data type: integer
  - Description: Represents a specific hour of the day, likely used to segment data by time.

- **sub_dish_pid**
  - Data type: integer
  - Description: The product identifier for a sub-dish.

- **sub_dish_name**
  - Data type: varchar
  - Description: The name of the sub-dish.

- **median_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The median of the estimated sales quantity for the last 7 days.

- **avg_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The average estimated sales quantity over the last 7 days.

- **max_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The maximum estimated sales quantity over the last 7 days.

- **min_potential_sale_quantity_last_7d**
  - Data type: double
  - Description: The minimum estimated sales quantity over the last 7 days.

- **median_quantity_sold_last_7d**
  - Data type: double
  - Description: The median of actual sales quantity for the last 7 days.

- **avg_quantity_sold_last_7d**
  - Data type: double
  - Description: The average actual sales quantity over the last 7 days.

- **max_quantity_sold_last_7d**
  - Data type: double
  - Description: The maximum actual sales quantity over the last 7 days.

- **min_quantity_sold_last_7d**
  - Data type: double
  - Description: The minimum actual sales quantity over the last 7 days.

- **deviation_days**
  - Data type: double
  - Description: The number of days with significant deviation from expected sales.

- **avg_deviation_positive**
  - Data type: double
  - Description: The average positive deviation from the expected sales quantity.

- **custom_weighted_deviation**
  - Data type: double
  - Description: A custom metric that weights deviations in sales quantities.

- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: The timestamp of the last update to the record.

- **date_**
  - Data type: date
  - Description: The date for which the data is relevant.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `outlet_id` and `sub_dish_pid` could be used to join with other tables that contain detailed outlet or dish information.
- **Business-Critical Columns**: Sales quantity columns (`median_quantity_sold_last_7d`, `avg_quantity_sold_last_7d`, etc.) are critical for analyzing performance and planning.

### Potential Relationships
- This table might relate to other tables containing detailed information about outlets (`outlet_id`) or dishes (`sub_dish_pid`), such as `outlets` or `dishes` tables.