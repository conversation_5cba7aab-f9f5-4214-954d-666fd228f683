# Table: blinkit.bistro_etls.merchant_group_sales_score

### Description
The `merchant_group_sales_score` table in the `blinkit.bistro_etls` schema is designed to track and analyze the sales performance of merchants within specific groups over various time frames. This table provides insights into sales scores and order volumes over 3, 7, and 30 days, facilitating performance comparisons and trend analysis.

### Partitioning
The table is partitioned on the column `snapshot_ts_ist`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for a merchant.

- **group_id**
  - Data type: bigint
  - Description: Identifier for the group to which the merchant belongs.

- **sales_score**
  - Data type: double
  - Description: Overall sales performance score of the merchant.

- **orders_per_day_30_days**
  - Data type: double
  - Description: Average number of orders per day for the merchant over the past 30 days.

- **sales_score_30_days**
  - Data type: double
  - Description: Sales performance score of the merchant over the past 30 days.

- **orders_per_day_7_days**
  - Data type: double
  - Description: Average number of orders per day for the merchant over the past 7 days.

- **sales_score_7_days**
  - Data type: double
  - Description: Sales performance score of the merchant over the past 7 days.

- **orders_per_day_3_days**
  - Data type: double
  - Description: Average number of orders per day for the merchant over the past 3 days.

- **sales_score_3_days**
  - Data type: double
  - Description: Sales performance score of the merchant over the past 3 days.

- **snapshot_ts_ist**
  - Data type: date
  - Description: The timestamp (in IST) when the data snapshot was taken. This is the partition key.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id**: Likely used to join with other tables containing merchant details.
- **group_id**: Could be used to join with group information tables to fetch additional group attributes.
- **sales_score**, **sales_score_30_days**, **sales_score_7_days**, **sales_score_3_days**: These are business-critical columns as they directly relate to the performance metrics of merchants.

### Potential Relationships to Other Tables
- **merchant_id** could be used to join with a `merchants` table to fetch merchant details.
- **group_id** might relate to a `merchant_groups` table that contains additional information about each group.

This documentation provides a clear and concise overview of the `merchant_group_sales_score` table, ensuring efficient usage and integration within the database environment.