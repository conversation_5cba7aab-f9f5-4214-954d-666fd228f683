# Table: blinkit.bistro_etls.bistro_hourly_inventory_snapshots_be

### Description
The `blinkit.bistro_etls.bistro_hourly_inventory_snapshots_be` table is designed to store hourly snapshots of inventory data for different facilities and products. This table is crucial for managing stock levels, understanding inventory trends, and planning logistics and replenishment strategies.

### Partitioning
The table is partitioned based on the `date_ist` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **facility_id**
  - Data type: varchar
  - Description: Unique identifier for each facility where inventory is stored.

- **product_number**
  - Data type: integer
  - Description: Unique number identifying a specific product in inventory.

- **warehouse_code**
  - Data type: varchar
  - Description: Code identifying a specific warehouse within a facility.

- **physical_warehouse_inventory**
  - Data type: integer
  - Description: The amount of inventory physically present in the warehouse.

- **blocked_inventory**
  - Data type: integer
  - Description: The portion of inventory that is not available for sale, possibly due to damage or reservation.

- **total_sellable_inventory**
  - Data type: integer
  - Description: Total inventory that is available for sale after accounting for blocked inventory.

- **updated_at_ist**
  - Data type: timestamp(6)
  - Description: The timestamp indicating when the inventory record was last updated, in Indian Standard Time.

- **date_ist**
  - Data type: date
  - Description: The date in Indian Standard Time on which the inventory snapshot was taken.

### Key Relationships and Joins
- The `facility_id` and `warehouse_code` columns can potentially be used to join with other facility or warehouse-related tables to gather more details about the locations.
- The `product_number` might be used to join with product tables to retrieve detailed product information.
- The `date_ist` partition key can be used to join with other date-partitioned tables for time-series analysis or reporting.

### Business-Critical Columns
- **total_sellable_inventory**: Critical for understanding the available stock for sales.
- **updated_at_ist**: Important for tracking the latest inventory updates and ensuring data timeliness.

This documentation provides a comprehensive overview of the `blinkit.bistro_etls.bistro_hourly_inventory_snapshots_be` table, ensuring efficient usage and integration into broader data systems.