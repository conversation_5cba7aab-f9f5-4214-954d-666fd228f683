# Table: blinkit.bistro_etls.search_meta_entity_product_relationship

### Description
The `blinkit.bistro_etls.search_meta_entity_product_relationship` table stores metadata and relationship information between various metadata entities and products. This table is likely used to manage and query metadata associations to enhance search functionalities within a product catalog system.

### Partitioning
The table is partitioned on the column `snapshot_ist`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS under any condition to ensure efficient data retrieval.

### Columns
- **metadata_id**
    - Data type: bigint
    - Description: A unique identifier for the metadata entity.

- **metadata_name**
    - Data type: varchar
    - Description: The name of the metadata entity.

- **relationship_id**
    - Data type: varchar
    - Description: A unique identifier for the relationship between the metadata entity and a product.

- **metadata_type**
    - Data type: varchar
    - Description: The type of metadata entity (e.g., category, tag).

- **tagged_product_id**
    - Data type: bigint
    - Description: The identifier of the product associated with the metadata.

- **is_pid_active**
    - Data type: bigint
    - Description: Indicates whether the product ID is active (1) or inactive (0).

- **snapshot_ist**
    - Data type: date
    - Description: The date of the data snapshot, used for partitioning the table.

### Key Relationships and JOINs
- The `tagged_product_id` can be used to join this table with other product-related tables, such as a product catalog or inventory tables, to fetch detailed product information.
- The `metadata_id` might be used to join with other metadata tables to fetch additional metadata details.

### Business-Critical Columns
- **metadata_id**: Essential for identifying unique metadata entities.
- **tagged_product_id**: Crucial for linking metadata to specific products in the catalog.
- **is_pid_active**: Important for determining the active status of products in relation to the metadata.

This documentation provides a foundational understanding of the `blinkit.bistro_etls.search_meta_entity_product_relationship` table, facilitating efficient data management and querying within the system.