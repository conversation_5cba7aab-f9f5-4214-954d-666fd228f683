# Table: blinkit.bistro_etls.mau_merchant_data_bistro

### Description
The `blinkit.bistro_etls.mau_merchant_data_bistro` table is designed to store daily metrics related to merchant activities on a platform. It likely captures user engagement and conversion metrics by merchant, which can be used for analyzing merchant performance, user behavior, and the effectiveness of different platforms.

### Partitioning
The table is partitioned on the `at_date_ist` column. This column is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **at_date_ist**
  - Data type: date
  - Description: The date of the data record, indicating when the metrics were recorded.

- **platform**
  - Data type: varchar
  - Description: The platform from which the data was recorded, e.g., mobile or desktop.

- **device_uuid**
  - Data type: varchar
  - Description: The unique identifier for the device used, helping in tracking unique engagements.

- **merchant_id**
  - Data type: integer
  - Description: The identifier for the merchant whose data is being recorded.

- **dau_flag**
  - Data type: integer
  - Description: A flag indicating whether the merchant had any daily active users.

- **atc_flag**
  - Data type: integer
  - Description: A flag indicating whether there was an add-to-cart action for the merchant.

- **cv_flag**
  - Data type: integer
  - Description: A flag indicating whether a conversion (e.g., purchase) occurred.

- **conv**
  - Data type: integer
  - Description: The number of conversions or transactions completed.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id**: This column is a potential JOIN key for linking with other merchant-related tables, such as merchant profiles or transaction details.
- **at_date_ist**: As a partition key, this is also critical for temporal analysis and can be used to join with other date-partitioned tables.

### Potential Relationships
- The `merchant_id` column suggests a relationship to other tables that contain detailed merchant information or transaction data.
- The `device_uuid` can be related to device or user session tables for a more detailed analysis of user engagement across different devices.

This documentation provides a structured overview of the `blinkit.bistro_etls.mau_merchant_data_bistro` table, essential for efficient querying and data analysis within the bedrock knowledge base.