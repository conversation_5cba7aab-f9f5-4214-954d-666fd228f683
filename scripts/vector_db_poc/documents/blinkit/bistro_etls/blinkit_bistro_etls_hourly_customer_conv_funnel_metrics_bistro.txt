# Table: blinkit.bistro_etls.hourly_customer_conv_funnel_metrics_bistro

### Description
The table `blinkit.bistro_etls.hourly_customer_conv_funnel_metrics_bistro` is designed to store hourly metrics related to customer conversion funnels on a bistro platform. It tracks user interactions from app launch to final checkout, segmented by various dimensions such as channel, platform, city, and merchant. This data is crucial for analyzing user behavior, optimizing user experience, and improving conversion rates across different segments.

### Partitioning
This table is not partitioned. Queries on this table should be optimized based on the indexed columns if available.

### Columns
- **snapshot_date_ist**
  - Data type: date
  - Description: The date of the data snapshot in IST timezone.

- **snapshot_hour_ist**
  - Data type: bigint
  - Description: The hour of the day for the data snapshot in IST timezone.

- **channel**
  - Data type: varchar
  - Description: The channel through which the user accessed the service (e.g., web, mobile).

- **platform**
  - Data type: varchar
  - Description: The platform (e.g., iOS, Android) used by the user.

- **city**
  - Data type: varchar
  - Description: The city from which the user accessed the service.

- **merchant_id**
  - Data type: varchar
  - Description: The identifier for the merchant involved in the transaction.

- **user_bucket**
  - Data type: varchar
  - Description: Categorization of users into different buckets based on predefined criteria.

- **user_type**
  - Data type: varchar
  - Description: Type of user (e.g., new, returning).

- **daily_active_users**
  - Data type: bigint
  - Description: Count of daily active users.

- **app_launch_to_home_page_visit**
  - Data type: bigint
  - Description: Number of users who launched the app and visited the home page.

- **dau_to_home_page_visit**
  - Data type: bigint
  - Description: Ratio of daily active users who visited the home page.

- **app_launch_to_home_page_visit_nsa**
  - Data type: bigint
  - Description: Number of users who launched the app and visited the home page, excluding specific activities.

- **dau_to_atc**
  - Data type: bigint
  - Description: Ratio of daily active users who added items to the cart.

- **atc_to_cv**
  - Data type: bigint
  - Description: Conversion rate from adding to cart to creating a checkout.

- **cv_to_user_registered**
  - Data type: bigint
  - Description: Number of checkouts leading to user registration.

- **cv_to_address_added**
  - Data type: bigint
  - Description: Number of checkouts where an address was added.

- **cv_to_payment**
  - Data type: bigint
  - Description: Number of checkouts that proceeded to payment.

- **payment_to_checkout**
  - Data type: bigint
  - Description: Number of payments that led to a final checkout.

- **cv_to_checkout**
  - Data type: bigint
  - Description: Number of conversions from checkout initiation to completion.

- **overall_conversion**
  - Data type: bigint
  - Description: Overall conversion rate across the platform.

- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of when the ETL process captured this data snapshot in IST timezone.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id**: Could be used to join with a merchant table to get additional merchant details.
- **city**: Could be used to join with geographic or demographic data tables for enriched analysis.
- Business-critical columns include metrics related to user conversion such as `dau_to_atc`, `atc_to_cv`, `cv_to_payment`, and `overall_conversion`, which are essential for performance tracking and strategic decision-making.

### Relationships to Other Tables
- The `merchant_id` suggests a relationship to a merchants table, which would contain details about the merchants.
- The `user_type` could relate to a user demographics table that categorizes user types in more detail.

This documentation provides a comprehensive overview of the `hourly_customer_conv_funnel_metrics_bistro` table, essential for effective querying and data analysis.