# Table: blinkit.bistro_etls.bistro_agg_sales_excluding_dotd

### Description
The table `blinkit.bistro_etls.bistro_agg_sales_excluding_dotd` is designed to store aggregated sales data for a bistro, excluding any deals of the day (DOTD). This data includes details about products sold, quantities, sales figures, and the specific outlets where sales occurred. The table likely supports business analytics and reporting by providing insights into sales performance across different parameters such as time, product, and location.

### Partitioning
The table is not partitioned. This means that queries might be slower on large datasets as they cannot take advantage of partition pruning.

### Columns
- **dt_**
  - Data type: date
  - Description: The date on which the sales data was recorded.

- **frontend_merchant_id**
  - Data type: integer
  - Description: Identifier for the merchant on the frontend system.

- **product_id**
  - Data type: integer
  - Description: Unique identifier for the product sold.

- **hr**
  - Data type: integer
  - Description: The hour of the day when the sales record was logged.

- **rnk**
  - Data type: integer
  - Description: A ranking or sequence number possibly used for ordering records or versioning.

- **procured_quantity**
  - Data type: integer
  - Description: The quantity of the product procured for sale.

- **total_selling_price**
  - Data type: integer
  - Description: The total price at which the product was sold.

- **quantity**
  - Data type: integer
  - Description: The quantity of the product that was actually sold.

- **outlet_id**
  - Data type: integer
  - Description: Identifier for the outlet where the product was sold.

- **outlet_name**
  - Data type: varchar
  - Description: The name of the outlet where the product was sold.

### Potential JOIN Keys and Business-Critical Columns
- **product_id**: Can be used to join with product tables to get more details about the products.
- **outlet_id**: Can be used to join with outlet tables to fetch additional outlet information.
- **frontend_merchant_id**: May be used to join with merchant tables for details on the merchants.

### Relationships to Other Tables
- The `product_id` suggests a relationship to a products table, which would contain details like product name, category, etc.
- The `outlet_id` indicates a possible link to an outlet table, detailing location, size, and other outlet-specific information.
- The `frontend_merchant_id` could link to a merchant information table that includes details about the merchants selling through the frontend.

This documentation provides a clear overview of the table structure, its contents, and its potential uses within the database environment.