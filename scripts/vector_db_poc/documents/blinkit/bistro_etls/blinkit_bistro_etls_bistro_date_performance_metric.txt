# Table: blinkit.bistro_etls.bistro_date_performance_metric

### Description
The `blinkit.bistro_etls.bistro_date_performance_metric` table stores daily performance metrics for merchants, products, and sales activities. It includes detailed information about orders, sales, product availability, and customer interactions on a specific date. This table is essential for analyzing daily business operations and performance trends over time.

### Partitioning
The table is partitioned on the `date_` column. This is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **date_**
  - Data type: date
  - Description: The specific date for which the performance metrics are recorded.
  
- **week**
  - Data type: date
  - Description: The week associated with the `date_`.

- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for the merchant.

- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant.

- **city_name**
  - Data type: varchar
  - Description: City where the merchant is located.

- **product_name**
  - Data type: varchar
  - Description: Name of the product.

- **product_type**
  - Data type: varchar
  - Description: Type or category of the product.

- **product_id**
  - Data type: bigint
  - Description: Unique identifier for the product.

- **l1_category**
  - Data type: varchar
  - Description: Top-level category of the product.

- **l2_category**
  - Data type: varchar
  - Description: Sub-category of the product under the top-level category.

- **orders**
  - Data type: bigint
  - Description: Total number of orders placed for the merchant on the given date.

- **items_sold**
  - Data type: bigint
  - Description: Total number of items sold on the given date.

- **ratings**
  - Data type: bigint
  - Description: Total number of ratings given by customers.

- **rated_products**
  - Data type: bigint
  - Description: Number of products that received ratings.

- **wait_time**
  - Data type: decimal(38,2)
  - Description: Average waiting time for orders in minutes.

- **prep_time**
  - Data type: decimal(38,2)
  - Description: Average preparation time for orders in minutes.

- **items_made**
  - Data type: bigint
  - Description: Total number of items prepared or made on the given date.

- **total_selling_price**
  - Data type: real
  - Description: Total revenue generated from sales on the given date.

- **oos_impressions**
  - Data type: bigint
  - Description: Number of times out-of-stock products were viewed.

- **impressions**
  - Data type: bigint
  - Description: Total number of product views on the given date.

- **added**
  - Data type: bigint
  - Description: Number of times products were added to the cart.

- **store_att_orders**
  - Data type: bigint
  - Description: Number of store-attended orders on the given date.

- **cart_pen**
  - Data type: decimal(24,2)
  - Description: Cart penetration rate, indicating the percentage of visits that resulted in an added cart item.

- **orders_at_merchant**
  - Data type: bigint
  - Description: Number of orders placed specifically at the merchant's location.

### Potential JOIN Keys and Relationships
- **merchant_id**: Can be used to join with other merchant-related tables.
- **product_id**: Can be used to join with product inventory or product details tables.
- **city_name**: Might be used to join with city demographics or regional sales tables.

### Business-Critical Columns
- **orders**
- **total_selling_price**
- **items_sold**
These columns are crucial for analyzing the sales performance and operational efficiency of the merchants.