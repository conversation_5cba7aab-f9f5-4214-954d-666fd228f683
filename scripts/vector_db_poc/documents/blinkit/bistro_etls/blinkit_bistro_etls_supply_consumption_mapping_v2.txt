# Table: blinkit.bistro_etls.supply_consumption_mapping_v2

### Description
The `blinkit.bistro_etls.supply_consumption_mapping_v2` table is designed to track the consumption of supplies at various outlets over specific periods. It records details about the quantity of supplies consumed, the duration supplies lasted in stock, and the timestamps marking the start and end of consumption. This table is essential for inventory management and operational analysis in a retail or restaurant chain environment.

### Partitioning
The table is partitioned by the `outlet_id`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval and management.

### Columns
- **outlet_id**
  - Data type: integer
  - Description: The unique identifier for an outlet. This is a critical column for partitioning and filtering data.

- **converted_id**
  - Data type: integer
  - Description: A secondary identifier for an outlet, possibly used for internal tracking or referencing in other systems.

- **supply_id**
  - Data type: integer
  - Description: The unique identifier for a type of supply. This could be used to join with a supplies catalog table.

- **consumption_start_dt**
  - Data type: timestamp(6)
  - Description: The timestamp marking the beginning of the consumption period for a supply.

- **consumption_end_dt**
  - Data type: timestamp(6)
  - Description: The timestamp marking the end of the consumption period for a supply.

- **quantity_consumed**
  - Data type: real
  - Description: The amount of supply that was consumed during the specified period.

- **days_in_stock**
  - Data type: integer
  - Description: The number of days the supply was in stock at the outlet before being fully consumed.

- **insert_dt_ist**
  - Data type: timestamp(6)
  - Description: The timestamp when the record was inserted into the database, in Indian Standard Time (IST).

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `supply_id` could potentially be used to join with a supplies catalog table to retrieve more detailed information about the supplies.
- **Business-Critical Columns**: `outlet_id`, `supply_id`, `quantity_consumed`, and the date range columns (`consumption_start_dt`, `consumption_end_dt`) are critical for analyzing supply consumption patterns and inventory management.

### Potential Relationships
- The `supply_id` column suggests a relationship to a supplies catalog table where each `supply_id` maps to a specific type of supply, including details like name, type, supplier, and cost.
- The `outlet_id` and `converted_id` may relate to an outlet information table that includes details about each outlet's location, size, and type.