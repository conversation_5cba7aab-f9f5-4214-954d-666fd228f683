# Table: blinkit.bistro_etls.bistr_conv_supply_map

### Description
The `blinkit.bistro_etls.bistr_conv_supply_map` table is designed to map ingredients to their final supply forms and quantities. It likely serves as a reference for converting raw ingredient data into a form that is usable in inventory management, purchasing, or recipe formulation.

### Partitioning
The table is not partitioned. This may impact performance on large datasets, and special attention should be given to optimizing queries for efficiency.

### Columns
- **ing_pid**
  - Data type: integer
  - Description: The unique identifier for the ingredient.

- **ing_name**
  - Data type: varchar
  - Description: The name of the ingredient.

- **final_supply_id**
  - Data type: integer
  - Description: The unique identifier for the final supply form of the ingredient.

- **final_supply_name**
  - Data type: varchar
  - Description: The name of the final supply form of the ingredient.

- **final_conversion_factor**
  - Data type: double
  - Description: The factor used to convert the ingredient to its final supply form.

- **number_of_supply_mapped**
  - Data type: integer
  - Description: The number of supply forms that the ingredient is mapped to.

- **updated_at_ist**
  - Data type: timestamp(6)
  - Description: The timestamp of the last update to the record, in Indian Standard Time.

### Potential JOIN Keys and Business-Critical Columns
- **ing_pid** and **final_supply_id** are potential JOIN keys. They can be used to link this table with other tables that contain detailed information about ingredients or supply items.
- **final_conversion_factor** is a business-critical column as it directly affects inventory calculations and recipe formulations.

### Potential Relationships
- The `ing_pid` could be used to JOIN with an ingredients table that contains detailed descriptions, nutritional information, or sourcing details.
- The `final_supply_id` could be used to JOIN with a supply or inventory table to get additional details like supplier, cost, and stock levels.

This documentation provides a clear and concise overview of the `blinkit.bistro_etls.bistr_conv_supply_map` table, optimized for quick searches and integration into a broader knowledge base.