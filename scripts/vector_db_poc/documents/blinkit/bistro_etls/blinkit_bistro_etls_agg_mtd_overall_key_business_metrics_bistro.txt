# Table: blinkit.bistro_etls.agg_mtd_overall_key_business_metrics_bistro

### Description
The table `blinkit.bistro_etls.agg_mtd_overall_key_business_metrics_bistro` is designed to store aggregated key business metrics on a monthly basis for a bistro. It captures various performance indicators such as transaction details, sales volumes, and customer engagement metrics across different cities. This data is crucial for analyzing business trends, customer behavior, and operational efficiency.

### Partitioning
This table is not partitioned. Queries on this table should be optimized based on the available indexes and careful selection of WHERE clause conditions.

### Columns
- **snapshot_date**
  - Data type: date
  - Description: The date when the data snapshot was taken, typically representing the end of a reporting period.

- **city**
  - Data type: varchar
  - Description: The city where the bistro's metrics are recorded, useful for geographic segmentation.

- **transacting_users**
  - Data type: bigint
  - Description: The number of users who completed transactions during the reporting period.

- **new_transacting_users**
  - Data type: bigint
  - Description: The count of new users who completed their first transaction in the reporting period.

- **gmv**
  - Data type: real
  - Description: Gross Merchandise Value, representing the total sales volume processed through the bistro.

- **aov**
  - Data type: real
  - Description: Average Order Value, calculated as the total GMV divided by the number of transactions.

- **checkout_carts**
  - Data type: bigint
  - Description: The number of shopping carts that reached the checkout phase.

- **delivered_carts**
  - Data type: bigint
  - Description: The number of shopping carts that were successfully delivered.

- **sku_count**
  - Data type: bigint
  - Description: The total number of unique stock keeping units (SKUs) available during the period.

- **items_per_order**
  - Data type: decimal(21,1)
  - Description: The average number of items included in each order.

- **total_items_sold**
  - Data type: bigint
  - Description: The total number of items sold during the reporting period.

- **unique_sku_per_order**
  - Data type: decimal(21,1)
  - Description: The average number of unique SKUs per order.

- **mau**
  - Data type: bigint
  - Description: Monthly Active Users, indicating the total number of unique users interacting with the bistro in a month.

- **conversion**
  - Data type: decimal(21,1)
  - Description: The conversion rate, calculated as the percentage of users who made a purchase out of the total visitors.

- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: The timestamp indicating when the ETL process for this snapshot was completed, in Indian Standard Time.

### Potential JOIN Keys and Business-Critical Columns
- **city** could be used to join with other geographic or demographic tables.
- **snapshot_date** and **etl_snapshot_ts_ist** are critical for time-series analysis and could be used to join with other time-specific datasets.
- Business-critical columns include **transacting_users**, **gmv**, **mau**, and **conversion**, which directly relate to the performance and health of the business.

### Infer Potential Relationships
- There might be relationships with tables that contain detailed transaction logs, user demographic information, or inventory details, especially linked through columns like **city** or metrics columns.