# Table: blinkit.bistro_etls.supply_consumption_mapping_v3

### Description
The `blinkit.bistro_etls.supply_consumption_mapping_v3` table is designed to track and manage the consumption of supplies at various outlets over specific periods. It records the quantity of supplies consumed, the duration of consumption, and the stock availability, facilitating efficient inventory management and operational planning.

### Partitioning
The table is partitioned on the `outlet_id` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **outlet_id**
  - Data type: integer
  - Description: The unique identifier for an outlet. This is a critical column for partitioning and filtering data.

- **converted_id**
  - Data type: integer
  - Description: A secondary identifier that may be used for referencing outlets in other systems or tables.

- **supply_id**
  - Data type: integer
  - Description: The unique identifier for a supply item. This can be used to join with other tables that contain detailed supply information.

- **consumption_start_dt**
  - Data type: timestamp(6)
  - Description: The start date and time of the consumption period for a supply item.

- **consumption_end_dt**
  - Data type: timestamp(6)
  - Description: The end date and time of the consumption period for a supply item.

- **quantity_consumed**
  - Data type: real
  - Description: The total quantity of the supply item consumed during the specified period.

- **days_in_stock**
  - Data type: integer
  - Description: The number of days the supply item was in stock during the consumption period.

- **insert_dt_ist**
  - Data type: timestamp(6)
  - Description: The timestamp when the consumption data was recorded in the database, in Indian Standard Time.

### Potential Relationships and JOIN Keys
- The `supply_id` column can potentially be used to join with other inventory or supply-related tables to fetch detailed information about the supplies.
- The `outlet_id` serves as both a partition key and a potential join key for other tables that contain outlet-specific data.

### Business-Critical Columns
- **outlet_id**: Essential for identifying the outlet and necessary for partitioning.
- **supply_id**: Important for linking consumption data with specific supplies.
- **quantity_consumed**: Critical for inventory and supply chain analysis.
- **consumption_start_dt** and **consumption_end_dt**: Key for determining the consumption window and planning.

This structured documentation ensures that the table's schema is clearly understood, supporting effective database management and query optimization.