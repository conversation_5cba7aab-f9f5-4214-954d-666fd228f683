# Table: blinkit.bistro_etls.precook_daily_projection_log_new

### Description
The `blinkit.bistro_etls.precook_daily_projection_log_new` table is designed to store daily projections for product sales, including historical sales data, growth rates, and projections based on different conditions. This table likely aids in inventory and supply chain management by predicting future product demands at various merchant outlets.

### Partitioning
This table is partitioned on the following keys, which are CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition:
- `projection_date` (date): The date for which the sales projections are calculated.
- `slot` (bigint): A time slot identifier which may relate to specific times of day for sales projections.

### Columns
- **order_date**
  - Data type: date
  - Description: The date on which the order was placed.
- **slot**
  - Data type: bigint
  - Description: Identifier for a specific time slot during which orders are placed.
- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for a merchant.
- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant.
- **product_id**
  - Data type: bigint
  - Description: Unique identifier for a product.
- **product_name**
  - Data type: varchar
  - Description: Name of the product.
- **outlet_id**
  - Data type: bigint
  - Description: Unique identifier for an outlet where the product is sold.
- **dow**
  - Data type: bigint
  - Description: Day of the week represented as an integer.
- **gmv**
  - Data type: real
  - Description: Gross Merchandise Value associated with the product for a specific slot.
- **quantity**
  - Data type: bigint
  - Description: Quantity of the product ordered.
- **last_dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold of the product on the last same day of the week.
- **last_dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the product was part of a deal of the day last week.
- **last_2dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold of the product two weeks ago on the same day of the week.
- **last_2dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the product was part of a deal of the day two weeks ago on the same day.
- **last_3dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold of the product three weeks ago on the same day of the week.
- **last_3dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the product was part of a deal of the day three weeks ago on the same day.
- **last_4dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold of the product four weeks ago on the same day of the week.
- **last_4dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the product was part of a deal of the day four weeks ago on the same day.
- **last_5dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold of the product five weeks ago on the same day of the week.
- **last_5dow_dotd_flag**
  - Data type: bigint
  - Description: Flag indicating if the product was part of a deal of the day five weeks ago on the same day.
- **store_age_in_days**
  - Data type: varchar
  - Description: Age of the store in days.
- **opd**
  - Data type: bigint
  - Description: Orders per day, a metric indicating the average number of orders per day.
- **projection_date**
  - Data type: date
  - Description: The date for which sales projections are being made.
- **dow_proj_date**
  - Data type: bigint
  - Description: Day of the week for the projection date.
- **preparation_type**
  - Data type: varchar
  - Description: Type of preparation required for the product.
- **sub_product_id**
  - Data type: bigint
  - Description: Identifier for a sub-category of the product.
- **sub_pid_name**
  - Data type: varchar
  - Description: Name of the sub-product.
- **pack_size**
  - Data type: varchar
  - Description: Packaging size of the product.
- **deliver_qty**
  - Data type: bigint
  - Description: Quantity of the product to be delivered.
- **last_4_non_dotd_similar_day_type_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold on the last four non-deal days similar to the current day type.
- **last_non_dotd_dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold on the last non-deal day of the week.
- **last_non_dotd_2dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold two weeks ago on the last non-deal day of the week.
- **last_non_dotd_3dow_sold_quantity**
  - Data type: bigint
  - Description: Quantity sold three weeks ago on the last non-deal day of the week.
- **last_2_dow_growth_rate**
  - Data type: real
  - Description: Growth rate in sales from two days of the week ago.
- **last_dow_growth_rate**
  - Data type: real
  - Description: Growth rate in sales from the last day of the week.
- **row_median_growth**
  - Data type: real
  - Description: Median growth rate across rows.
- **last_2_dow_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold two days of the week ago.
- **last_3_dow_median_sold_quantity**
  - Data type: real
  - Description: Median quantity sold three days of the week ago.
- **last_3_dow_stdev_sold_quantity**
  - Data type: real
  - Description: Standard deviation of quantity sold three days of the week ago.
- **is_strictly_increasing**
  - Data type: boolean
  - Description: Boolean flag indicating if the sales trend is strictly increasing.
- **is_strictly_decreasing**
  - Data type: boolean
  - Description: Boolean flag indicating if the sales trend is strictly decreasing.
- **projected_increasing**
  - Data type: real
  - Description: Projected increase in sales.
- **increasing_cap1**
  - Data type: real
  - Description: First cap value for increasing sales projections.
- **increasing_cap2**
  - Data type: real
  - Description: Second cap value for increasing sales projections.
- **increasing_cap3**
  - Data type: real
  - Description: Third cap value for increasing sales projections.
- **projected_increasing_updated**
  - Data type: real
  - Description: Updated projection for increasing sales.
- **projected_decreasing**
  - Data type: real
  - Description: Projected decrease in sales.
- **projected_default**
  - Data type: real
  - Description: Default projected value for sales.
- **final_projected**
  - Data type: real
  - Description: Final projected value for sales.
- **final_projected_updated**
  - Data type: real
  - Description: Updated final projected value for sales.
- **updated_at_ts**
  - Data type: timestamp(6)
  - Description: Timestamp when the record was last updated.
- **cap_projected_strictly_increasing**
  - Data type: real
  - Description: Capped projection for strictly increasing sales.
- **new_final_projected_updated**
  - Data type: real
  - Description: Newly updated final projected value for sales.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `merchant_id`, `product_id`, `outlet_id` can be used to join with other tables containing merchant, product, or outlet details.
- **Business-Critical Columns**: `projection_date`, `product_id`, `merchant_id`, `gmv`, `quantity`, `final_projected`.

### Potential Relationships
- The `product_id` could be used to join with a `products` table to get more detailed product information.
- The `merchant_id` might link to a `merchants` table for merchant-specific data.
- The `outlet_id` could relate to an `outlets` table for location-specific details.