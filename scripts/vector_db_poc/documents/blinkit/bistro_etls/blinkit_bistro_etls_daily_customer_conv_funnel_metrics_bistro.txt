# Table: blinkit.bistro_etls.daily_customer_conv_funnel_metrics_bistro

### Description
The `blinkit.bistro_etls.daily_customer_conv_funnel_metrics_bistro` table stores daily metrics related to customer conversion funnels for a bistro business. It captures data at various stages of the user journey from app launch to checkout, segmented by different dimensions such as channel, platform, and city. This table is crucial for analyzing user engagement and effectiveness of the conversion process.

### Partitioning
This table is not partitioned. Queries on this table should be optimized based on the available indexes and should consider filtering on frequently queried columns to enhance performance.

### Columns
- **snapshot_date_ist**
    - Data type: date
    - Description: The date of the data snapshot in IST timezone.

- **snapshot_hour_ist**
    - Data type: integer
    - Description: The hour of the day for the data snapshot in IST timezone.

- **channel**
    - Data type: varchar
    - Description: The channel through which the user accessed the service (e.g., web, mobile).

- **platform**
    - Data type: varchar
    - Description: The platform on which the service was accessed (e.g., Android, iOS).

- **city**
    - Data type: varchar
    - Description: The city from which the user accessed the service.

- **merchant_id**
    - Data type: varchar
    - Description: The identifier for the merchant.

- **user_bucket**
    - Data type: varchar
    - Description: Categorization of users into different buckets based on defined criteria.

- **user_type**
    - Data type: varchar
    - Description: Type of user (e.g., new, returning).

- **daily_active_users**
    - Data type: bigint
    - Description: Count of unique users active on the platform on a given day.

- **app_launch_to_home_page_visit**
    - Data type: bigint
    - Description: Number of users who launched the app and visited the home page.

- **dau_to_home_page_visit**
    - Data type: bigint
    - Description: Daily active users who visited the home page.

- **app_launch_to_home_page_visit_nsa**
    - Data type: bigint
    - Description: Number of users who launched the app and visited the home page, not subject to any specific adjustments.

- **dau_to_atc**
    - Data type: bigint
    - Description: Daily active users who added items to the cart.

- **atc_to_cv**
    - Data type: bigint
    - Description: Users who added to cart and then completed a visit.

- **cv_to_user_registered**
    - Data type: bigint
    - Description: Users who completed a visit and then registered.

- **cv_to_address_added**
    - Data type: bigint
    - Description: Users who completed a visit and added an address.

- **cv_to_payment**
    - Data type: bigint
    - Description: Users who completed a visit and made a payment.

- **payment_to_checkout**
    - Data type: bigint
    - Description: Users who made a payment and proceeded to checkout.

- **cv_to_checkout**
    - Data type: bigint
    - Description: Users who completed a visit and proceeded to checkout.

- **overall_conversion**
    - Data type: bigint
    - Description: Overall conversion rate calculated from various stages of the user journey.

- **etl_snapshot_ts_ist**
    - Data type: timestamp(6)
    - Description: Timestamp of the ETL process snapshot in IST timezone.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id**: Can be used to join with merchant tables.
- **city**: May be used to join with geographic or demographic data tables.
- **platform**, **channel**: Useful for segmenting data and could be joined with marketing or platform-specific tables.

### Relationships to Other Tables
- Columns like `merchant_id` suggest potential relationships with a merchants table.
- The `city` column could relate to a table containing city-specific data or demographics.
- User-related metrics (`daily_active_users`, `user_type`) might correlate with user profile tables.

This documentation provides a structured overview of the `blinkit.bistro_etls.daily_customer_conv_funnel_metrics_bistro` table, facilitating efficient data retrieval and analysis for business insights.