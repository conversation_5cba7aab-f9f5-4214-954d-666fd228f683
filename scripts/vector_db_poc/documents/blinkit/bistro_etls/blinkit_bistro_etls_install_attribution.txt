# Table: blinkit.bistro_etls.install_attribution

### Description
The `blinkit.bistro_etls.install_attribution` table is designed to store data related to the attribution of app installations. This includes identifying the source of the installation, the specific campaign associated with it, and other relevant details that help in analyzing the effectiveness of different marketing strategies.

### Partitioning
The table is partitioned on the column `dt`. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **dt**
    - Data type: date
    - Description: The date of the event or installation.

- **appsflyer_id**
    - Data type: varchar
    - Description: A unique identifier for the user as assigned by <PERSON><PERSON><PERSON><PERSON>er, a mobile marketing analytics and attribution platform.

- **event_name**
    - Data type: varchar
    - Description: The name of the event associated with the app installation.

- **media_source**
    - Data type: varchar
    - Description: The source of the media from which the app was installed, such as an advertisement platform.

- **af_prt**
    - Data type: varchar
    - Description: The partner associated with <PERSON><PERSON><PERSON><PERSON><PERSON>, involved in the marketing or advertisement.

- **campaign**
    - Data type: varchar
    - Description: The specific marketing campaign name that led to the app installation.

- **ts_ist**
    - Data type: timestamp(6) with time zone
    - Description: The exact timestamp when the installation took place, including the time zone.

- **device_uuid**
    - Data type: varchar
    - Description: A unique identifier for the device on which the app was installed.

### Potential JOIN Keys and Business-Critical Columns
- **appsflyer_id** and **device_uuid** could be potential JOIN keys if there are other tables in the database that track user or device-specific activities or attributes.
- Business-critical columns likely include **appsflyer_id**, **media_source**, **campaign**, and **ts_ist** as they directly relate to tracking the effectiveness of marketing efforts and user engagement.

### Potential Relationships
- The **appsflyer_id** might be used to join with other user activity or demographic tables that also use AppsFlyer IDs.
- The **campaign** could be linked to other campaign management or budgeting tables to analyze cost-effectiveness and ROI.

This documentation provides a structured and detailed overview of the `blinkit.bistro_etls.install_attribution` table, optimized for AI search capabilities and efficient data management practices.