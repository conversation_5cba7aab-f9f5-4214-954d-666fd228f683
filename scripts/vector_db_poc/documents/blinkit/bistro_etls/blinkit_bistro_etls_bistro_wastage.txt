# Table: blinkit.bistro_etls.bistro_wastage

### Description
The `blinkit.bistro_etls.bistro_wastage` table is designed to store detailed records of wastage at various outlets up to 6 AM each day. It includes data about both negative and positive wastage quantities and values, as well as quantities and values associated with CEC (Customer Experience Center) testing. This table is crucial for analyzing and managing inventory wastage and efficiency in outlet operations.

### Partitioning
This table is not partitioned.

### Columns
- **date_till_6am**
  - Data type: date
  - Description: The date up to which wastage data is recorded, inclusive of activity until 6 AM.
  
- **outlet_name**
  - Data type: varchar
  - Description: The name of the outlet where the wastage is recorded.
  
- **frontend_merchant_id**
  - Data type: bigint
  - Description: A unique identifier for the merchant at the frontend system.
  
- **merchant_name**
  - Data type: varchar
  - Description: The name of the merchant associated with the wastage data.
  
- **negative_wastage_quantity**
  - Data type: bigint
  - Description: The quantity of items counted as wastage that reduced inventory.
  
- **positive_wastage_quantity**
  - Data type: bigint
  - Description: The quantity of items counted as wastage that increased inventory.
  
- **cec_testing_quantity**
  - Data type: bigint
  - Description: The quantity of items used for CEC testing purposes.
  
- **negative_wastage_value**
  - Data type: double
  - Description: The monetary value of negative wastage.
  
- **positive_wastage_value**
  - Data type: double
  - Description: The monetary value of positive wastage.
  
- **cec_testing_value**
  - Data type: double
  - Description: The monetary value of items used for CEC testing.

### Key Relationships and Joins
- The `frontend_merchant_id` can potentially be used as a JOIN key with other tables that contain merchant-related data, such as sales or inventory tables, to provide a comprehensive view of merchant activities and performance.
- Columns like `negative_wastage_quantity`, `positive_wastage_quantity`, `negative_wastage_value`, and `positive_wastage_value` are business-critical as they directly impact financial and inventory metrics.

### Business-Critical Columns
- **negative_wastage_quantity**
- **positive_wastage_quantity**
- **negative_wastage_value**
- **positive_wastage_value**

This documentation provides a structured overview of the `blinkit.bistro_etls.bistro_wastage` table, ensuring efficient data retrieval and analysis for business intelligence purposes.