# Table: blinkit.bistro_etls.bistr_dish_supply_map

### Description
The `blinkit.bistro_etls.bistr_dish_supply_map` table maps the supply details to dish details within a bistro environment. It tracks the relationship between supplies and dishes, including the quantities and conversion factors used, along with merchant and outlet identifiers. This table is essential for managing inventory and understanding the resource allocation for dishes in various outlets.

### Partitioning
The table is not partitioned.

### Columns
- **supply_pid**
  - Data type: integer
  - Description: The unique identifier for a supply item.

- **supply_item_id**
  - Data type: integer
  - Description: A specific identifier for each supply item, potentially used for tracking and inventory purposes.

- **supply_name**
  - Data type: varchar
  - Description: The name of the supply item.

- **conversion_factor**
  - Data type: integer
  - Description: The factor used to convert supply units to units suitable for dish preparation.

- **ps_quantity**
  - Data type: integer
  - Description: The quantity of the supply item allocated per specific dish.

- **dish_pid**
  - Data type: integer
  - Description: The unique identifier for a dish.

- **dish_item_id**
  - Data type: integer
  - Description: A specific identifier for each dish, potentially used for tracking and menu listing.

- **dish_name**
  - Data type: varchar
  - Description: The name of the dish.

- **merchant_id**
  - Data type: integer
  - Description: Identifier for the merchant under which the outlet operates.

- **outlet_id**
  - Data type: integer
  - Description: Identifier for the specific outlet where the dish is served.

- **updated_at_ist**
  - Data type: timestamp(6)
  - Description: The timestamp indicating the last update time of the record in Indian Standard Time.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `supply_pid`, `dish_pid`, `merchant_id`, `outlet_id` could be used to join with other tables that contain detailed information about supplies, dishes, merchants, or outlets.
- **Business-Critical Columns**: `supply_item_id`, `dish_item_id`, `ps_quantity`, and `conversion_factor` are critical for inventory and resource management.

### Potential Relationships
- The `supply_item_id` and `dish_item_id` suggest potential relationships with other inventory or menu tables where these items are detailed further.
- The `merchant_id` and `outlet_id` columns suggest a relationship to other tables that contain information about business entities and their physical locations.