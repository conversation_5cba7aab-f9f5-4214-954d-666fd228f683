# Table: blinkit.bistro_etls.supply_inwards_consumption_mapping

### Description
The `blinkit.bistro_etls.supply_inwards_consumption_mapping` table is designed to track the consumption of supplies at various outlets over specific periods. It records the quantities of supplies consumed, the duration supplies were in stock, and the timestamps related to the consumption periods. This table is essential for managing inventory, analyzing supply usage, and planning future supply needs.

### Partitioning
This table is not partitioned. It is important to consider performance implications when querying large datasets from this table.

### Columns
- **outlet_id**
  - Data type: integer
  - Description: Identifies the outlet where the supplies are consumed.

- **converted_id**
  - Data type: integer
  - Description: Likely a unique identifier for a transaction or record conversion process.

- **supply_id**
  - Data type: integer
  - Description: Identifies the specific supply item being consumed.

- **consumption_start_dt**
  - Data type: timestamp(6)
  - Description: The start date and time of the consumption period for the supply.

- **consumption_end_dt**
  - Data type: timestamp(6)
  - Description: The end date and time of the consumption period for the supply.

- **quantity_consumed**
  - Data type: real
  - Description: The amount of the supply that was consumed during the specified period.

- **days_in_stock**
  - Data type: integer
  - Description: The number of days the supply was in stock before being completely consumed.

- **insert_dt_ist**
  - Data type: timestamp(6)
  - Description: The timestamp when the record was inserted into the database, in Indian Standard Time.

### Potential JOIN Keys and Business-Critical Columns
- **outlet_id**: Can be used to join with other tables that contain outlet-related information.
- **supply_id**: Can be used to join with tables detailing supply items, such as a supply catalog or inventory table.

### Potential Relationships to Other Tables
- The `supply_id` column suggests a relationship to a `supplies` table where details about each supply item can be found.
- The `outlet_id` column indicates a relationship to an `outlets` table which would contain information about each outlet location. 

This table is critical for understanding supply consumption patterns and managing inventory efficiently across various outlets.