# Table: blinkit.bistro_etls.agg_wtd_overall_key_business_metrics_bistro

### Description
The table `blinkit.bistro_etls.agg_wtd_overall_key_business_metrics_bistro` is designed to store aggregated weekly-to-date key business metrics for a bistro. This includes data on user transactions, sales performance, and product metrics across different cities. The table likely serves as a critical component for business analysis and decision-making, providing insights into market trends, customer behavior, and operational efficiency.

### Partitioning
This table is not partitioned. Queries on this table should be optimized based on the available indexes and careful selection of WHERE clause conditions.

### Columns
- **snapshot_date**
  - Data type: date
  - Description: The date on which the data snapshot was taken.

- **city**
  - Data type: varchar
  - Description: The city for which the metrics are aggregated.

- **transacting_users**
  - Data type: bigint
  - Description: The number of users who completed a transaction on the given snapshot date.

- **new_transacting_users**
  - Data type: bigint
  - Description: The number of users who completed their first transaction on the given snapshot date.

- **gmv**
  - Data type: real
  - Description: Gross Merchandise Value, total sales volume processed through the bistro.

- **aov**
  - Data type: real
  - Description: Average Order Value, the average monetary value of a transaction.

- **checkout_carts**
  - Data type: bigint
  - Description: The number of carts that reached the checkout phase.

- **delivered_carts**
  - Data type: bigint
  - Description: The number of carts that were successfully delivered.

- **sku_count**
  - Data type: bigint
  - Description: The total number of unique stock keeping units available for sale.

- **items_per_order**
  - Data type: decimal(21,1)
  - Description: The average number of items included in each order.

- **total_items_sold**
  - Data type: bigint
  - Description: The total number of items sold across all transactions.

- **unique_sku_per_order**
  - Data type: decimal(21,1)
  - Description: The average number of unique SKUs per order.

- **wau**
  - Data type: bigint
  - Description: Weekly Active Users, the number of unique users who transacted at least once during the week.

- **conversion**
  - Data type: decimal(21,1)
  - Description: The conversion rate, calculated as the ratio of transactions to visits or users.

- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: The timestamp when the ETL process captured this data snapshot, in Indian Standard Time.

### Potential JOIN Keys and Business-Critical Columns
- **snapshot_date** and **city** could be used as JOIN keys if there are other tables with date and city dimensions.
- Business-critical columns include **transacting_users**, **new_transacting_users**, **gmv**, **aov**, and **conversion** as they directly relate to the business's financial and operational performance.

### Potential Relationships
- The table might relate to other tables containing detailed user data, transaction logs, or inventory details, especially if those tables contain city or date fields for detailed analysis.