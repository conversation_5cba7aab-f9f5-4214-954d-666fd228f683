# Table: blinkit.bistro_etls.marketing_bistro_appsflyer_branch_install_base

### Description
The `marketing_bistro_appsflyer_branch_install_base` table stores detailed tracking and attribution data related to app installations and user activities from various marketing campaigns. It captures data from AppsFlyer and Branch, focusing on user interactions, device information, and campaign effectiveness. This table is essential for analyzing the performance of marketing efforts, understanding user engagement, and optimizing future advertising strategies.

### Partitioning
The table is partitioned on the `dt` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval and management.

### Columns
- **event_name**
  - Data type: varchar
  - Description: Name of the event triggered by the user.
  
- **source**
  - Data type: varchar
  - Description: The origin source of the traffic or event.
  
- **appsflyer_id**
  - Data type: varchar
  - Description: Unique identifier for a user in the AppsFlyer system.
  
- **advertising_id**
  - Data type: varchar
  - Description: Unique ID used for advertising purposes, typically linked to the device.
  
- **device_id**
  - Data type: varchar
  - Description: Unique identifier of the device used by the user.
  
- **idfa**
  - Data type: varchar
  - Description: Identifier for Advertisers on Apple devices.
  
- **idfv**
  - Data type: varchar
  - Description: Identifier for Vendors on Apple devices.
  
- **am_afid**
  - Data type: varchar
  - Description: AppsFlyer ID used for attribution modeling.
  
- **am_uid**
  - Data type: varchar
  - Description: Unique identifier for a user session in attribution modeling.
  
- **am_did**
  - Data type: varchar
  - Description: Device ID used in attribution modeling.
  
- **am_advert_id**
  - Data type: varchar
  - Description: Advertising ID used in attribution modeling.
  
- **f_uid**
  - Data type: varchar
  - Description: Facebook user ID.
  
- **f_did**
  - Data type: varchar
  - Description: Facebook device ID.
  
- **cityid_uid**
  - Data type: integer
  - Description: City identifier linked with a user ID.
  
- **app_open_uid**
  - Data type: integer
  - Description: Tracks when an app is opened by a specific user ID.
  
- **homepage_uid**
  - Data type: integer
  - Description: Tracks visits to the homepage by user ID.
  
- **product_added_uid**
  - Data type: integer
  - Description: Tracks products added to a cart by user ID.
  
- **cart_viewed_uid**
  - Data type: integer
  - Description: Tracks when a cart is viewed by a user ID.
  
- **order_completed_uid**
  - Data type: integer
  - Description: Tracks completed orders by user ID.
  
- **cityid_did**
  - Data type: integer
  - Description: City identifier linked with a device ID.
  
- **app_open_did**
  - Data type: integer
  - Description: Tracks app openings by device ID.
  
- **homepage_did**
  - Data type: integer
  - Description: Tracks homepage visits by device ID.
  
- **product_added_did**
  - Data type: integer
  - Description: Tracks products added to a cart by device ID.
  
- **cart_viewed_did**
  - Data type: integer
  - Description: Tracks when a cart is viewed by a device ID.
  
- **order_completed_did**
  - Data type: integer
  - Description: Tracks completed orders by device ID.
  
- **media_source**
  - Data type: varchar
  - Description: The media source from which the user originated.
  
- **af_prt**
  - Data type: varchar
  - Description: Partner ID in AppsFlyer.
  
- **af_channel**
  - Data type: varchar
  - Description: Marketing channel as identified in AppsFlyer.
  
- **campaign**
  - Data type: varchar
  - Description: Name of the marketing campaign.
  
- **af_c_id**
  - Data type: varchar
  - Description: Campaign ID in AppsFlyer.
  
- **af_adset**
  - Data type: varchar
  - Description: Ad set name in AppsFlyer.
  
- **af_adset_id**
  - Data type: varchar
  - Description: Ad set ID in AppsFlyer.
  
- **af_ad**
  - Data type: varchar
  - Description: Specific advertisement in AppsFlyer.
  
- **af_ad_id**
  - Data type: varchar
  - Description: Advertisement ID in AppsFlyer.
  
- **af_ad_type**
  - Data type: varchar
  - Description: Type of advertisement in AppsFlyer.
  
- **attributed_touch_time**
  - Data type: varchar
  - Description: Timestamp of the touch that led to conversion.
  
- **attributed_touch_time_selected_timezone**
  - Data type: varchar
  - Description: Timezone of the attributed touch time.
  
- **attributed_touch_type**
  - Data type: varchar
  - Description: Type of touch (e.g., click, impression) attributed to the conversion.
  
- **city**
  - Data type: varchar
  - Description: City from which the user is accessing.
  
- **country_code**
  - Data type: varchar
  - Description: Country code of the user's location.
  
- **install_time**
  - Data type: varchar
  - Description: Timestamp when the app was installed.
  
- **install_time_selected_timezone**
  - Data type: varchar
  - Description: Timezone of the install time.
  
- **user_agent**
  - Data type: varchar
  - Description: Browser or device user agent string of the user.
  
- **time_af**
  - Data type: bigint
  - Description: AppsFlyer time related to the user's action.
  
- **af_attribution_lookback**
  - Data type: varchar
  - Description: Lookback window for attribution in AppsFlyer.
  
- **af_keywords**
  - Data type: varchar
  - Description: Keywords used for the campaign in AppsFlyer.
  
- **af_reengagement_window**
  - Data type: varchar
  - Description: Time window for re-engagement attribution in AppsFlyer.
  
- **af_siteid**
  - Data type: varchar
  - Description: Site ID related to the campaign in AppsFlyer.
  
- **api_version**
  - Data type: varchar
  - Description: API version used for the data collection.
  
- **app_id**
  - Data type: varchar
  - Description: Unique identifier for the app.
  
- **app_name**
  - Data type: varchar
  - Description: Name of the app.
  
- **app_version**
  - Data type: varchar
  - Description: Version of the app installed.
  
- **bundle_id**
  - Data type: varchar
  - Description: Bundle identifier for the app.
  
- **carrier**
  - Data type: varchar
  - Description: Mobile carrier of the user's device.
  
- **contributors**
  - Data type: varchar
  - Description: Contributors to the event or campaign.
  
- **device_category**
  - Data type: varchar
  - Description: Category of the device used (e.g., mobile, tablet).
  
- **device_download_time**
  - Data type: varchar
  - Description: Timestamp when the device downloaded the app.
  
- **device_download_time_selected_timezone**
  - Data type: varchar
  - Description: Timezone of the device download time.
  
- **device_type**
  - Data type: varchar
  - Description: Type of device used by the user (e.g., smartphone, tablet).
  
- **dma**
  - Data type: varchar
  - Description: Designated Market Area the user is in.
  
- **gp_broadcast_referrer**
  - Data type: varchar
  - Description: Google Play broadcast referrer information.
  
- **gp_click_time**
  - Data type: varchar
  - Description: Timestamp when the user clicked on a Google Play link.
  
- **gp_install_begin**
  - Data type: varchar
  - Description: Timestamp when the installation began from Google Play.
  
- **gp_referrer**
  - Data type: varchar
  - Description: Referrer URL from Google Play.
  
- **http_referrer**
  - Data type: varchar
  - Description: HTTP referrer URL.
  
- **keyword_id**
  - Data type: varchar
  - Description: Identifier for the keyword used in the campaign.
  
- **keyword_match_type**
  - Data type: varchar
  - Description: Type of keyword match used in the campaign.
  
- **language_af**
  - Data type: varchar
  - Description: Language setting in AppsFlyer.
  
- **match_type**
  - Data type: varchar
  - Description: Type of match used for attributing the user action.
  
- **network_account_id**
  - Data type: varchar
  - Description: Account ID for the network involved in the campaign.
  
- **operator_af**
  - Data type: varchar
  - Description: Operator information from AppsFlyer.
  
- **original_url**
  - Data type: varchar
  - Description: Original URL that led to the installation or event.
  
- **postal_code**
  - Data type: varchar
  - Description: Postal code of the user's location.
  
- **region**
  - Data type: varchar
  - Description: Region from which the user is accessing.
  
- **retargeting_conversion_type**
  - Data type: varchar
  - Description: Type of conversion for retargeting in AppsFlyer.
  
- **sdk_version**
  - Data type: varchar
  - Description: Version of the SDK used for tracking.
  
- **selected_currency**
  - Data type: varchar
  - Description: Currency selected by the user.
  
- **selected_timezone**
  - Data type: varchar
  - Description: Timezone selected by the user.
  
- **state**
  - Data type: varchar
  - Description: State from which the user is accessing.
  
- **os_version**
  - Data type: varchar
  - Description: Operating system version of the user's device.
  
- **row_computed_at**
  - Data type: timestamp(6)
  - Description: Timestamp when the row data was computed.
  
- **master_customer_id**
  - Data type: varchar
  - Description: Master identifier for the customer across different systems.
  
- **dt**
  - Data type: varchar
  - Description: Date partition key for the table, critical for query performance.
  
- **date_**
  - Data type: varchar
  - Description: General date related to the event or data entry.

### Potential JOIN Keys and Business-Critical Columns
- **appsflyer_id**, **device_id**, **advertising_id**: These are potential JOIN keys for linking with other user or device-related tables.
- **event_name**, **campaign**, **media_source**: Business-critical columns for analyzing marketing campaign performance.

### Potential Relationships
- Columns like **appsflyer_id**, **device_id**, **advertising_id** suggest potential relationships with other tables that store user or device-specific data, possibly for further detailed analysis or tracking across different platforms or services.