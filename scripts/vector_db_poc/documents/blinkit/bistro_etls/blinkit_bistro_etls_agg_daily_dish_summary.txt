# Table: blinkit.bistro_etls.agg_daily_dish_summary

### Description
The `blinkit.bistro_etls.agg_daily_dish_summary` table aggregates daily data related to dishes offered by merchants. It includes information about sales, ratings, preparation times, and inventory metrics. This table is likely used for analyzing daily business performance, product popularity, and operational efficiency at a granular level.

### Partitioning
This table is not partitioned. Queries on this table might not be optimized for performance without specific indexing strategies.

### Columns
- **date_**
  - Data type: varchar
  - Description: The date for which the data is aggregated.
  
- **week**
  - Data type: varchar
  - Description: The week of the year for which the data is aggregated.
  
- **merchant_id**
  - Data type: bigint
  - Description: Unique identifier for the merchant.
  
- **merchant_name**
  - Data type: varchar
  - Description: Name of the merchant.
  
- **city_name**
  - Data type: varchar
  - Description: The city where the merchant is located.
  
- **product_name**
  - Data type: varchar
  - Description: Name of the product (dish).
  
- **product_type**
  - Data type: varchar
  - Description: Type of the product (e.g., vegetarian, non-vegetarian).
  
- **product_id**
  - Data type: bigint
  - Description: Unique identifier for the product.
  
- **l1_category**
  - Data type: varchar
  - Description: The top-level category of the product.
  
- **l2_category**
  - Data type: varchar
  - Description: The second-level category of the product.
  
- **orders**
  - Data type: bigint
  - Description: Total number of orders placed for the product on the given date.
  
- **items_sold**
  - Data type: bigint
  - Description: Total number of items sold.
  
- **ratings**
  - Data type: double
  - Description: Average rating for the product.
  
- **rated_products**
  - Data type: bigint
  - Description: Number of times the product was rated.
  
- **wait_time**
  - Data type: varchar
  - Description: Average wait time for the product.
  
- **prep_time**
  - Data type: varchar
  - Description: Average preparation time for the product.
  
- **items_made**
  - Data type: double
  - Description: Total number of items prepared.
  
- **total_selling_price**
  - Data type: double
  - Description: Total revenue generated from the product.
  
- **oos_impressions**
  - Data type: double
  - Description: Number of times the product was viewed while out of stock.
  
- **impressions**
  - Data type: double
  - Description: Total number of times the product was viewed.
  
- **added**
  - Data type: double
  - Description: Number of times the product was added to a cart.
  
- **store_att_orders**
  - Data type: double
  - Description: Number of store-attended orders for the product.

### Potential JOIN Keys and Business-Critical Columns
- **JOIN Keys**: `merchant_id`, `product_id` can be used to join with other tables containing merchant or product details.
- **Business-Critical Columns**: `orders`, `items_sold`, `total_selling_price`, and `ratings` are critical for analyzing business performance.

### Potential Relationships
- **merchant_id** could be used to join with a `merchants` table.
- **product_id** could be used to join with a `products` table to fetch more detailed product information.
- **city_name** could relate to a geographical or demographics table for regional analysis.