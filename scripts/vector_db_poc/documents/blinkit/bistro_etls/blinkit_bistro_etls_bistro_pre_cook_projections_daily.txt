# Table: blinkit.bistro_etls.bistro_pre_cook_projections_daily

### Description
The `blinkit.bistro_etls.bistro_pre_cook_projections_daily` table is designed to store daily projections for pre-cooked dishes at various outlets. It includes details such as the quantity of each dish projected to be needed, sales adjustments based on day of the week and hour, and preparation times. This data helps in planning and optimizing the preparation of dishes to meet anticipated demand.

### Partitioning
This table is not partitioned. Queries on this table might not be as performant as those on partitioned tables, especially when dealing with large datasets.

### Columns
- **outlet_id**
  - Data type: integer
  - Description: The identifier for the outlet where the projection is applicable.
  
- **projection_date**
  - Data type: date
  - Description: The date for which the projection is made.
  
- **sub_pid**
  - Data type: integer
  - Description: The identifier for a specific sub-product or dish.
  
- **sub_name**
  - Data type: varchar
  - Description: The name of the sub-product or dish.
  
- **sub_dish_quantity**
  - Data type: integer
  - Description: The projected quantity of the sub-product or dish needed for the day.
  
- **prep_hour**
  - Data type: integer
  - Description: The hour of the day for which the preparation quantity is projected.
  
- **adj_sale**
  - Data type: double
  - Description: Adjusted sales figures taking into account various factors not specified in the schema.
  
- **dow_adj_sale**
  - Data type: double
  - Description: Sales adjusted based on the day of the week.
  
- **hourly_sale**
  - Data type: double
  - Description: Sales figures broken down by hour.
  
- **pre_prep_qty**
  - Data type: integer
  - Description: The quantity of the dish that needs to be pre-prepared.

### Potential JOIN Keys and Business-Critical Columns
- **outlet_id** could be a potential JOIN key with other tables that contain outlet-specific information such as outlet locations or outlet performance metrics.
- **sub_pid** might be used to JOIN with product tables that detail more information about each dish or product.
- Business-critical columns likely include **projection_date**, **sub_dish_quantity**, **adj_sale**, **dow_adj_sale**, and **hourly_sale** as they directly relate to sales performance and operational planning.

### Potential Relationships to Other Tables
- The **outlet_id** can be used to relate to an `outlets` table that would contain location and other outlet-specific details.
- The **sub_pid** suggests a relationship to a `products` or `dishes` table where detailed information about each dish, including ingredients and cost, might be stored.

This documentation provides a foundational understanding of the `blinkit.bistro_etls.bistro_pre_cook_projections_daily` table, aiding in efficient data querying and analysis.