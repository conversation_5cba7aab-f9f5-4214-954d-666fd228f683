# Table: blinkit.bistro_etls.bistro_hourly_inventory_snapshots

### Description
The `blinkit.bistro_etls.bistro_hourly_inventory_snapshots` table stores hourly snapshots of inventory levels for various products across different outlets and merchants. This table is crucial for tracking inventory changes, managing stock levels, and analyzing inventory trends over time.

### Partitioning
The table is partitioned by the `date_ist` column. This partition key is CRITICAL for query performance and MUST be used in WHERE clauses ALWAYS in any condition to ensure efficient data retrieval.

### Columns
- **product_id**
  - Data type: integer
  - Description: The unique identifier for a product.

- **item_id**
  - Data type: integer
  - Description: The unique identifier for an item, potentially a specific instance or variation of a product.

- **outlet_id**
  - Data type: integer
  - Description: The unique identifier for an outlet where the product is available.

- **merchant_id**
  - Data type: integer
  - Description: The unique identifier for a merchant associated with the product at the outlet.

- **property_id**
  - Data type: integer
  - Description: An identifier for a specific property or attribute of the product.

- **property_name**
  - Data type: varchar
  - Description: The name of the property or attribute of the product.

- **property_value**
  - Data type: varchar
  - Description: The value of the property or attribute of the product.

- **cms_flag**
  - Data type: integer
  - Description: A flag used for content management purposes, possibly indicating special conditions or statuses.

- **current_inventory**
  - Data type: integer
  - Description: The current inventory level of the product at the outlet.

- **date_ist**
  - Data type: date
  - Description: The date in IST (Indian Standard Time) when the inventory snapshot was taken.

- **updated_at_ist**
  - Data type: timestamp(6)
  - Description: The exact timestamp in IST when the record was last updated.

### Key Relationships and Joins
- Potential JOIN keys include `product_id`, `item_id`, `outlet_id`, and `merchant_id`. These keys can be used to join with other tables such as product details, item specifics, outlet information, and merchant profiles.
- Business-critical columns include `current_inventory`, `date_ist`, and `updated_at_ist` for real-time inventory management and historical analysis.

This documentation provides a clear overview of the `blinkit.bistro_etls.bistro_hourly_inventory_snapshots` table, facilitating efficient and effective use within the bedrock knowledge base.