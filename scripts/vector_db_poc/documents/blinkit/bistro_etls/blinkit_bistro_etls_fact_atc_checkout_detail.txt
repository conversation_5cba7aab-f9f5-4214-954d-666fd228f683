# Table: blinkit.bistro_etls.fact_atc_checkout_detail

### Description
The `blinkit.bistro_etls.fact_atc_checkout_detail` table appears to be a fact table designed to store detailed records of user interactions with a digital platform, specifically focusing on actions related to adding to cart (ATC) and checkout processes. It captures a wide range of data including user traits, product properties, session details, and transaction specifics, which are useful for analyzing user behavior, product performance, and sales effectiveness.

### Partitioning
The table is partitioned on the following keys:
- `at_date_ist`: This key is critical for query performance and must be used in WHERE clauses to ensure efficient data retrieval.
- `hour_`: This key is also critical for query performance and must be used in WHERE clauses to optimize data access.

### Columns
- **at_date_ist**
  - Data type: date
  - Description: The date of the event in IST timezone.
- **at_ist**
  - Data type: timestamp(6)
  - Description: The exact timestamp of the event in IST timezone.
- **platform**
  - Data type: varchar
  - Description: The platform from which the event was generated.
- **name**
  - Data type: varchar
  - Description: The name associated with the event, possibly the user's name.
- **hour_**
  - Data type: bigint
  - Description: The hour of the day when the event occurred.
- **traits__user_id**
  - Data type: varchar
  - Description: Unique identifier for the user involved in the event.
- **device_uuid**
  - Data type: varchar
  - Description: Unique device identifier from which the event was logged.
- **traits__city_name**
  - Data type: varchar
  - Description: The city name associated with the user's location.
- **traits__merchant_id**
  - Data type: integer
  - Description: Identifier for the merchant involved in the transaction.
- **properties__product_id**
  - Data type: varchar
  - Description: Unique identifier for the product involved in the event.
- **properties__page_name**
  - Data type: varchar
  - Description: The name of the page where the event occurred.
- **properties__page_type**
  - Data type: varchar
  - Description: The type of page (e.g., product detail, checkout page) where the event occurred.
- **properties__product_position**
  - Data type: integer
  - Description: The position of the product on the page when the event occurred.
- **properties__search_keyword_type**
  - Data type: varchar
  - Description: Type of search keyword used during the event.
- **properties__search_previous_keyword**
  - Data type: varchar
  - Description: The previous search keyword entered by the user before the current one.
- **properties__search_input_keyword**
  - Data type: varchar
  - Description: The search keyword input by the user during the event.
- **properties__search_actual_keyword**
  - Data type: varchar
  - Description: The actual search keyword that triggered the event.
- **properties__search_keyword_parent**
  - Data type: varchar
  - Description: Parent keyword related to the search keyword used.
- **properties__page_visit_id**
  - Data type: varchar
  - Description: Unique identifier for the page visit during which the event occurred.
- **properties__parent_product**
  - Data type: varchar
  - Description: Identifier for the parent product related to the product involved in the event.
- **properties__price**
  - Data type: double
  - Description: Price of the product at the time of the event.
- **properties__widget_name**
  - Data type: varchar
  - Description: Name of the widget interacting during the event.
- **properties__widget_title**
  - Data type: varchar
  - Description: Title of the widget interacting during the event.
- **properties__sub_page_name**
  - Data type: varchar
  - Description: Name of the sub-page visited during the event.
- **properties__sub_page_visit_id**
  - Data type: varchar
  - Description: Unique identifier for the sub-page visit during the event.
- **properties__widget_tracking_id**
  - Data type: varchar
  - Description: Tracking identifier for the widget used during the event.
- **traits__cart_id**
  - Data type: varchar
  - Description: Unique identifier for the cart involved in the event.
- **properties__inventory**
  - Data type: varchar
  - Description: Inventory status of the product at the time of the event.
- **properties__page_title**
  - Data type: varchar
  - Description: Title of the page where the event occurred.
- **traits__merchant_name**
  - Data type: varchar
  - Description: Name of the merchant involved in the event.
- **traits__segment_enabled_features**
  - Data type: varchar
  - Description: Features enabled for the user segment at the time of the event.
- **properties__child_widget_id**
  - Data type: varchar
  - Description: Identifier for any child widget involved during the event.
- **properties__page_id**
  - Data type: varchar
  - Description: Unique identifier for the page where the event occurred.
- **properties__sub_page_title**
  - Data type: varchar
  - Description: Title of the sub-page visited during the event.
- **properties__title**
  - Data type: varchar
  - Description: Title associated with the event or product.
- **properties__child_widget_title**
  - Data type: varchar
  - Description: Title of any child widget involved during the event.
- **app__version**
  - Data type: varchar
  - Description: Version of the application from which the event was logged.
- **traits__app_version_code**
  - Data type: bigint
  - Description: Numeric code representing the version of the application.
- **session_uuid**
  - Data type: varchar
  - Description: Unique identifier for the session during which the event occurred.
- **properties__badge**
  - Data type: varchar
  - Description: Any badge associated with the product or event.
- **properties__collection_id**
  - Data type: varchar
  - Description: Identifier for a collection of products involved in the event.
- **properties__collection_name**
  - Data type: varchar
  - Description: Name of the collection of products involved in the event.
- **properties__widget_position**
  - Data type: integer
  - Description: Position of the widget on the page during the event.
- **properties__widget_variation_id**
  - Data type: varchar
  - Description: Identifier for different variations of the widget used during the event.
- **properties__child_widget_tracking_id**
  - Data type: varchar
  - Description: Tracking identifier for any child widget used during the event.
- **properties__filter**
  - Data type: varchar
  - Description: Filters applied during the event, possibly in a search or product listing.
- **properties__child_widget_position**
  - Data type: integer
  - Description: Position of any child widget involved during the event.
- **id**
  - Data type: varchar
  - Description: Unique identifier for the record.
- **matching_flag**
  - Data type: varchar
  - Description: Flag indicating whether there was a match (e.g., product match, search match) during the event.
- **checkout_flag**
  - Data type: integer
  - Description: Flag indicating whether the checkout process was initiated or completed.
- **order_id**
  - Data type: bigint
  - Description: Unique identifier for the order created as a result of the event.
- **cart_id**
  - Data type: bigint
  - Description: Unique identifier for the cart used during the event.
- **order_create_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp for when the order was created in IST timezone.
- **unit_selling_price**
  - Data type: real
  - Description: Selling price per unit of the product at the time of the event.
- **total_checkout_quantity**
  - Data type: bigint
  - Description: Total quantity of products checked out during the event.
- **extra_quantity**
  - Data type: bigint
  - Description: Extra quantity added during the checkout process.
- **traits__session_launch_source**
  - Data type: varchar
  - Description: Source from which the session was launched.
- **properties__product_list_id**
  - Data type: varchar
  - Description: Identifier for a list of products involved in the event.
- **properties__last_page_name**
  - Data type: varchar
  - Description: Name of the last page visited before the current event.
- **properties__last_page_visit_id**
  - Data type: varchar
  - Description: Unique identifier for the last page visit before the current event.

### Potential JOIN Keys and Business-Critical Columns
- **traits__user_id**, **device_uuid**, **session_uuid**: These can be used to join with user or session tables to get more detailed user behavior analytics.
- **properties__product_id**, **order_id**: These are crucial for joining with product and order tables to analyze product performance and order details.
- **traits__merchant_id**, **traits__cart_id**: Important for linking with merchant and cart tables for transactional analysis.

### Relationships to Other Tables
- **properties__product_id** could be related to a `products` table.
- **order_id** could be related to an `orders` table.
- **traits__user_id** could be related to a `users` table.
- **traits__merchant_id** could be related to a `merchants` table.
- **traits__cart_id** and **cart_id** could be related to a `carts` table.