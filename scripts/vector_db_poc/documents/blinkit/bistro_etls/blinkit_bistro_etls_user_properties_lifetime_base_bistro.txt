# Table: blinkit.bistro_etls.user_properties_lifetime_base_bistro

### Description
The table `blinkit.bistro_etls.user_properties_lifetime_base_bistro` is designed to store comprehensive lifetime activity and financial metrics of customers. It aggregates various aspects of a customer's interaction with the service, such as gross merchandise value, cart activities, costs, and promotional benefits. This data is crucial for analyzing customer behavior, profitability, and retention over time.

### Partitioning
This table is not partitioned. Queries on this table should be optimized based on the available indexes and should consider potential full table scans.

### Columns
- **snapshot_date_ist**
  - Data type: date
  - Description: The date when the data snapshot was taken.

- **customer_id**
  - Data type: bigint
  - Description: Unique identifier for the customer.

- **lifetime_gmv**
  - Data type: real
  - Description: The lifetime gross merchandise value generated by the customer.

- **lifetime_carts**
  - Data type: bigint
  - Description: Total number of shopping carts created by the customer over their lifetime.

- **lifetime_retained_margin**
  - Data type: real
  - Description: Total profit margin retained from the customer after accounting for various costs.

- **lifetime_delivery_cost**
  - Data type: real
  - Description: Total delivery costs associated with the customer's orders.

- **lifetime_slot_charges**
  - Data type: real
  - Description: Charges applied to the customer for delivery slot bookings.

- **lifetime_packaging_cost**
  - Data type: real
  - Description: Total cost of packaging for the customer's orders.

- **lifetime_tip_amount**
  - Data type: real
  - Description: Total tip amount given by the customer.

- **lifetime_promo_discount**
  - Data type: real
  - Description: Total promotional discounts availed by the customer.

- **lifetime_promo_cashback**
  - Data type: real
  - Description: Total cashback received by the customer under promotional schemes.

- **first_cart_checkout_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the first cart checkout by the customer.

- **second_last_cart_checkout_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the second last cart checkout by the customer.

- **last_cart_checkout_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp of the most recent cart checkout by the customer.

- **lifetime_net_cost_to_customer**
  - Data type: real
  - Description: Net cost incurred by the customer after all deductions and additions.

- **recent_city_ordered_from**
  - Data type: varchar
  - Description: The most recent city from which the customer placed an order.

### Potential JOIN Keys and Business-Critical Columns
- **customer_id**: Likely a primary key and can be used to join with other customer-related tables such as order details, customer demographics, etc.
- **recent_city_ordered_from**: Can be used to join with geographic or regional data tables for location-based analysis.

### Relationships to Other Tables
- **customer_id** could be used to join with tables containing detailed order transactions, customer profiles, or customer service interactions.
- **recent_city_ordered_from** might link to tables containing city-specific data or regional performance metrics.