# Table: blinkit.bistro_etls.dim_map_dish_subdish_ingredient_bistro_06052025_bckup

### Description
The `blinkit.bistro_etls.dim_map_dish_subdish_ingredient_bistro_06052025_bckup` table appears to be a dimension table used in a data warehouse for a restaurant or food service business. This table likely maps dishes to their sub-dishes and ingredients, detailing quantities and conversion factors for each store. It may be used for inventory management, recipe detailing, and cost analysis.

### Partitioning
This table is not partitioned. Queries on this table may not benefit from partition pruning and could be slower on large datasets.

### Columns
- **dish_pid**
  - Data type: bigint
  - Description: A unique identifier for the dish.

- **store_id**
  - Data type: bigint
  - Description: Identifier for the store where the dish is available.

- **pid_combined**
  - Data type: bigint
  - Description: A combined identifier possibly linking dishes with sub-dishes or ingredients.

- **supply_pid_combined**
  - Data type: bigint
  - Description: A combined identifier linking the supply chain details related to the dish or ingredient.

- **ing_sub_flag**
  - Data type: varchar
  - Description: A flag indicating whether the item is an ingredient or a sub-dish.

- **combined_qty**
  - Data type: bigint
  - Description: The quantity of the dish or ingredient combined, likely used for inventory or recipe calculations.

- **conversion_factor**
  - Data type: decimal(12,3)
  - Description: Factor used to convert quantities from one unit to another.

- **is_current**
  - Data type: boolean
  - Description: A boolean flag indicating whether the record is the current active record.

- **dbt_scd_id**
  - Data type: varchar
  - Description: Slowly Changing Dimension (SCD) identifier used in data warehousing to track changes over time.

- **dbt_updated_at**
  - Data type: timestamp(6)
  - Description: The timestamp when the record was last updated.

- **dbt_valid_from**
  - Data type: timestamp(6)
  - Description: The start timestamp from when the record is considered valid.

- **dbt_valid_to**
  - Data type: timestamp(6)
  - Description: The end timestamp until when the record is considered valid.

- **station**
  - Data type: varchar
  - Description: The specific station or location within the store where the dish is prepared or ingredients are handled.

### Potential JOIN Keys and Business-Critical Columns
- **dish_pid**: Likely a primary key and could be used to join with other tables that contain dish details.
- **store_id**: Could be used to join with other store-related tables for regional analysis or inventory management.
- **pid_combined** and **supply_pid_combined**: These could be critical in linking various tables in the database that deal with inventory, supply chain, and recipe management.

### Potential Relationships
- The `store_id` might relate to a `stores` table that includes location and other store-specific information.
- `dish_pid`, `pid_combined`, and `supply_pid_combined` might be used to join with tables that detail individual dishes, sub-dishes, or ingredient specifics, enhancing the depth of analysis possible with this data.

This documentation provides a structured overview of the `blinkit.bistro_etls.dim_map_dish_subdish_ingredient_bistro_06052025_bckup` table, facilitating efficient data handling and query optimization in a data warehouse environment.