# Table: blinkit.bistro_etls.weekly_customer_conv_funnel_metrics_bistro

### Description
The `blinkit.bistro_etls.weekly_customer_conv_funnel_metrics_bistro` table is designed to store weekly metrics related to customer conversion funnels for a bistro. It tracks various stages of user interaction from app launch to final checkout, segmented by different channels, platforms, and cities. This data helps in analyzing the effectiveness of different marketing channels and platforms in converting users at various stages of the customer journey.

### Partitioning
This table is not partitioned. Queries on this table might not be optimized for performance unless indexed appropriately on frequently queried columns.

### Columns
- **snapshot_week_ist**
  - Data type: date
  - Description: The week for which the data snapshot is taken, typically representing the end of a reporting period.

- **channel**
  - Data type: varchar
  - Description: The marketing or acquisition channel through which the user was engaged.

- **platform**
  - Data type: varchar
  - Description: The platform (e.g., iOS, Android, Web) through which the user accessed the services.

- **city**
  - Data type: varchar
  - Description: The city where the user is located.

- **merchant_id**
  - Data type: varchar
  - Description: Unique identifier for the merchant.

- **user_bucket**
  - Data type: varchar
  - Description: Categorization of users into different buckets based on predefined criteria.

- **user_type**
  - Data type: varchar
  - Description: Type of user, such as new or returning.

- **weekly_active_users**
  - Data type: bigint
  - Description: Count of users who were active during the week.

- **app_launch_to_home_page_visit**
  - Data type: bigint
  - Description: Number of users who launched the app and visited the home page.

- **wau_to_home_page_visit**
  - Data type: bigint
  - Description: Conversion ratio of weekly active users to those who visited the home page.

- **app_launch_to_home_page_visit_nsa**
  - Data type: bigint
  - Description: Number of users who launched the app and visited the home page, not subject to any specific adjustments.

- **wau_to_atc**
  - Data type: bigint
  - Description: Conversion ratio of weekly active users to those who added items to the cart.

- **atc_to_cv**
  - Data type: bigint
  - Description: Conversion ratio from adding to cart to creating a checkout.

- **cv_to_user_registered**
  - Data type: bigint
  - Description: Conversion from creating a checkout to registering a user account.

- **cv_to_address_added**
  - Data type: bigint
  - Description: Conversion from creating a checkout to adding an address.

- **cv_to_payment**
  - Data type: bigint
  - Description: Conversion from creating a checkout to initiating a payment.

- **payment_to_checkout**
  - Data type: bigint
  - Description: Conversion from making a payment to completing the checkout process.

- **cv_to_checkout**
  - Data type: bigint
  - Description: Overall conversion from creating a checkout to completing it.

- **overall_conversion**
  - Data type: bigint
  - Description: Overall conversion rate across all stages of the funnel.

- **etl_snapshot_ts_ist**
  - Data type: timestamp(6)
  - Description: Timestamp when the ETL process that generated this record was completed.

### Potential JOIN Keys and Business-Critical Columns
- **merchant_id**: Can be used to join with merchant tables to get more details about the merchants.
- **city**: Possible join key for geographical data tables for regional analysis.
- **user_type**, **weekly_active_users**, and **overall_conversion** are critical for business analysis to gauge user engagement and conversion effectiveness.

### Potential Relationships
- **merchant_id** might relate to a `merchants` table containing detailed merchant information.
- **user_type** could be used to join with a `users` table that categorizes user details.
- Metrics columns like **app_launch_to_home_page_visit** and **cv_to_checkout** could be used in conjunction with user behavior logs or session data tables.