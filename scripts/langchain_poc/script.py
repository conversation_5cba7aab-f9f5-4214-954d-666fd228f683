# langchain_trino_agent.py (refactored for reactive schema exploration
from langchain.agents import Too<PERSON>, AgentExecutor, AgentType, initialize_agent
from langchain_aws.chat_models.bedrock import ChatBedrock
from langchain.tools import BaseTool
from typing import List, Dict, Any
import os
import json
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, inspect
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import select

class TrinoAgent:
    def __init__(self):
        self.llm = None
        self.engine = None
        self.Session = None
        self.agent_executor = None
        self._initialized = False

    def _initialize(self):
        if self._initialized:
            return
            
        # Initialize LLM with streaming enabled
        self.llm = ChatBedrock(
            model_id="anthropic.claude-3-5-sonnet-20240620-v1:0",
            region_name="ap-southeast-1",
            model_kwargs={
                "temperature": 0.1,
                "max_tokens": 4000
            },
            streaming=True
        )
        
        # Initialize database connection without schema inspection
        self.engine = create_engine(
            "trino://dwh_admin@${TRINO_HOST}:${TRINO_PORT}/${TRINO_CATALOG}/${TRINO_SCHEMA}",
            connect_args={
                'http_scheme': 'http',
                'auth': None,
                'catalog': 'blinkit_iceberg',
                'schema': 'dwh'
            },
            echo=False,
            future=True
        )
        self.Session = sessionmaker(bind=self.engine)
        
        # Create custom tools
        tools = self._create_tools()
        
        # Import required components
        from langchain.agents import AgentExecutor
        from langchain.agents.structured_chat.base import StructuredChatAgent
        from langchain.agents import AgentType
        
        # Initialize the agent with tools
        self.agent_executor = initialize_agent(
            tools=tools,
            llm=self.llm,
            agent=AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION,
            verbose=True,
            handle_parsing_errors=True,
            return_intermediate_steps=True,
            max_iterations=15
        )
        
        self._initialized = True

    def _create_tools(self) -> List[BaseTool]:
        """Create custom tools for database interaction."""
        return [
            Tool(
                name="list_tables",
                func=self._list_tables,
                description="""Use this to list all available tables in the database. 
                Always use this first to understand what tables are available.
                Input should be an empty string."""
            ),
            Tool(
                name="describe_table",
                func=self._describe_table,
                description="""Use this to get column information for a specific table. 
                Input should be a string with just the table name."""
            ),
            Tool(
                name="show_create_table",
                func=self._show_create_table,
                description="""Use this to get the DDL/CREATE TABLE statement for a table. 
                Input should be a string with just the table name."""
            ),
            Tool(
                name="get_sample_data",
                func=self._get_sample_data,
                description="""Use this to get sample rows from a table. 
                Input should be a string with just the table name."""
            ),
            Tool(
                name="execute_query",
                func=self._execute_query,
                description="""Use this to execute a custom SQL query. 
                Input should be a valid SQL query as a string."""
            )
        ]

    def _execute_sql(self, sql: str, params: dict = None) -> list:
        """Execute SQL query and return results."""
        with self.engine.connect() as conn:
            if params:
                result = conn.execute(text(sql), params)
            else:
                result = conn.execute(text(sql))
            
            if result.returns_rows:
                columns = result.keys()
                return [dict(zip(columns, row)) for row in result.fetchall()]
            return []

    def _list_tables(self, _: str) -> str:
        """List all tables in the database."""
        try:
            # Use a direct SQL query to list tables without reflection
            tables = self._execute_sql("SHOW TABLES")
            table_names = [row['Table'] for row in tables if 'Table' in row]
            return json.dumps({"tables": table_names}, indent=2)
        except Exception as e:
            return json.dumps({"error": f"Error listing tables: {str(e)}"})

    def _describe_table(self, table_name: str) -> str:
        """Describe the structure of a table."""
        try:
            # First verify the table exists
            tables = self._execute_sql("SHOW TABLES")
            table_names = [row['Table'].lower() for row in tables if 'Table' in row]
            
            if table_name.lower() not in table_names:
                return json.dumps({"error": f"Table '{table_name}' not found"})
                
            # Get column info
            columns = self._execute_sql(f"DESCRIBE {table_name}")
            return json.dumps({
                "table": table_name, 
                "columns": [{"column": c['Column'], "type": c['Type'], "extra": c.get('Extra', '')} 
                           for c in columns]
            }, indent=2)
        except Exception as e:
            return json.dumps({"error": f"Error describing table: {str(e)}"})

    def _show_create_table(self, table_name: str) -> str:
        """Get the CREATE TABLE statement for a table."""
        try:
            result = self._execute_sql(f"SHOW CREATE TABLE {table_name}")
            if result and 'Create Table' in result[0]:
                return result[0]['Create Table']
            return f"Could not retrieve CREATE TABLE statement for {table_name}"
        except Exception as e:
            return f"Error getting CREATE TABLE statement: {str(e)}"

    def _get_sample_data(self, table_name: str, limit: int = 5) -> str:
        """Get sample data from a table."""
        try:
            # First verify the table exists
            tables = self._execute_sql("SHOW TABLES")
            table_names = [row['Table'].lower() for row in tables if 'Table' in row]
            
            if table_name.lower() not in table_names:
                return json.dumps({"error": f"Table '{table_name}' not found"})
                
            # Get sample data
            rows = self._execute_sql(f"SELECT * FROM {table_name} LIMIT {limit}")
            return json.dumps({
                "table": table_name,
                "sample_data": rows
            }, indent=2)
        except Exception as e:
            return json.dumps({"error": f"Error getting sample data: {str(e)}"})

    def _execute_query(self, query: str) -> str:
        """Execute a custom SQL query."""
        try:
            # Basic validation to prevent destructive operations
            query_lower = query.lower().strip()
            if any(cmd in query_lower for cmd in ['drop ', 'delete from', 'truncate', 'update ', 'insert ']):
                return "Error: This operation is not allowed for safety reasons."
                
            result = self._execute_sql(query)
            return json.dumps({
                "query": query,
                "result": result
            }, indent=2, default=str)
        except Exception as e:
            return json.dumps({"error": f"Error executing query: {str(e)}"})

    def run_query(self, question: str) -> str:
        """Run a natural language query using the agent."""
        if not self._initialized:
            self._initialize()
        try:
            return self.agent_executor.run(question)
        except Exception as e:
            return f"Error processing your request: {str(e)}"

# Initialize the agent lazily
trino_agent = TrinoAgent()

def run_query(question: str) -> str:
    return trino_agent.run_query(question)


# --- CLI or API Usage ---
if __name__ == "__main__":
    print("LangChain Trino Agent Ready. Ask a question:")
    while True:
        user_input = input("\n> ")
        if user_input.lower() in ("exit", "quit"): break
        try:
            result = run_query(user_input)
            print("\nAnswer:", result)
        except Exception as e:
            print("\n[Error]", str(e))
