import streamlit as st
from script import TrinoAgent
import json
from typing import Dict, Any

# Set page config
st.set_page_config(
    page_title="Trino SQL Assistant",
    page_icon="🔍",
    layout="wide"
)

# Custom CSS for better styling
st.markdown("""
    <style>
    /* Fixed chat input at bottom */
    .main .block-container {
        padding-bottom: 100px;
    }
    
    .stTextInput {
        position: fixed;
        bottom: 2rem;
        left: 2rem;
        right: 2rem;
        background: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        z-index: 100;
    }
    
    .stTextInput input {
        height: 3rem;
        font-size: 1rem;
    }
    .stButton>button {
        width: 100%;
        height: 3rem;
        font-size: 1rem;
        background-color: #4CAF50;
        color: white;
    }
    .stButton>button:hover {
        background-color: #45a049;
    }
    .stMarkdown {
        font-size: 1.1rem;
    }
    .tool-call {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        font-family: monospace;
        white-space: pre-wrap;
        border-left: 4px solid #4CAF50;
    }
    .tool-result {
        background-color: #e6f7ff;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        font-family: monospace;
        white-space: pre-wrap;
        border-left: 4px solid #2196F3;
    }
    pre {
        margin: 0.5rem 0;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        overflow-x: auto;
    }
    </style>
""", unsafe_allow_html=True)

# Initialize session state
if 'agent' not in st.session_state:
    try:
        st.session_state.agent = TrinoAgent()
        # Force initialization
        st.session_state.agent._initialize()
        st.session_state.messages = []
        st.session_state.thinking = False
    except Exception as e:
        st.error(f"Failed to initialize Trino agent: {str(e)}")
        st.stop()

def display_message(content, is_user=False, is_ai=False, is_tool_call=False, is_tool_result=False, thoughts=None):
    """Display a message in the chat interface with appropriate styling."""
    with st.chat_message("user" if is_user else "assistant"):
        if is_tool_call:
            st.markdown(f'<div class="tool-call">🔧 <strong>Tool Call:</strong><br/>{content}</div>', 
                      unsafe_allow_html=True)
        elif is_tool_result:
            st.markdown(f'<div class="tool-result">📊 <strong>Tool Result:</strong><br/>{content}</div>', 
                      unsafe_allow_html=True)
        else:
            st.markdown(content)
            
            # Add collapsible thought process if available
            if thoughts and len(thoughts) > 0:
                with st.expander("Show Thought Process", expanded=True):
                    st.markdown("### 🤔 Thought Process")
                    for i, thought in enumerate(thoughts, 1):
                        st.markdown(f"""
                        <div style='margin: 10px 0; padding: 12px; 
                                    background: #f8f9fa; 
                                    border-radius: 8px;
                                    border-left: 4px solid #6c757d;
                                    font-size: 14px;'>
                            {i}. {thought}
                        </div>
                        """, unsafe_allow_html=True)
    
    # Store the message in session state
    msg = {
        "content": content,
        "is_user": is_user,
        "is_ai": is_ai,
        "is_tool_call": is_tool_call,
        "is_tool_result": is_tool_result
    }
    
    if thoughts and len(thoughts) > 0:
        msg["thoughts"] = thoughts
        
    st.session_state.messages.append(msg)

def format_tool_call(step_num, tool_name, tool_input):
    return f"""
    <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #4CAF50;">
        <div><strong>🔧 Step {step_num} - {tool_name}</strong></div>
        <div style="margin-top: 5px;">
            <details>
                <summary>Show Input</summary>
                <pre style="white-space: pre-wrap; background: white; padding: 10px; border-radius: 4px; margin: 5px 0 0 0;">
{json.dumps(tool_input, indent=2, default=str)}
                </pre>
            </details>
        </div>
    </div>
    """

def format_tool_result(step_num, result):
    if not result:
        return ""
    
    try:
        if isinstance(result, str):
            formatted_result = json.dumps(json.loads(result), indent=2)
        else:
            formatted_result = json.dumps(result, indent=2, default=str)
        
        return f"""
        <div style="margin: 10px 0 20px 20px; padding: 10px; background: #e9f7fe; border-radius: 5px; border-left: 4px solid #2196F3;">
            <div><strong>📊 Step {step_num} - Result</strong></div>
            <div style="margin-top: 5px;">
                <pre style="white-space: pre-wrap; background: white; padding: 10px; border-radius: 4px; margin: 5px 0 0 0;">
{formatted_result}
                </pre>
            </div>
        </div>
        """
    except:
        return f"""
        <div style="margin: 10px 0 20px 20px; padding: 10px; background: #e9f7fe; border-radius: 5px; border-left: 4px solid #2196F3;">
            <div><strong>📊 Step {step_num} - Result</strong></div>
            <div style="margin-top: 5px;">
                <pre style="white-space: pre-wrap; background: white; padding: 10px; border-radius: 4px; margin: 5px 0 0 0;">
[Content processed but not displayed]
                </pre>
            </div>
        </div>
        """

def process_query(query):
    """Process the user query using the Trino agent."""
    try:
        # Display user message
        display_message(query, is_user=True)
        
        # Ensure agent is initialized
        if not hasattr(st.session_state.agent, 'agent_executor') or st.session_state.agent.agent_executor is None:
            st.session_state.agent._initialize()
        
        # Enable verbose output to capture thought process in console
        st.session_state.agent.agent_executor.verbose = True
        
        # Enable debug logging for LangChain components
        import logging
        logging.basicConfig(level=logging.INFO)
        logging.getLogger('langchain').setLevel(logging.DEBUG)
        logging.getLogger('langchain.agents').setLevel(logging.DEBUG)
        logging.getLogger('langchain.tools').setLevel(logging.DEBUG)
        
        # Initialize thoughts list at the start
        thoughts = []
        debug_content = ""
        
        # Process the query through the agent
        try:
            # Get the raw output to access intermediate steps
            result = st.session_state.agent.agent_executor({"input": query}, include_run_info=True)
            
            # Prepare the debug content
            debug_content = ""
            
            # Process intermediate steps if available
            if 'intermediate_steps' in result and result['intermediate_steps']:
                print(f"Found {len(result['intermediate_steps'])} intermediate steps")
                
                for i, step in enumerate(result['intermediate_steps']):
                    if len(step) >= 2:
                        action = step[0]
                        observation = step[1]
                        
                        # Process the agent's thought process
                        if hasattr(action, 'log') and action.log:
                            thought = action.log.strip()
                            if thought:
                                clean_thought = thought.replace('```', '').strip()
                                thought_msg = f"🤔 {clean_thought}"
                                thoughts.append(thought_msg)
                                print(f"Added thought: {thought_msg}")
                        
                        # Process tool calls and results
                        if hasattr(action, 'tool'):
                            tool_name = action.tool
                            tool_input = action.tool_input
                            
                            # Skip certain tools from debug output
                            if tool_name not in ['list_tables', 'describe_table']:
                                debug_content += format_tool_call(i+1, tool_name, tool_input)
                                debug_content += format_tool_result(i+1, observation)
                            
                            # Add tool info to thoughts
                            tool_thought = f"🔧 Used tool: {tool_name}"
                            if tool_input:
                                input_str = json.dumps(tool_input, default=str)
                                tool_thought += f" with input: {input_str[:100]}"
                                if len(input_str) > 100:
                                    tool_thought += "..."
                            thoughts.append(tool_thought)
                            print(f"Added tool thought: {tool_thought}")
            else:
                print("No intermediate steps found in result")
            
            # Display the final response with thoughts
            if 'output' in result:
                # First display the final answer
                with st.chat_message("assistant"):
                    st.markdown(result['output'])
                    
                    # Add thoughts if available
                    if thoughts:
                        with st.expander("Show Thought Process", expanded=False):
                            st.markdown("### 🤔 Thought Process")
                            for i, thought in enumerate(thoughts, 1):
                                st.markdown(f"""
                                <div style='margin: 10px 0; padding: 12px; 
                                            background: #f8f9fa; 
                                            border-radius: 8px;
                                            border-left: 4px solid #6c757d;
                                            font-size: 14px;'>
                                    {i}. {thought}
                                </div>
                                """, unsafe_allow_html=True)
                
                # Store the message in session state
                st.session_state.messages.append({
                    'content': result['output'],
                    'is_ai': True,
                    'thoughts': thoughts
                })
                
                # Debug output
                print(f"Number of thoughts: {len(thoughts)}")
                print(f"Debug content length: {len(debug_content) if debug_content else 0}")
                
                # Force a rerun to update the UI
                st.rerun()
            
            return result.get('output', 'No response generated')
            
        except Exception as e:
            error_msg = f"Error processing query: {str(e)}"
            st.error(error_msg)
            return error_msg
    except Exception as e:
        error_msg = f"Error processing your request: {str(e)}"
        display_message(error_msg, is_ai=True)
        return error_msg

# Main app layout
st.title("🔍 Trino SQL Assistant")
st.markdown("Ask questions about your data in natural language and get SQL-powered answers.")

# Chat interface
st.markdown("### Chat with your data")

# Display chat messages from history
for msg in st.session_state.messages:
    if msg.get('is_user'):
        with st.chat_message("user"):
            st.markdown(msg['content'])
    elif msg.get('is_ai'):
        with st.chat_message("assistant"):
            st.markdown(msg['content'])
            
            # Display thoughts if available
            if 'thoughts' in msg and msg['thoughts']:
                with st.expander("Show Thought Process", expanded=False):
                    st.markdown("### 🤔 Thought Process")
                    for i, thought in enumerate(msg['thoughts'], 1):
                        st.markdown(f"""
                        <div style='margin: 10px 0; padding: 12px; 
                                    background: #f8f9fa; 
                                    border-radius: 8px;
                                    border-left: 4px solid #6c757d;
                                    font-size: 14px;'>
                            {i}. {thought}
                        </div>
                        """, unsafe_allow_html=True)
    elif msg.get('is_tool_call'):
        with st.chat_message("assistant"):
            st.markdown(f'<div class="tool-call">🔧 <strong>Tool Call:</strong><br/>{msg["content"]}</div>', 
                      unsafe_allow_html=True)
    elif msg.get('is_tool_result'):
        with st.chat_message("assistant"):
            st.markdown(f'<div class="tool-result">📊 <strong>Tool Result:</strong><br/>{msg["content"]}</div>', 
                      unsafe_allow_html=True)

# Input area
with st.container():
    user_input = st.chat_input("Ask me anything about your data...")
    submit_button = user_input is not None and user_input.strip() != ""

# Process input when button is clicked or Enter is pressed
if submit_button:
    try:
        st.session_state.thinking = True
        process_query(user_input)
    except Exception as e:
        st.error(f"Error processing query: {str(e)}")
    finally:
        st.session_state.thinking = False
        st.rerun()


# Add footer
st.markdown("---")
st.markdown("*Powered by LangChain, Trino, and Streamlit*")

# Add some JavaScript for better UX
st.markdown("""
    <script>
    // Auto-scroll to bottom of chat
    const scrollToBottom = () => {
        window.scrollTo(0, document.body.scrollHeight);
    };
    
    // Run on page load and after each Streamlit render
    document.addEventListener('DOMContentLoaded', scrollToBottom);
    document.addEventListener('st:render', scrollToBottom);
    </script>
""", unsafe_allow_html=True)
