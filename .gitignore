# Environment variables
.env
.env.local
.env.development
.env.production

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.next/
out/
dist/
build/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/
.coverage
htmlcov/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Docker
.docker/
docker-compose.override.yml

# Database
*.sqlite3
*.db

# Logs
# logs/
# *.log