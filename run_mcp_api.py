#!/usr/bin/env python3
"""
MCP API Server Runner

Starts the MCP API server for testing MCP integration.
"""

import argparse
import logging
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.ai_gateway.mcp_api import run_mcp_api_server

def main():
    parser = argparse.ArgumentParser(description="Run MCP API Server")
    parser.add_argument(
        "--config", 
        type=str, 
        default=".stitch/mcp.json",
        help="Path to MCP configuration file"
    )
    parser.add_argument(
        "--host", 
        type=str, 
        default="0.0.0.0",
        help="Host to bind to"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8080,
        help="Port to bind to"
    )
    parser.add_argument(
        "--log-level", 
        type=str, 
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Log level"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"Starting MCP API Server...")
    logger.info(f"Config: {args.config}")
    logger.info(f"Host: {args.host}")
    logger.info(f"Port: {args.port}")
    
    # Check if config file exists
    config_path = Path(args.config)
    if not config_path.exists():
        logger.error(f"Configuration file not found: {args.config}")
        sys.exit(1)
    
    try:
        run_mcp_api_server(args.config, args.host, args.port)
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
