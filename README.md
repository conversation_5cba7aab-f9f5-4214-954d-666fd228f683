# AI-Powered Insights Project

This project creates an autonomous AI-powered insights system that takes natural language questions, understands user requirements, and queries a database to provide meaningful insights.

## Features

- Natural language query processing using AWS Bedrock (Claude3.5Sonnet)
- Autonomous decision-making for query optimization and database interaction
- Simple chat interface for user interaction
- Containerized setup with <PERSON><PERSON> for easy deployment

## Project Structure

```
.
├── docker/# Docker configuration files
├── src/                 # Source code
│   ├── frontend/        # Frontend chat interface
│   ├── backend/         # Backend API and services
│   └── database/        # Database scripts and models
├── docker-compose.yml   # Docker Compose configuration
└── README.md            # Project documentation
```

## Prerequisites

- Docker and Docker Compose
- AWS account with access to Bedrock
- Node.js (for local development)
- Python3.9+ (for local development)

## Setup

1. Clone the repository
2. Copy `.env.example` to `.env` and fill in the required values
3. Run `docker-compose up` to start the application
4. Access the chat interface at `http://localhost:3000`

## Environment Variables

The following environment variables must be set in the `.env` file:

- `AWS_ACCESS_KEY_ID`: Your AWS access key
- `AWS_SECRET_ACCESS_KEY`: Your AWS secret key
- `AWS_REGION`: AWS region (ap-southeast-1)
- `BEDROCK_MODEL_ID`: Claude model ID (anthropic.claude-3-sonnet-********-v1:0)

## Usage

1. Open the chat interface in your browser at `http://localhost:3000`
2. Type your natural language question about data you want to analyze
3. The system will autonomously determine how to query the database and provide insights
4. Review the results and ask follow-up questions

## License

[MIT License](LICENSE)