apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: wren

resources:
  - namespace.yaml
  - backend/configmap.yaml
  - backend/secret.yaml
  - frontend/nginx-configmap.yaml
  - combined-deployment.yaml
  - combined-service.yaml
  - ingress/ingress.yaml

# Uncomment and update these if you're using Kustomize for environment-specific configs
# configMapGenerator:
# - name: insights-backend-config
#   behavior: merge
#   literals:
#     - ENVIRONMENT=production

# secretGenerator:
# - name: insights-backend-secrets
#   behavior: merge
#   literals:
#     - AWS_ACCESS_KEY_ID=your-key
#     - AWS_SECRET_ACCESS_KEY=your-secret

# images:
# - name: your-docker-registry/insights-backend
#   newTag: latest
# - name: your-docker-registry/insights-frontend
#   newTag: latest
