apiVersion: apps/v1
kind: Deployment
metadata:
  name: text-to-insights
  namespace: wren
  labels:
    app: text-to-insights
    tier: fullstack
spec:
  replicas: 1
  selector:
    matchLabels:
      app: text-to-insights
      tier: fullstack
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: text-to-insights
        tier: fullstack
    spec:
      serviceAccountName: data-ai-assistant-sa
      containers:
      # Backend container
      - name: backend
        image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/ai-assistant:mcp-backend-v2.1
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: text-to-insights-backend-config
        - secretRef:
            name: text-to-insights-backend-secrets
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
      
      # Frontend container
      - name: frontend
        image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/ai-assistant:mcp-frontend-v2.1
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d/default.conf
          subPath: default.conf
        # - name: nginx-config
        #   mountPath: /usr/share/nginx/html
        env:
        # - name: REACT_APP_API_URL
        #   value: "/api"
        # - name: REACT_APP_WS_URL
        #   value: "/ws/query"
      volumes:
      - name: nginx-config
        configMap:
          name: text-to-insights-frontend-nginx-config
          items:
          - key: default.conf
            path: default.conf
        resources:
          requests:
            cpu: "50m"
            memory: "128Mi"
          limits:
            cpu: "200m"
            memory: "256Mi"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: nginx-config
        configMap:
          name: text-to-insights-frontend-nginx-config
          items:
          - key: default.conf
            path: default.conf
