apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # AWS ALB Controller Settings
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    
    # Health Check Configuration
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '5'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/success-codes: '200-399'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '2'
    
    # SSL/TLS Configuration
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:ap-southeast-1:183295456051:certificate/a54cf14a-bbc2-4484-b0f1-620b4d351554
    alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS-1-2-2017-01
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/backend-protocol: 'HTTP'
    
    # Redirect HTTP to HTTPS
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    
    # WebSocket Configuration
    alb.ingress.kubernetes.io/target-group-attributes: 'stickiness.enabled=true,stickiness.lb_cookie.duration_seconds=3600'
    alb.ingress.kubernetes.io/conditions.ws: |
      [{
        "field":"http-header",
        "httpHeaderConfig": {
          "httpHeaderName": "Upgrade",
          "values":["websocket"]
        }
      }]
    
    # Security Headers
    alb.ingress.kubernetes.io/response-timeout: '60'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=60
    
    # Network Configuration
    alb.ingress.kubernetes.io/security-groups: sg-001ab57485bc7892b
    alb.ingress.kubernetes.io/subnets: subnet-09a51196787a38025,subnet-02b97d432cfb361a2,subnet-06b312b17ae4764a3
    
    # Tags
    alb.ingress.kubernetes.io/tags: Environment=prod,Service=text-to-insights
  finalizers:
  - ingress.k8s.aws/resources
  name: text-to-insights
  namespace: wren
spec:
  ingressClassName: alb
  rules:
  - host: data-ai-assistant.analytics.blinkit.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: text-to-insights
            port:
              number: 80
        
      # Add this if you need WebSocket support
      - path: /ws
        pathType: Prefix
        backend:
          service:
            name: text-to-insights
            port:
              number: 80
        
      # Add this if you have an API service
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: text-to-insights
            port:
              number: 8000
