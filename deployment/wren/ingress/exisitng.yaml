apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:ap-southeast-1:183295456051:certificate/a54cf14a-bbc2-4484-b0f1-620b4d351554
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: "30"
    alb.ingress.kubernetes.io/healthcheck-path: /_stcore/health
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: "5"
    alb.ingress.kubernetes.io/healthy-threshold-count: "2"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/security-groups: sg-001ab57485bc7892b
    alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-2016-08
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/subnets: subnet-09a51196787a38025,subnet-02b97d432cfb361a2,subnet-06b312b17ae4764a3
    alb.ingress.kubernetes.io/tags: Environment=prod,Service=data-ai-assistant
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/unhealthy-threshold-count: "2"
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"networking.k8s.io/v1","kind":"Ingress","metadata":{"annotations":{"alb.ingress.kubernetes.io/certificate-arn":"arn:aws:acm:ap-southeast-1:183295456051:certificate/a54cf14a-bbc2-4484-b0f1-620b4d351554","alb.ingress.kubernetes.io/healthcheck-interval-seconds":"30","alb.ingress.kubernetes.io/healthcheck-path":"/_stcore/health","alb.ingress.kubernetes.io/healthcheck-protocol":"HTTP","alb.ingress.kubernetes.io/healthcheck-timeout-seconds":"5","alb.ingress.kubernetes.io/healthy-threshold-count":"2","alb.ingress.kubernetes.io/listen-ports":"[{\"HTTP\": 80}, {\"HTTPS\": 443}]","alb.ingress.kubernetes.io/scheme":"internal","alb.ingress.kubernetes.io/security-groups":"sg-001ab57485bc7892b","alb.ingress.kubernetes.io/ssl-policy":"ELBSecurityPolicy-2016-08","alb.ingress.kubernetes.io/ssl-redirect":"443","alb.ingress.kubernetes.io/subnets":"subnet-09a51196787a38025,subnet-02b97d432cfb361a2,subnet-06b312b17ae4764a3","alb.ingress.kubernetes.io/tags":"Environment=prod,Service=data-ai-assistant","alb.ingress.kubernetes.io/target-type":"ip","alb.ingress.kubernetes.io/unhealthy-threshold-count":"2"},"name":"data-ai-assistant","namespace":"data-tools"},"spec":{"ingressClassName":"alb","rules":[{"host":"data-ai-assistant.analytics.blinkit.dev","http":{"paths":[{"backend":{"service":{"name":"data-ai-assistant-svc","port":{"number":8501}}},"path":"/","pathType":"Prefix"}]}}]}}
  creationTimestamp: "2025-06-17T14:56:23Z"
  finalizers:
  - ingress.k8s.aws/resources
  generation: 2
  name: data-ai-assistant
  namespace: data-tools
  resourceVersion: "183736966"
  uid: f81eb543-02d6-46d5-9b1b-5d4da3fbe47f
spec:
  ingressClassName: alb
  rules:
  - host: data-ai-assistant.analytics.blinkit.dev
    http:
      paths:
      - backend:
          service:
            name: data-ai-assistant-svc
            port:
              number: 8501
        path: /
        pathType: Prefix
status:
  loadBalancer:
    ingress:
    - hostname: internal-k8s-datatool-dataaias-0a2bee2840-**********.ap-southeast-1.elb.amazonaws.com
