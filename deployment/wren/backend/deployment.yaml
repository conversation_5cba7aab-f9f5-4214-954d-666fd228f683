apiVersion: apps/v1
kind: Deployment
metadata:
  name: text-to-insights-backend
  namespace: wren
  labels:
    app: text-to-insights
    tier: backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: text-to-insights
      tier: backend
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: text-to-insights
        tier: backend
    spec:
      serviceAccountName: data-ai-assistant-sa
      containers:
      - name: text-to-insights-backend
        image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/ai-assistant:mcp-backend
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: text-to-insights-backend-config
        - secretRef:
            name: text-to-insights-backend-secrets
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        env:
        - name: PYTHONUNBUFFERED
          value: "1"
