apiVersion: v1
kind: ConfigMap
metadata:
  name: text-to-insights-frontend-nginx-config
  namespace: wren
data:
  default.conf: |
    server {
        listen 3000;
        
        # Health check endpoint
        location = /health {
            access_log off;
            add_header Content-Type text/plain;
            return 200 'OK';
        }
        
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }
        
        location /api/ {
            proxy_pass http://text-to-insights:8000/api/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }
        
        location /ws/ {
            proxy_pass http://text-to-insights:8000/ws/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_read_timeout 86400;  # 24 hours
            proxy_send_timeout 86400;  # 24 hours
            proxy_connect_timeout 300;  # 5 minutes
            proxy_buffer_size 16k;
            proxy_buffers 4 16k;
            proxy_busy_buffers_size 32k;
        }
    }
