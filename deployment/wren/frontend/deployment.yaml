apiVersion: apps/v1
kind: Deployment
metadata:
  name: text-to-insights-frontend
  namespace: wren
  labels:
    app: text-to-insights
    tier: frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: text-to-insights
      tier: frontend
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: text-to-insights
        tier: frontend
    spec:
      containers:
      - name: text-to-insights-frontend
        image: 183295456051.dkr.ecr.ap-southeast-1.amazonaws.com/data/ai-assistant:mcp-frontend
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d/default.conf
          subPath: default.conf
        env:
        - name: REACT_APP_API_URL
          value: "/api"
        - name: REACT_APP_WS_URL
          value: "ws://text-to-insights-backend.wren.svc.cluster.local:8000/ws/query"
        - name: WDS_SOCKET_PORT
          value: "0"  # Disable Webpack Dev Server's socket port
        resources:
          requests:
            cpu: "50m"
            memory: "128Mi"
          limits:
            cpu: "200m"
            memory: "256Mi"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: nginx-config
        configMap:
          name: text-to-insights-frontend-nginx-config
          items:
          - key: default.conf
            path: default.conf
