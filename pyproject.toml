[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "insights_project"
version = "0.1.0"
description = "Insights Project with LangChain and Trino integration"
authors = [
    { name = "Your Name", email = "<EMAIL>" },
]
requires-python = ">=3.8"
dependencies = [
    "fastapi>=0.68.0",
    "uvicorn>=0.15.0",
    "langchain>=0.0.200",
    "langchain-aws>=0.0.1",
    "sqlalchemy>=1.4.0",
    "python-dotenv>=0.19.0",
    "pydantic>=1.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "black>=21.7b0",
    "isort>=5.9.0",
    "mypy>=0.910",
]

[tool.setuptools]
packages = { find = { where = ["src/backend"] } }
package-dir = { "" = "src/backend" }

[project.scripts]
insights-api = "app.main:app"
