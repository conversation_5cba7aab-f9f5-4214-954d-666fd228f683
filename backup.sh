#!/bin/bash

# Get the current timestamp for the backup file
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/tmp/backup_$TIMESTAMP"
ZIP_FILE="insights_project_$TIMESTAMP.zip"
S3_PATH="s3://blinkit-analytics-test/neelesh/text-to-insights-poc/$ZIP_FILE"
PROJECT_DIR="/Users/<USER>/Documents/repos/dev/insights_project"

echo "Starting backup of insights_project..."

# Create a temporary directory for the backup
mkdir -p "$BACKUP_DIR"

# Define exclude patterns for rsync
RSYNC_EXCLUDES=(
  --exclude='.env'
  --exclude='.git'
  --exclude='__pycache__'
  --exclude='*.pyc'
  --exclude='*.pyo'
  --exclude='*.pyd'
  --exclude='.Python'
  --exclude='env/'
  --exclude='venv/'
  --exclude='.venv/'
  --exclude='.mypy_cache/'
  --exclude='.pytest_cache/'
  --exclude='.tox/'
  --exclude='.eggs/'
  --exclude='*.egg-info/'
  --exclude='.installed.cfg'
  --exclude='.egg'
  --exclude='node_modules/'
  --exclude='.next/'
  --exclude='.nuxt/'
  --exclude='.cache/'
  --exclude='.yarn/'
  --exclude='.yarn-cache/'
  --exclude='.sass-cache/'
  --exclude='.parcel-cache/'
  --exclude='.serverless/'
  --exclude='.serverless_nextjs/'
  --exclude='.vercel/'
  --exclude='.netlify/'
  --exclude='dist/'
  --exclude='build/'
  --exclude='coverage/'
  --exclude='.DS_Store'
  --exclude='*.log'
)

# Copy the project files to the backup directory with exclusions
rsync -av "${RSYNC_EXCLUDES[@]}" "$PROJECT_DIR/" "$BACKUP_DIR/"

# Create a zip file of the backup
echo "Creating zip file..."
cd "$BACKUP_DIR/.."
zip -r "$ZIP_FILE" "$(basename $BACKUP_DIR)"

# Upload the zip file to S3
echo "Uploading to S3..."
aws s3 cp "$ZIP_FILE" "$S3_PATH"

# Clean up temporary files
echo "Cleaning up..."
rm -rf "$BACKUP_DIR"
rm "$ZIP_FILE"

echo "Backup completed successfully: $S3_PATH"

echo "Backup completed successfully!"
echo "Backup file: $S3_PATH"