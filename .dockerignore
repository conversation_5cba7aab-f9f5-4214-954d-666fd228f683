# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Node
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Environment files
.env
.venv
env/
venv/
ENV/
.env.*
!.env.example

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Docker
Dockerfile
docker-compose.yml
docker-compose.*.yml

# Project specific
*.log
logs/
*.sqlite3

# Frontend build
build/
.next/
.nuxt/
.cache/
coverage/

# Backend specific
*.sqlite
*.sqlite3
*.db
*.sql
*.dump

# Local development
.local/
.cache/
