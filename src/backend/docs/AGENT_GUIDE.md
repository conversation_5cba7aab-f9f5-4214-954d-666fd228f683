# LangChain Trino Agent System Guide

Welcome to the LangChain Trino Agent, a powerful natural language interface for querying and analyzing data in your Trino data warehouse. This guide will help you effectively use the agent's capabilities.

## 🚀 Getting Started

1. **Initialize the Agent**: The agent automatically initializes on first use
2. **Use Natural Language**: Ask questions in plain English
3. **Review Results**: The agent provides formatted responses with data and insights

## 🎯 Quick Reference Card

| Task | Command Example |
|------|-----------------|
| List catalogs | "What catalogs are available?" |
| List schemas | "Show schemas in blinkit_iceberg" |
| List tables | "What tables are in dwh?" |
| Describe table | "Describe dwh.customers" |
| Run query | "Show top 10 customers by order value" |
| Get help | "How do I find customer data?" |

## 🔍 Data Discovery

### Explore Your Data
- **List Catalogs**: "What catalogs are available?"
- **Browse Schemas**: "Show me schemas in the 'blinkit_iceberg' catalog"
- **Find Tables**: "What tables are in the 'dwh' schema?"
- **Table Details**: "Describe the 'customers' table in 'dwh'"

### Rich Metadata Access
For any table, you can get:
- Column names and data types
- Nullability constraints
- Default values
- Column descriptions
- Primary/Foreign key relationships
- Partitioning information
- Table statistics

## 🛠️ Available Tools

The agent has access to these specialized tools:

### 1. Data Exploration
- `list_catalogs`: List all available catalogs
  - Input: None required
  - Example: "What data sources do we have?"
  - Response Format: `{"catalogs": ["blinkit_iceberg", "system", "hive"]}`

- `list_schemas`: Show schemas in a catalog
  - Input: Catalog name
  - Example: "Show me schemas in the blinkit_iceberg catalog"

- `list_tables`: List tables in a schema
  - Input: "catalog.schema" format
  - Example: "What tables are in blinkit_iceberg.dwh?"

- `describe_table`: Get table schema
  - Input: "catalog.schema.table" format
  - Example: "Describe blinkit_iceberg.dwh.customers"

### 2. Query Execution
- `execute_sql`: Run custom SQL queries
  - Input: Valid SQL query
  - Features:
    - Automatic result limiting (default 100 rows)
    - Query validation
    - Protection against destructive operations
  - Example: "Show me the top 10 customers by order value"

## 💡 Best Practices

1. **Start Broad**: Begin with general questions to understand the data
2. **Use Natural Language**: The agent understands questions like:
   - "What tables contain customer information?"
   - "Show me the schema for the orders table"
   - "What are the columns in the products table?"
3. **Be Specific**: Include catalog and schema names when possible
4. **Review Data First**: Check table schemas before writing complex queries

## 🔄 Example Workflows

### Basic Exploration
```
User: What catalogs are available?
Agent: Here are the available catalogs: ["blinkit_iceberg", "system"]

User: Show me schemas in blinkit_iceberg
Agent: Found 3 schemas: ["dwh", "staging", "analytics"]

User: What tables are in dwh?
Agent: Tables in blinkit_iceberg.dwh:
- customers (TABLE)
- orders (TABLE)
- products (TABLE)

User: Describe dwh.orders
Agent: Table: blinkit_iceberg.dwh.orders
Columns:
- order_id (bigint, NOT NULL)
- customer_id (bigint, NOT NULL)
- order_date (timestamp)
- total_amount (decimal)
- status (varchar)
```

### Data Analysis
```
User: Show me top 5 customers by total spending
Agent: [Executes: SELECT c.customer_name, SUM(o.total_amount) as total_spent
       FROM dwh.customers c
       JOIN dwh.orders o ON c.customer_id = o.customer_id
       GROUP BY c.customer_name
       ORDER BY total_spent DESC
       LIMIT 5]
```

## ⚡ Performance Tips

1. **Be Specific**: Narrow down your queries with filters
   - ❌ "Show me all orders"
   - ✅ "Show orders from last month"

2. **Use Approximations** when exact counts aren't needed
   - "Approximately how many customers do we have?"

3. **Check Query Plan** for complex queries
   - "Explain the query plan for [your query]"

## ⚠️ Security & Limitations

### Security
- **Read-Only**: The agent prevents destructive operations (DROP, DELETE, TRUNCATE, etc.)
- **Query Validation**: All queries are validated before execution
- **Data Access**: Limited to authorized schemas and tables

### Technical Limits
- **Result Size**: Limited to 1000 rows by default
- **Query Timeout**: 5 minutes per query
- **Concurrency**: 5 concurrent queries per user

## 📊 Response Format

Responses include:
- Formatted tables for better readability
- Query execution time
- Number of rows returned
- Pagination for large result sets
- Error messages with suggestions

Example Response:
```
✅ Query executed successfully (1.24s)
Showing 10 of 1,245 rows

| customer_name | total_orders | total_spent |
|---------------|--------------|-------------|
| John Doe     | 24           | Rs. 12,456.78  |
| Jane Smith   | 18           | Rs. 9,876.54   |

Use LIMIT and OFFSET to navigate through results
```

## 🆘 Getting Help

### Self-Service
1. Check the error message details
2. Try simplifying your question
3. Verify table and column names

### Support
For additional assistance:
- Type `help` for interactive guidance
- Contact <EMAIL> with:
  - The query you were trying to run
  - Any error messages received
  - Expected vs actual results

## 📚 Additional Resources

- [Trino SQL Syntax Guide](https://trino.io/docs/current/sql.html)
- [SQL Best Practices](https://www.sqlstyle.guide/)
- [Data Dictionary](https://your-wiki/data-dictionary) (Internal)

## 🔄 Version Information
- Agent Version: 1.0.0
- Last Updated: 2025-06-30
