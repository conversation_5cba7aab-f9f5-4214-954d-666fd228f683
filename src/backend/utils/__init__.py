"""Utility functions for the application."""

import logging
from typing import Any, Dict, Optional
from pathlib import Path
import json
import yaml

logger = logging.getLogger(__name__)

def load_config(file_path: str) -> Dict[str, Any]:
    """Load configuration from JSON or YAML file.
    
    Args:
        file_path: Path to the config file
        
    Returns:
        Dict containing the configuration
    """
    path = Path(file_path)
    if not path.exists():
        logger.warning(f"Config file not found: {file_path}")
        return {}
    
    try:
        with open(path, 'r') as f:
            if path.suffix.lower() == '.json':
                return json.load(f)
            elif path.suffix.lower() in ('.yaml', '.yml'):
                return yaml.safe_load(f)
            else:
                logger.error(f"Unsupported config file format: {path.suffix}")
                return {}
    except Exception as e:
        logger.error(f"Error loading config file {file_path}: {str(e)}")
        return {}

def ensure_directory_exists(directory: str) -> Path:
    """Ensure that a directory exists, creating it if necessary.
    
    Args:
        directory: Path to the directory
        
    Returns:
        Path object for the directory
    """
    path = Path(directory).expanduser().absolute()
    path.mkdir(parents=True, exist_ok=True)
    return path

def get_logger(name: str = None) -> logging.Logger:
    """Get a logger with the given name.
    
    Args:
        name: Name for the logger (defaults to module name)
        
    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)
