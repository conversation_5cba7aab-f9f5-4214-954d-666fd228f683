"""Main agent module for the application."""

import asyncio
import json
from typing import Dict, Any, AsyncIterator, List, Optional

from .agents import TrinoAgentAdapter
from config import settings
from utils import get_logger

logger = get_logger(__name__)

# Create a global agent instance
_agent = None

async def get_agent() -> TrinoAgentAdapter:
    """Get or create a singleton instance of the Trino agent.
    
    Returns:
        An initialized TrinoAgentAdapter instance
    """
    global _agent
    if _agent is None:
        _agent = TrinoAgentAdapter()
        await _agent.initialize()
    return _agent

async def list_catalogs() -> List[str]:
    """List all available catalogs.
    
    Returns:
        List of catalog names
    """
    agent = await get_agent()
    result = await agent.list_catalogs()
    return result.get('catalogs', [])

async def list_schemas(catalog: str) -> List[str]:
    """List all schemas in a catalog.
    
    Args:
        catalog: The catalog name
        
    Returns:
        List of schema names
    """
    agent = await get_agent()
    result = await agent.list_schemas(catalog)
    return result.get('schemas', [])


async def list_tables(catalog: str, schema: str) -> List[str]:
    """List all tables in a schema.
    
    Args:
        catalog: The catalog name
        schema: The schema name
        
    Returns:
        List of table names
    """
    try:
        agent = await get_agent()
        result = await agent.list_tables(catalog, schema)
        
        if not result.get('success', False):
            error_msg = result.get('error', 'Unknown error')
            logger.error(f"Error listing tables for {catalog}.{schema}: {error_msg}")
            return []
            
        tables = result.get('tables', [])
        if not isinstance(tables, list):
            logger.error(f"Unexpected tables format: {tables}")
            return []
            
        logger.info(f"Found {len(tables)} tables in {catalog}.{schema}")
        return tables
        
    except Exception as e:
        logger.error(f"Unexpected error in list_tables: {str(e)}", exc_info=True)
        return []

async def query(question: str, catalog: str = None, schema: str = None) -> Dict[str, Any]:
    """Execute a query and return the result.
    
    Args:
        question: The natural language question to answer
        catalog: Optional catalog to use for the query
        schema: Optional schema to use for the query
        
    Returns:
        Dictionary containing the query result and metadata
    """
    agent = await get_agent()
    # Set the catalog and schema if provided
    if catalog and schema:
        question = f"Using catalog '{catalog}' and schema '{schema}': {question}"
    return await agent.query(question)

async def stream_query(question: str) -> AsyncIterator[Dict[str, Any]]:
    """Stream the query results as they are generated.
    
    Args:
        question: The natural language question to answer
        
    Yields:
        Dictionary chunks containing partial results
    """
    agent = await get_agent()
    async for chunk in agent.stream_query(question):
        yield chunk

def run_query(question: str) -> Dict[str, Any]:
    """Run a query synchronously and return the result.
    
    This is a convenience function for use in synchronous contexts.
    
    Args:
        question: The natural language question to answer
        
    Returns:
        Dictionary containing the query result and metadata
    """
    return asyncio.run(query(question))

# Clean up on module unload
async def cleanup() -> None:
    """Clean up resources used by the agent."""
    global _agent
    if _agent is not None:
        await _agent.close()
        _agent = None

# Register cleanup for application shutdown
import atexit
atexit.register(lambda: asyncio.run(cleanup()))
