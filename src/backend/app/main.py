from fastapi import <PERSON>AP<PERSON>, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from .utils import get_logger, format_chunk_for_slack
import os
import json
import asyncio
from dotenv import load_dotenv


# Initialize logger
logger = get_logger(__name__)

# Import TrinoAgent adapter
from .agent import TrinoAgentAdapter, run_query, get_agent
from .slack_utils import send_slack_message
from .bedrock.bedrock_service import BedrockMCPOrchestrator
from .bedrock.config import BedrockMCPConfigManager

# Load environment variables
# load_dotenv()

app = FastAPI(title="LangChain Trino Agent API with MCP Integration")

# Initialize Bedrock MCP Orchestrator
bedrock_orchestrator: Optional[BedrockMCPOrchestrator] = None
bedrock_config_manager = BedrockMCPConfigManager()

async def get_bedrock_orchestrator() -> BedrockMCPOrchestrator:
    """Get or initialize the Bedrock MCP orchestrator"""
    global bedrock_orchestrator
    if bedrock_orchestrator is None:
        bedrock_orchestrator = BedrockMCPOrchestrator()
        await bedrock_orchestrator.initialize()
    return bedrock_orchestrator

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

class EmailValidationRequest(BaseModel):
    email: str

class EmailValidationResponse(BaseModel):
    valid: bool
    user_data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class QueryRequest(BaseModel):
    query: str
    catalog: Optional[str] = None
    db_schema: Optional[str] = Field(default=None, alias="schema")

class ChatRequest(BaseModel):
    message: str
    selected_catalog: Optional[str] = None
    selected_schema: Optional[str] = None
    selected_tables: Optional[str] = None
    slack_id: Optional[str] = None

class QueryResponse(BaseModel):
    answer: str
    intermediate_steps: Optional[List[Dict[str, Any]]] = None
    suggested_questions: Optional[List[str]] = None

class WebSocketMessage(BaseModel):
    type: str  # 'query' or 'clear_history'
    query: Optional[str] = None
    catalog: Optional[str] = None
    db_schema: Optional[str] = Field(default=None, alias="schema")
    tables: Optional[List[str]] = None
    session_id: Optional[str] = None
    slack_id: Optional[str] = None

@app.get("/")
async def root():
    return {"message": "Welcome to LangChain Trino Agent API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.post("/api/validate-email", response_model=EmailValidationResponse)
async def validate_email(request: EmailValidationRequest):
    """Validate a user email against the database and return user details if found."""
    try:
        logger.info(f"Validating email: {request.email}")
        
        # Get the Trino agent and ensure it's initialized
        agent = await get_agent()
        await agent.initialize()
        
        # Construct the validation query
        validation_query = f"""
        select
            enterprise_user_id as slack_id,
            id,
            email,
            first_name,
            last_name
        from de_etls.user_slack_info
        where email = '{request.email}'
        and deleted = false
        and is_bot = false
        """
        
        # Execute the query through the agent's trino_agent
        result = await agent.trino_agent.execute_query(validation_query)
        
        if "error" in result:
            logger.error(f"Error validating email: {result['error']}")
            return EmailValidationResponse(
                valid=False,
                error=result["error"]
            )
        
        # Check if any results were returned
        if result.get("results") and len(result["results"]) > 0:
            user_data = result["results"][0]
            logger.info(f"User found: {user_data.get('email')}")
            return EmailValidationResponse(
                valid=True,
                user_data=user_data
            )
        else:
            logger.warning(f"No user found with email: {request.email}")
            return EmailValidationResponse(
                valid=False,
                error="User not found"
            )
            
    except Exception as e:
        logger.error(f"Error validating email: {str(e)}", exc_info=True)
        return EmailValidationResponse(
            valid=False,
            error=f"Error validating email: {str(e)}"
        )

@app.post("/api/chat")
async def chat(request: ChatRequest):
    """Handle chat messages and return responses."""
    try:
        agent = await get_agent()
        response = await agent.query(
            request.message,
            selected_catalog=request.selected_catalog,
            selected_schema=request.selected_schema,
            selected_tables=request.selected_tables,
            slack_id=request.slack_id
        )
        return response
    except Exception as e:
        logger.error(f"Error processing chat message: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/catalogs")
async def get_catalogs():
    """Get list of all catalogs"""
    try:
        agent = await get_agent()
        catalogs = await agent.list_catalogs()
        # Return the list directly as it's already in the correct format
        return catalogs
    except Exception as e:
        logger.error(f"Error getting catalogs: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/schemas")
async def get_schemas(catalog: str = Query(..., description="Catalog name")):
    """Get list of schemas for a catalog"""
    try:
        agent = await get_agent()
        schemas = await agent.list_schemas(catalog)
        # Return the list directly as it's already in the correct format
        return schemas
    except Exception as e:
        logger.error(f"Error getting schemas for catalog {catalog}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/tables")
async def get_tables(
    catalog: str = Query(..., description="Catalog name"),
    schema: str = Query(..., description="Schema name")
):
    """Get list of tables in a schema"""
    try:
        agent = await get_agent()
        result = await agent.list_tables(catalog, schema)
        
        # Return just the list of tables as expected by the frontend
        if result.get('success', False):
            return result.get('tables', [])
        else:
            error_msg = result.get('error', 'Failed to fetch tables')
            logger.error(f"Error getting tables for {catalog}.{schema}: {error_msg}")
            return []
            
    except Exception as e:
        logger.error(f"Error getting tables for {catalog}.{schema}: {str(e)}", exc_info=True)
        return []

@app.post("/api/query", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    try:
        print(f"Processing query: {request.query} with catalog: {request.catalog}, schema: {request.db_schema}")
        result = run_query(
            question=request.query,
            catalog=request.catalog,
            schema=request.db_schema
        )
        # If the result is a dictionary with answer and steps, return it
        if isinstance(result, dict) and "answer" in result:
            print(f"Query processed successfully with {len(result.get('intermediate_steps', []))} steps")
            return result
        # Otherwise, return just the answer string
        print("Query processed with simple answer (no steps)")
        return {"answer": result, "intermediate_steps": []}
    except Exception as e:
        error_message = str(e)
        print(f"Error processing query: {error_message}")
        
        # Handle timeout errors with a more user-friendly message
        if "timed out" in error_message.lower() or "timeout" in error_message.lower():
            error_message = "Your query is too complex and timed out. Please try a simpler query or break it down into multiple questions."
            # Return a 504 Gateway Timeout status code
            raise HTTPException(status_code=504, detail=error_message)
        
        # Handle other errors
        raise HTTPException(status_code=500, detail=f"Error processing query: {error_message}")

# Bedrock MCP API Endpoints
class BedrockChatRequest(BaseModel):
    message: str
    conversation_history: Optional[List[Dict[str, Any]]] = None

class BedrockChatResponse(BaseModel):
    answer: str
    tool_calls: List[Dict[str, Any]] = []
    intermediate_steps: List[str] = []
    conversation_history: Optional[List[Dict[str, Any]]] = None

class MCPServerRequest(BaseModel):
    name: str
    url: str
    type: str = "streamable-http"
    description: str = ""

class MCPServerResponse(BaseModel):
    success: bool
    message: str
    server: Optional[Dict[str, Any]] = None

@app.post("/api/bedrock/chat", response_model=BedrockChatResponse)
async def bedrock_chat(request: BedrockChatRequest):
    """Process a natural language query using Bedrock + MCP integration"""
    try:
        orchestrator = await get_bedrock_orchestrator()
        result = await orchestrator.query(
            user_message=request.message,
            conversation_history=request.conversation_history
        )
        
        return BedrockChatResponse(
            answer=result.get("answer", ""),
            tool_calls=result.get("tool_calls", []),
            intermediate_steps=result.get("intermediate_steps", []),
            conversation_history=result.get("conversation_history")
        )
        
    except Exception as e:
        logger.error(f"Error in Bedrock chat: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing Bedrock query: {str(e)}")

@app.get("/api/bedrock/tools")
async def get_bedrock_tools():
    """Get available MCP tools for Bedrock"""
    try:
        orchestrator = await get_bedrock_orchestrator()
        tools = orchestrator.get_available_tools()
        
        return {
            "tools": tools,
            "count": len(tools),
            "servers": list(orchestrator.get_mcp_servers().keys())
        }
        
    except Exception as e:
        logger.error(f"Error getting Bedrock tools: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error retrieving tools: {str(e)}")

@app.get("/api/bedrock/servers")
async def get_mcp_servers():
    """Get configured MCP servers"""
    try:
        bedrock_config_manager.load_config()
        servers = bedrock_config_manager.get_all_servers()
        
        return {
            "servers": [server.to_dict() for server in servers.values()],
            "summary": bedrock_config_manager.get_config_summary()
        }
        
    except Exception as e:
        logger.error(f"Error getting MCP servers: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error retrieving servers: {str(e)}")

@app.post("/api/bedrock/servers", response_model=MCPServerResponse)
async def add_mcp_server(request: MCPServerRequest):
    """Add a new MCP server configuration"""
    try:
        bedrock_config_manager.load_config()
        
        # Validate configuration
        errors = bedrock_config_manager.validate_server_config(
            request.name, request.url, request.type
        )
        
        if errors:
            raise HTTPException(status_code=400, detail=f"Validation errors: {', '.join(errors)}")
        
        # Add server
        success = bedrock_config_manager.add_server(
            name=request.name,
            url=request.url,
            server_type=request.type,
            description=request.description
        )
        
        if not success:
            raise HTTPException(status_code=409, detail=f"Server with name '{request.name}' already exists")
        
        # Save configuration
        bedrock_config_manager.save_config()
        
        # Reload tools in orchestrator
        global bedrock_orchestrator
        if bedrock_orchestrator:
            await bedrock_orchestrator.reload_tools()
        
        server = bedrock_config_manager.get_server(request.name)
        
        return MCPServerResponse(
            success=True,
            message=f"MCP server '{request.name}' added successfully",
            server=server.to_dict() if server else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding MCP server: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error adding server: {str(e)}")

@app.delete("/api/bedrock/servers/{server_name}", response_model=MCPServerResponse)
async def remove_mcp_server(server_name: str):
    """Remove an MCP server configuration"""
    try:
        bedrock_config_manager.load_config()
        
        success = bedrock_config_manager.remove_server(server_name)
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Server '{server_name}' not found")
        
        # Save configuration
        bedrock_config_manager.save_config()
        
        # Reload tools in orchestrator
        global bedrock_orchestrator
        if bedrock_orchestrator:
            await bedrock_orchestrator.reload_tools()
        
        return MCPServerResponse(
            success=True,
            message=f"MCP server '{server_name}' removed successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing MCP server: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error removing server: {str(e)}")

@app.post("/api/bedrock/servers/{server_name}/reload")
async def reload_mcp_server_tools(server_name: str):
    """Reload tools from a specific MCP server"""
    try:
        orchestrator = await get_bedrock_orchestrator()
        
        # Check if server exists
        servers = orchestrator.get_mcp_servers()
        if server_name not in servers:
            raise HTTPException(status_code=404, detail=f"Server '{server_name}' not found")
        
        # Reload all tools (for now, we reload all since individual server reload needs more complex logic)
        await orchestrator.reload_tools()
        
        return {
            "success": True,
            "message": f"Tools reloaded for server '{server_name}'",
            "total_tools": len(orchestrator.get_available_tools())
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reloading server tools: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error reloading tools: {str(e)}")

# WebSocket endpoint for streaming Bedrock responses
@app.websocket("/ws/bedrock")
async def websocket_bedrock_stream(websocket: WebSocket):
    """WebSocket endpoint for streaming Bedrock responses"""
    await websocket.accept()
    
    try:
        orchestrator = await get_bedrock_orchestrator()
        
        while True:
            # Wait for messages from the client
            data = await websocket.receive_text()
            
            try:
                request_data = json.loads(data)
                message = request_data.get("message", "")
                conversation_history = request_data.get("conversation_history")
                
                if not message:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "error": "Message is required"
                    }))
                    continue
                
                # Stream the response
                async for chunk in orchestrator.stream_query(message, conversation_history):
                    await websocket.send_text(chunk)
                
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "error": "Invalid JSON format"
                }))
            except Exception as e:
                logger.error(f"Error processing Bedrock stream message: {str(e)}", exc_info=True)
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "error": f"Error processing request: {str(e)}"
                }))
                
    except WebSocketDisconnect:
        logger.info("Bedrock WebSocket client disconnected")
    except Exception as e:
        logger.error(f"Bedrock WebSocket error: {str(e)}", exc_info=True)

# WebSocket endpoint for streaming responses
@app.websocket("/ws/query")
async def websocket_query(websocket: WebSocket):
    await websocket.accept()
    
    try:
        # Initialize our agent adapter
        agent_adapter = TrinoAgentAdapter(streaming=True)
        
        while True:
            # Wait for messages from the client
            data = await websocket.receive_text()
            
            try:
                query_data = json.loads(data)
                message = WebSocketMessage(**query_data)
                
                # Handle clear history request
                if message.type == 'clear_history':
                    if message.session_id:
                        cleared = await agent_adapter.clear_conversation(message.session_id)
                        await websocket.send_text(json.dumps({
                            "type": "history_cleared",
                            "session_id": message.session_id,
                            "success": cleared
                        }))
                    continue
                
                # Handle query request
                if message.type != 'query' or not message.query:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "error": "Invalid message type or missing query",
                        "session_id": message.session_id or "unknown"
                    }))
                    continue

                # slack_channel = os.getenv("NOTIF_SLACK_CHANNEL", "neelesh-personal")
                # response = send_slack_message(channel=slack_channel, text=f"<@{message.slack_id}> asked a question\n```{message.query}```", source_info=False)
                # thread_ts = response[0].get("ts", None)
    
                # Process the query with streaming and pass catalog/schema/session/slack_id
                async for chunk in agent_adapter.stream_query(
                    question=message.query,
                    selected_catalog=message.catalog,
                    selected_schema=message.db_schema,
                    selected_tables=message.tables,
                    session_id=message.session_id
                ):
                    # The chunk is already a JSON string, so we send it as text
                    await websocket.send_text(chunk)

                    # send_slack_message(channel=slack_channel, text=f"```{format_chunk_for_slack(chunk)}```", source_info=False, thread_ts=thread_ts)
                
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "error": "Invalid JSON format",
                    "session_id": "unknown"
                }))
            except Exception as e:
                logger.error(f"Error processing message: {str(e)}", exc_info=True)
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "error": f"Error processing request: {str(e)}",
                    "session_id": message.session_id if 'message' in locals() else "unknown"
                }))
                
    except WebSocketDisconnect:
        logger.info("Client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}", exc_info=True)
        if websocket.client_state.value == 1:  # CONNECTED
            await websocket.send_text(json.dumps({
                "type": "error",
                "error": f"WebSocket error: {str(e)}",
                "session_id": message.session_id if 'message' in locals() else "unknown"
            }))


# Mount static files for the frontend
static_dir = os.path.join(os.path.dirname(__file__), "..", "static")
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")

# Add MCP health check to main app
@app.get("/api/health")
async def api_health():
    """Combined health check for all services"""
    return {
        "status": "healthy",
        "services": {
            "trino_agent": "running",
            "mcp_service": "running"
        },
        "timestamp": "2025-01-08T00:00:00Z"
    }

# Serve the MCP chat interface
@app.get("/mcp")
async def mcp_interface():
    """Redirect to MCP chat interface"""
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/static/mcp_chat.html")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
    # response = send_slack_message(channel="neelesh-personal", text="Hello World", source_info=False)
    # print(response[0].get("ts", None))