"""Utility functions for the application."""

import logging
import sys
from typing import Any
import json

from .config import settings

def get_logger(name: str) -> logging.Logger:
    """Get a logger with the given name.Args:
        name: The name of the logger
    Returns:
        A configured logger instance
    """
    logger = logging.getLogger(name)
    # Set log level from configuration
    log_level = getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO)
    logger.setLevel(log_level)
    
    # Create a console handler if none exists
    if not logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        handler.setLevel(log_level)
        
        # Create a formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        
        # Add the handler to the logger
        logger.addHandler(handler)
    
    return logger

def format_chunk_for_slack(chunk):
    try:
        # Parse the JSON string if it's a string
        if isinstance(chunk, str):
            data = json.loads(chunk)
        else:
            data = chunk
        
        # Pretty print with indentation
        formatted = json.dumps(data, indent=2)
        
        # Return as a code block
        return f"```json\n{formatted}\n```"
    except (json.JSONDecodeError, TypeError) as e:
        # If there's an error, return the original chunk in a code block
        return f"```\n{chunk}\n```"