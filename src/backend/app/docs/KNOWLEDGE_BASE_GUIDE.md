# Knowledge Base Guide

Welcome to the Knowledge Base! This guide provides information on how to use the knowledge base to find relevant tables and information in the data warehouse.

## 🎯 Quick Reference

The Knowledge Base helps you discover and understand database tables using natural language. It can help you:

- Find tables relevant to your analysis
- Understand table structures and relationships
- Discover available data across different domains

## 🔍 Using the Knowledge Base

### Finding Relevant Tables

Ask natural language questions to find relevant tables:

```
Find tables related to customer data
Show me tables with sales information
What tables contain user activity data?
```

### Understanding Table Contents

When you find a table, you'll see:
- Table name
- Description of what the table contains
- Complete list of columns
- Relevance score (0-1) indicating match quality

### Example Queries

#### E-commerce Analytics
```
Find tables about customer purchases
Show me tables with product catalog information
What tables track user behavior on the website?
```

#### User Analytics
```
Find tables with user registration data
Show me tables that track user sessions
What tables contain user demographic information?
```

#### Business Metrics
```
Find tables with revenue data
Show me tables with inventory information
What tables contain marketing campaign data?
```

## 📊 Best Practices

1. **Be Specific** - More specific queries yield better results
   - ❌ "Show me data about users"
   - ✅ "Show me tables with user login activity"

2. **Use Domain Terms** - The knowledge base understands business and technical terms
   - ❌ "Where is the money info?"
   - ✅ "Find tables with financial transactions"

3. **Check Column Lists** - Always review the column list to understand what data is available

## 🔄 Integration with Other Tools

The knowledge base works alongside other tools:
- Use `list_tables` to see all tables in a schema
- Use `describe_table` to get detailed schema information
- Use `execute_sql` to query the tables you find

## Need Help?

If you can't find what you're looking for, try:
- Using different keywords
- Being more specific about the data you need
- Checking related schemas or domains
- Asking for help with a specific analysis goal
