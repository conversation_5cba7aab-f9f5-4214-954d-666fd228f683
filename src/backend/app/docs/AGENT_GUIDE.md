# Trino Agent Guide

Welcome to the Trino Agent! This guide provides information on how to interact with the Trino database using natural language queries.

## 🎯 Quick Reference Card

### Available Commands

The Trino Agent can help you with:

1. **Data Discovery**
   - List available catalogs
   - List schemas in a catalog
   - List tables in a schema
   - Describe table structure

2. **Data Analysis**
   - Execute SQL queries
   - Analyze data patterns
   - Calculate statistics
   - Find insights in your data

### Example Questions

You can ask questions like:

- "What tables are available in the database?"
- "Show me the schema of the customers table"
- "Get me the top 5 orders by value"
- "What's the average purchase amount by region?"
- "How many orders were placed last month?"
- "Show the distribution of customer types"

## 🔍 Data Discovery

### Listing Catalogs

To see available catalogs:
```
What catalogs are available?
Show me all catalogs
List available catalogs
```

### Listing Schemas

To see schemas in a catalog:
```
What schemas are in the blinkit_iceberg catalog?
Show schemas in blinkit_iceberg
List all schemas in blinkit_iceberg
```

### Listing Tables

To see tables in a schema:
```
What tables are in blinkit_iceberg.dwh?
Show me tables in dwh schema
List all tables in blinkit_iceberg.dwh
```

### Describing Tables

To see a table's structure:
```
Describe the customers table
What columns are in the orders table?
Show me the schema of blinkit_iceberg.dwh.customers
```

## 📊 Data Analysis

### Running Simple Queries

To get data from tables:
```
Get the first 10 rows from customers
Show me customer_id, name, and email from the customers table
```

### Filtering Data

To filter data:
```
Find orders with amount greater than 100
Show customers from New York
Get transactions from the last 7 days
```

### Aggregating Data

To calculate statistics:
```
What's the total revenue by region?
Calculate average order value by product category
Count orders by status
```

### Joining Tables

To connect data from multiple tables:
```
Get customer names with their orders
Show products and their categories
Find orders with their corresponding customer information
```

## 🚫 Limitations

The Trino Agent has some limitations:

1. It cannot execute data modification operations (INSERT, UPDATE, DELETE)
2. Complex analytical questions might need to be broken down into simpler steps
3. Performance may be limited for very large result sets
4. Custom user-defined functions might not be accessible

## 🆘 Getting Help

For any issues, please contact the system administrator or refer to the Trino documentation.