(AgentAction(tool='list_tables', tool_input='', log='To answer this question, I\'ll need to query the fact_sales_order_details table. First, let\'s check if this table exists and examine its structure.

Action:
```
{
  "action": "list_tables",
  "action_input": ""
}
```

'), '{
  "tables": [
    "absolute_hourly_inventory_availability_fnv",
    "absolute_hourly_inventory_availability_fnv_log",
    "absolute_hourly_inventory_availability_packaged_goods",
    "absolute_hourly_inventory_availability_packaged_goods_log",
    "absolute_hourly_inventory_availability_packaged_goods_old",
    "absolute_hourly_inventory_availability_perishables",
    "absolute_hourly_inventory_availability_perishables_log",
    "agg_city_l1_cat_product_atc_impressions",
    "agg_city_l1_cat_product_atc_impressions2",
    "agg_city_l1_cat_product_atc_impressions3",
    "agg_city_l1_cat_product_atc_impressions4",
    "agg_city_l1_cat_product_atc_impressions5",
    "agg_city_l1_cat_product_atc_impressions6",
    "agg_daily_brand_item_product_sales",
    "agg_daily_cart_bifurcation_metrics",
    "agg_daily_cart_bifurcation_metrics_bckfl",
    "agg_daily_city_conversion",
    "agg_daily_city_conversion_backfill",
    "agg_daily_city_conversion_block_bckfl",
    "agg_daily_city_item_availability_drop_rca",
    "agg_daily_city_item_overall_availability_drop",
    "agg_daily_city_l0_cat_conversion",
    "agg_daily_city_l0_cat_conversion_backup_20241220",
    "agg_daily_city_l0_cat_search_conversion",
    "agg_daily_city_l0_cat_search_conversion_backfill",
    "agg_daily_city_ptype_availability",
    "agg_daily_city_ptype_availability_backfill",
    "agg_daily_city_ptype_conversion",
    "agg_daily_city_ptype_search_conversion",
    "agg_daily_city_ptype_search_conversion_backfill",
    "agg_daily_city_ptype_search_conversion_extended_temp",
    "agg_daily_city_ptype_search_dau_contribution",
    "agg_daily_city_ptype_search_dau_contribution_backfill",
    "agg_daily_city_ptype_user_type_search_conversion",
    "agg_daily_cn_dn",
    "agg_daily_cn_dn_v2",
    "agg_daily_consumer_conversion_backup_20240301_20240331",
    "agg_daily_consumer_conversion_details",
    "agg_daily_consumer_conversion_details_dd",
    "agg_daily_consumer_conversion_details_jbf",
    "agg_daily_consumer_conversion_details_jumbo",
    "agg_daily_diff_loss",
    "agg_daily_dslo_city_conversion",
    "agg_daily_dslo_merchant_city_conversion",
    "agg_daily_dslo_metrics",
    "agg_daily_dslo_metrics_bckfl",
    "agg_daily_entity_payout_breakup",
    "agg_daily_entity_payout_breakup_delivery",
    "agg_daily_entity_payout_breakup_recovery",
    "agg_daily_holi_inventory_mapping",
    "agg_daily_homepage_user_event",
    "agg_daily_homepage_user_event_impressions_l0",
    "agg_daily_homepage_user_event_impressions_l1",
    "agg_daily_homepage_user_event_ptype",
    "agg_daily_homepage_user_event_ptype_impressions",
    "agg_daily_homepage_user_impressions_manufacturer_brand",
    "agg_daily_item_availability_drop_rca",
    "agg_daily_lost_order_metrics",
    "agg_daily_lost_order_metrics_bckup_old_logic",
    "agg_daily_marketing_cost",
    "agg_daily_merchant_city_conversion",
    "agg_daily_merchant_city_conversion_backfill",
    "agg_daily_merchant_city_conversion_block_bckfl",
    "agg_daily_merchant_item_availability_drop_rca",
    "agg_daily_merchant_item_overall_availability_drop",
    "agg_daily_merchant_ptype_availability",
    "agg_daily_merchant_ptype_availability_backfill",
    "agg_daily_merchant_ptype_conversion",
    "agg_daily_merchant_ptype_oos_impressions",
    "agg_daily_merchant_ptype_search_conv_backup",
    "agg_daily_merchant_ptype_search_conversion",
    "agg_daily_merchant_ptype_search_conversion_backfill",
    "agg_daily_merchant_ptype_search_dau_contribution",
    "agg_daily_merchant_ptype_search_dau_contribution_backfill",
    "agg_daily_other_additions",
    "agg_daily_other_additions_v2",
    "agg_daily_outlet_combined_promo_cost",
    "agg_daily_outlet_customer_support_cost",
    "agg_daily_outlet_fleet_cost",
    "agg_daily_outlet_promo_cost",
    "agg_daily_page_asset_ptype_consumer_conversion_details",
    "agg_daily_pg_charges_data",
    "agg_daily_poi_conversion_metrics",
    "agg_daily_post_ebo_cost",
    "agg_daily_ptype_avg_availabilty_oos",
    "agg_daily_ptype_avg_availabilty_oos_v2",
    "agg_daily_search_city_conversion",
    "agg_daily_search_keyword_conversion_metrics",
    "agg_daily_search_keyword_conversion_metrics_at_hex",
    "agg_daily_search_keyword_conversion_metrics_backfill_7d",
    "agg_daily_search_keyword_conversion_metrics_qty_atc_gmv_update",
    "agg_daily_search_keyword_conversion_metrics_test_new_ver",
    "agg_daily_search_keyword_conversion_metrics_test_null",
    "agg_daily_search_keyword_conversion_metrics_v3",
    "agg_daily_search_merchant_city_conversion",
    "agg_daily_seller_payout_delivery_v2",
    "agg_daily_seller_payout_recovery_v2",
    "agg_daily_seller_sales_summary",
    "agg_daily_seller_sales_summary_vstg",
    "agg_daily_store_bifurcation_metrics",
    "agg_daily_store_bifurcation_metrics_bckfl",
    "agg_daily_user_type_search_keyword_conversion_metrics",
    "agg_hourly_backend_outlet_item_inventory",
    "agg_hourly_backend_outlet_item_inventory_v2",
    "agg_hourly_city_conversion",
    "agg_hourly_city_conversion_backfill",
    "agg_hourly_city_conversion_block_bckfl",
    "agg_hourly_city_l0_cat_availability",
    "agg_hourly_city_ptype_availability",
    "agg_hourly_city_ptype_availability_backfill",
    "agg_hourly_city_ptype_availability_backup_20241120",
    "agg_hourly_city_ptype_availability_v2",
    "agg_hourly_city_ptype_conversion",
    "agg_hourly_city_ptype_search_conversion",
    "agg_hourly_city_ptype_search_conversion_extended_temp",
    "agg_hourly_city_ptype_search_dau_contribution",
    "agg_hourly_consumer_conversion_backup_20240301_20240331",
    "agg_hourly_consumer_conversion_details",
    "agg_hourly_consumer_conversion_details_dd",
    "agg_hourly_consumer_conversion_details_jbf",
    "agg_hourly_consumer_conversion_details_jumbo",
    "agg_hourly_dslo_city_conversion",
    "agg_hourly_dslo_merchant_city_conversion",
    "agg_hourly_merchant_city_conversion",
    "agg_hourly_merchant_city_conversion_backfill",
    "agg_hourly_merchant_city_conversion_block_bckfl",
    "agg_hourly_merchant_ptype_availability",
    "agg_hourly_merchant_ptype_availability_backfill",
    "agg_hourly_merchant_ptype_availability_backup_20241120",
    "agg_hourly_merchant_ptype_availability_v2",
    "agg_hourly_merchant_ptype_conversion",
    "agg_hourly_merchant_ptype_oos_impressions",
    "agg_hourly_merchant_ptype_search_conversion",
    "agg_hourly_merchant_ptype_search_dau_contribution",
    "agg_hourly_outlet_item_inventory",
    "agg_hourly_outlet_item_inventory_backfill",
    "agg_hourly_outlet_item_inventory_backfill_temp",
    "agg_hourly_outlet_item_inventory_backup_20241226",
    "agg_hourly_outlet_item_inventory_backup_20241226_temp",
    "agg_hourly_outlet_item_inventory_backup_20241227",
    "agg_hourly_outlet_item_inventory_backup_20241227_temp",
    "agg_hourly_outlet_overall_business_metrics",
    "agg_hourly_outlet_role_instore_employee_login",
    "agg_hourly_search_keyword_conversion_metrics",
    "agg_hourly_search_keyword_conversion_metrics_qty_atc_gmv_updated",
    "agg_kpi_city_zone_split_mtd",
    "agg_kpi_city_zone_split_wtd",
    "agg_mtd_overall_key_business_metrics",
    "agg_product_sales_detail",
    "agg_sales_data",
    "agg_weekly_consumer_conversion_details",
    "agg_weekly_consumer_conversion_details_dd",
    "agg_weekly_consumer_conversion_details_jumbo",
    "agg_wtd_overall_key_business_metrics",
    "backup_agg_daily_consumer_conversion_details_20240223_20240322",
    "backup_agg_hourly_consumer_conversion_details_20240223_20240322",
    "backup_fact_sales_invoice_item_details_2023",
    "backup_fact_sales_order_details_2023",
    "backup_fact_sales_order_item_details_2023",
    "bkp_fact_sales_order_details_20230620_20231231",
    "bkp_fact_sales_order_item_details_wlp_20230620_20231231",
    "bl_calendar_events_holidays",
    "bl_city_state_mapping",
    "bl_table_dependancy_map",
    "bl_trino_query_log",
    "bl_trino_superset_log",
    "bl_trino_superset_role_mapping",
    "business_performance_daily_consumer_metrics",
    "business_performance_daily_consumer_metrics_test",
    "business_performance_daily_consumer_metrics_v2",
    "business_performance_daily_conversion_metrics",
    "business_performance_daily_conversion_metrics_v2",
    "bv_cx_acct_details",
    "bv_cx_properties_daily_group_frequent_changing_attributes_g2",
    "bv_cx_properties_daily_group_new_cx_g1",
    "bv_cx_properties_daily_group_slow_changing_attributes_g3",
    "bv_cx_properties_weekly_group_attributes_g1",
    "cart_bucket_mapping_log",
    "cart_product_sale_details_base",
    "category_superset_redash_access_map",
    "category_table_access_map",
    "cbilling_service_bl_1708_0309_bkp",
    "cbilling_service_pd_1708_0309_bkp",
    "city_hour_weights",
    "city_item_weights",
    "city_mau_30d_rolling_agg",
    "city_merchant_ath_base",
    "city_store_weights",
    "city_weights",
    "city_zone_mapping",
    "current_date_select_test",
    "cx_properties_daily_group_frequent_changing_attributes_g2",
    "cx_properties_daily_group_new_cx_g1",
    "cx_properties_daily_group_slow_changing_attributes_g3",
    "cx_properties_installed_not_transcated",
    "cx_properties_last_seen",
    "cx_properties_weekly_group_slow_changing_attributes_g1",
    "daily_airflow_de_dags_log",
    "daily_bl_pe_zn_bkp",
    "daily_bl_zn_pe_1708_0309_bkp",
    "daily_customer_conv_abs_metrics",
    "daily_customer_conv_abs_metrics_dd",
    "daily_customer_conv_abs_metrics_jbf",
    "daily_customer_conv_abs_metrics_jumbo",
    "daily_customer_conv_funnel_metrics",
    "daily_customer_conv_funnel_metrics_dd",
    "daily_customer_conv_funnel_metrics_jbf",
    "daily_customer_conv_funnel_metrics_jumbo",
    "daily_sales_commision_summary_download_sheet",
    "daily_sales_commision_summary_download_sheet_1708_0309_bkp",
    "daily_sales_commision_summary_download_sheet_test_biz_cat",
    "daily_sales_commision_summary_download_sheet_v2",
    "dark_store_daily_gna_cost",
    "dark_store_daily_rent",
    "dark_store_projects_backup_28jan",
    "dbt_agg_hourly_outlet_role_instore_employee_login",
    "dbt_models_runtime_log",
    "de_dwh_etl_trino_data_warehouse_column_schema",
    "de_dwh_etl_trino_data_warehouse_table_column_schema",
    "dim_assortment_type",
    "dim_city_zone_mapping",
    "dim_customer",
    "dim_customer_address",
    "dim_customer_address_bckup20250626",
    "dim_customer_address_enriched",
    "dim_customer_bckup_20250306",
    "dim_customer_dup_fix_20240306",
    "dim_customer_dup_fix_20240306_clean",
    "dim_date",
    "dim_device",
    "dim_device_backup_v1",
    "dim_device_first_seen_bckup",
    "dim_device_new",
    "dim_device_properties",
    "dim_employee",
    "dim_employee__dbt_tmp",
    "dim_item_pid_offer_mapping",
    "dim_item_pid_offer_mapping_bkp_20240123",
    "dim_item_product_offer_maping",
    "dim_item_product_offer_mapping",
    "dim_item_product_offer_mapping_backup_20240621",
    "dim_item_product_offer_mapping_bkp_20240123",
    "dim_item_product_offer_mapping_v1",
    "dim_item_variant_mapping",
    "dim_item_variant_mapping__dbt_tmp",
    "dim_keywords_l0_mapping",
    "dim_keywords_l0_mapping_backup_20240516",
    "dim_keywords_l1_mapping",
    "dim_keywords_l2_mapping",
    "dim_keywords_ptype_mapping",
    "dim_map_device_user",
    "dim_merchant",
    "dim_merchant_backup_20240215_150000",
    "dim_merchant_bkp_20231219",
    "dim_merchant_fixed",
    "dim_merchant_outlet_facility_mapping",
    "dim_merchant_outlet_facility_mapping_new",
    "dim_merchant_polygon",
    "dim_merchant_polygon_hex_enriched",
    "dim_outlet",
    "dim_product",
    "dim_product_20240916",
    "dim_product_20240926",
    "dim_product_backup_20240516",
    "dim_product_backup_20250127",
    "dim_promo_share_mapping",
    "dim_promo_share_mapping_test",
    "dim_promotion",
    "dim_search_keyword_l0category",
    "dim_search_keyword_l0category_extended_temp",
    "dim_search_keyword_l0category_extended_temp__dbt_tmp",
    "dim_search_keyword_l1category",
    "dim_search_keyword_l2category",
    "dim_search_keyword_ptype",
    "dim_search_keyword_ptype_extended_temp",
    "dim_search_keyword_ptype_tmp",
    "dslo_bucket_mapping_log",
    "dwh_bv_dwh_live_fact_sales_data_t_minus_1_motherday_202505",
    "dwh_diwali_sku_list",
    "e3_store_go_live_planning_live_stores_master",
    "emergency_service_fleet_availability_log",
    "fact_ars_inventory_transfer_details",
    "fact_inventory_transfer_details",
    "fact_inventory_transfer_details_current",
    "fact_invoice_item_details",
    "fact_item_ars_truncation_details",
    "fact_sales_cart_rank_details",
    "fact_sales_hourly_dag_runs_log",
    "fact_sales_invoice_item_details",
    "fact_sales_invoice_item_details_bkp_20231101_2023_12_19",
    "fact_sales_invoice_item_details_temp_430",
    "fact_sales_invoice_item_details_v1",
    "fact_sales_order_details",
    "fact_sales_order_details_backup_20230628",
    "fact_sales_order_details_ext",
    "fact_sales_order_details_rds_bck_202303_202402",
    "fact_sales_order_details_rds_bck_202307_202312",
    "fact_sales_order_item_backup_20240801_20241028",
    "fact_sales_order_item_details",
    "fact_sales_order_item_details_backup_20230628",
    "fact_sales_order_item_details_ext",
    "fact_sales_order_item_details_rds_bck_202303_202402",
    "fact_sales_order_item_details_rds_bck_202307_202312",
    "fact_sales_order_item_details_test",
    "fact_supply_chain_order_details",
    "fact_supply_chain_order_details_1",
    "fact_supply_chain_order_details_backup_202402020",
    "fact_supply_chain_order_details_new",
    "fact_supply_emergency_service_orders",
    "fact_supply_order_partner_details",
    "fact_supply_order_picker_details",
    "fc_to_ds_percentage_share_calculation",
    "festive_hawkeye_block_surge",
    "festive_hawkeye_cart_rank",
    "festive_hawkeye_dslo_mix",
    "festive_hawkeye_keyword_conversion",
    "festive_hawkeye_l0_conversion",
    "festive_hawkeye_l0_sales",
    "festive_hawkeye_merchant_consumer",
    "festive_hawkeye_merchant_conversion",
    "festive_hawkeye_ptype_conversion",
    "festive_hawkeye_ptype_sales",
    "fin_city_superset_redash_access_map",
    "fin_cost_agg_daily_manual_adjustments",
    "fin_cost_daily_outlet_backend_cost",
    "fin_cost_daily_outlet_esto_loss",
    "fin_cost_daily_outlet_store_variable",
    "fin_cost_metrics_daily",
    "fin_daily_outlet_rider_support_cost",
    "fin_etls_daily_pnl_revenue_view_test",
    "fin_fe_be_mapping",
    "fin_revenue_metrics_daily",
    "finance_category_superset_redash_access_map",
    "flat_cancelled_order_item_unused_refunds",
    "flat_cancelled_order_item_unused_refunds_v2",
    "flat_daily_avg_availability",
    "flat_daily_city_conversion_drop",
    "flat_daily_merchant_conversion_drop",
    "flat_daily_merchant_oos_impressions",
    "flat_daily_merchant_search_conversion",
    "flat_daily_overall_conversion_drop",
    "flat_hourly_city_conversion_backfill",
    "flat_hourly_merchant_city_conversion_backfill",
    "flat_hourly_merchant_ptype_oos__dbt_tmp",
    "flat_invoice_consignment_details",
    "flat_invoice_item_b2b_reasons",
    "flat_invoice_item_billed_details",
    "flat_invoice_item_dn_details",
    "flat_invoice_item_grn_details",
    "flat_invoice_item_rsto_details",
    "flat_mv_refresh_test_dump",
    "flat_order_item_refunds",
    "flat_order_item_refunds_bckup_20240411",
    "flat_order_item_refunds_bckup_20240416",
    "flat_order_item_refunds_dbt",
    "flat_order_item_refunds_test",
    "flat_order_item_returns",
    "flat_order_item_returns_backup_20240807",
    "flat_order_item_returns_bckup_20240416",
    "flat_pharmacy_sales_order_state_log",
    "flat_pharmacy_sales_order_state_log_bkp",
    "flat_po_invoice_item_transfer_details_hpl",
    "flat_po_invoice_item_transfer_details_hpl_test",
    "flat_sto_item_details",
    "fnv_city_superset_redash_access_map",
    "homepage_position_wise_user_event",
    "hourly_customer_conv_abs_metrics",
    "hourly_customer_conv_abs_metrics_dd",
    "hourly_customer_conv_abs_metrics_jbf",
    "hourly_customer_conv_abs_metrics_jumbo",
    "hourly_customer_conv_funnel_metrics",
    "hourly_customer_conv_funnel_metrics_dd",
    "hourly_customer_conv_funnel_metrics_jbf",
    "hourly_customer_conv_funnel_metrics_jumbo",
    "hpl_log_order_scan",
    "iceberg_tables_maintenance_alerts_log",
    "ie_dark_store_assets_cctv_h",
    "ie_dark_store_assets_electricals_h",
    "ie_dark_store_assets_separator_and_munchies_h",
    "ie_dark_store_assets_separator_and_munchies_h__dbt_tmp",
    "ie_dark_store_projects_h",
    "imd_discrepancy_type_mapping",
    "imd_flat_invoice_consignment_details__dbt_tmp",
    "imd_flat_invoice_item_b2b_reasons",
    "imd_flat_invoice_item_b2b_reasons_backup_20240515",
    "imd_flat_invoice_item_billed_details_backup_20240515",
    "imd_flat_invoice_item_dn_details__dbt_tmp",
    "imd_flat_invoice_item_dn_details_backup_20240515",
    "imd_flat_invoice_item_grn_details_backup_20240515",
    "imd_flat_invoice_item_rsto_details_backup_20240515",
    "imd_inventory_availability_overall",
    "imd_logistics_3pl_data",
    "imd_mau_overall",
    "imd_weekly_app_open_invalid_cx_id",
    "imd_weekly_app_open_invalid_cx_id_2",
    "imd_weekly_availability",
    "item_variant_mapping",
    "log_order_scan",
    "log_order_scan_new",
    "logs_doorstep_returns_order_details",
    "logs_weighted_landing_price",
    "materialized_refresh_control_base",
    "merchant_level_packaging_cost",
    "metrics_supply_chain_view_board",
    "model_dim_merchant",
    "monthly_business_metrics",
    "monthly_sku_inventory_data",
    "one_time_bv_cx_acct_details",
    "one_time_bv_cx_properties_daily_group_frequent_changing_attributes_g2",
    "one_time_bv_cx_properties_daily_group_new_cx_g1",
    "one_time_bv_cx_properties_daily_group_slow_changing_attributes_g3",
    "one_time_bv_cx_properties_weekly_group_attributes_g1",
    "order_item_wlp_logs",
    "overall_key_business_metrics_mtd",
    "overall_key_business_metrics_wtd",
    "pnl_other_income_visibility",
    "quarterly_business_metrics",
    "scc_daily_outlet_fleet_cost",
    "search_keyword_pids_tmp",
    "sku_inventory_data",
    "sm_and_manager_contact_details",
    "sm_and_manager_contact_details_bkp_20240816",
    "snapshot_daily_product_live_tag_details",
    "sonar_insights_tables_source_lag_check",
    "source_replica_bl_v2",
    "source_replica_cn_dn_v2",
    "source_replica_pd_v2",
    "ss_base_inventory_model_backup",
    "ss_fin_manual_adjustments",
    "store_properties",
    "strategic_partner_hyperpure_cost",
    "superset_redash_access_map",
    "tableau_hourly_consumer_metrics",
    "tableau_hourly_consumer_metrics_h",
    "tableau_hourly_conversion_metrics",
    "tableau_hourly_conversion_metrics_h",
    "tableau_hourly_conversion_metrics_jumbo",
    "tv_dwh_outlet_type",
    "user_properties_daily",
    "user_properties_daily_base",
    "user_properties_daily_base_backup_20240701",
    "user_properties_lifetime_base",
    "user_properties_weekly",
    "user_properties_weekly_base",
    "vw_dim_merchant_backup_20240215_150000",
    "warehouse_superset_redash_access_map",
    "weekly_business_metrics",
    "weekly_customer_conv_abs_metrics",
    "weekly_customer_conv_abs_metrics_dd",
    "weekly_customer_conv_abs_metrics_jumbo",
    "weekly_customer_conv_funnel_metrics",
    "weekly_customer_conv_funnel_metrics_dd",
    "weekly_customer_conv_funnel_metrics_jumbo",
    "weekly_sku_inventory_data"
  ]
}')