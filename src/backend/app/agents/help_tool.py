"""Help tool for the LangChain agent to reference the guide."""
from typing import Optional, Dict, Any
from langchain.tools import BaseTool
from .guide_utils import get_guide

class HelpTool(BaseTool):
    """Tool for providing help and guidance to users."""
    
    name: str = "get_help"
    description: str = """Use this tool when the user asks for help, documentation, or guidance.
    Input should be the user's question or what they need help with.
    Examples:
    - "How do I list tables?"
    - "Show me examples of queries"
    - "What can this agent do?"
    """
    
    def _run(self, query: str) -> str:
        """Use the tool synchronously."""
        guide = get_guide()
        return guide.search_guide(query)
    
    async def _arun(self, query: str) -> str:
        """Use the tool asynchronously."""
        return self._run(query)

def create_help_tool() -> HelpTool:
    """Create and return a configured help tool instance."""
    return HelpTool()
