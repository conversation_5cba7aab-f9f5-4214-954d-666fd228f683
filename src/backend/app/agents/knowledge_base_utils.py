"""Utility functions for knowledge base guide."""
import os
from pathlib import Path
from typing import Dict, List, Optional

from utils import get_logger

logger = get_logger(__name__)

class KnowledgeBaseGuide:
    """Manages the knowledge base guide and documentation."""
    
    def __init__(self, guide_path: Optional[str] = None):
        """Initialize with path to the knowledge base guide markdown file."""
        if not guide_path:
            # Default path if not specified
            base_dir = Path(__file__).parent.parent
            guide_path = base_dir / "docs" / "KNOWLEDGE_BASE_GUIDE.md"
        
        self.guide_path = Path(guide_path)
        self._guide_content = None
    
    def load_guide(self) -> str:
        """Load the guide content from file."""
        if not self._guide_content:
            try:
                with open(self.guide_path, 'r', encoding='utf-8') as f:
                    self._guide_content = f.read()
            except Exception as e:
                logger.error(f"Failed to load knowledge base guide: {str(e)}")
                self._guide_content = "# Knowledge Base Guide\n\nUnable to load the knowledge base guide. Please contact support."
        return self._guide_content
    
    def get_quick_reference(self) -> str:
        """Extract just the quick reference section."""
        content = self.load_guide()
        # Extract content between Quick Reference headers
        start = content.find("## 🎯 Quick Reference")
        end = content.find("## 🔍", start)
        return content[start:end].strip() if start >= 0 else "Quick reference not available."
    
    def search_guide(self, query: str) -> str:
        """Search the guide for relevant sections based on query."""
        content = self.load_guide().lower()
        query = query.lower()
        
        # Simple keyword matching - can be enhanced with more sophisticated search
        if any(term in query for term in ['how to', 'how do i', 'help', 'use']):
            if any(term in query for term in ['find', 'search', 'discover', 'relevant']):
                return self._extract_section("## 🔍 Using the Knowledge Base")
            if any(term in query for term in ['example', 'sample']):
                return self._extract_section("### Example Queries")
            if any(term in query for term in ['best practice', 'tips', 'suggest']):
                return self._extract_section("## 📊 Best Practices")
        
        # Default to quick reference if no specific section matches
        return self.get_quick_reference()
    
    def _extract_section(self, header: str) -> str:
        """Extract a section from the guide by header."""
        content = self.load_guide()
        start = content.find(header)
        if start < 0:
            return f"Section not found: {header}"
            
        # Find the next section header
        next_header = content.find('## ', start + 1)
        if next_header < 0:
            return content[start:].strip()
        return content[start:next_header].strip()

# Singleton instance
_knowledge_base_guide = None

def get_knowledge_base_guide() -> KnowledgeBaseGuide:
    """Get the singleton knowledge base guide instance."""
    global _knowledge_base_guide
    if _knowledge_base_guide is None:
        _knowledge_base_guide = KnowledgeBaseGuide()
    return _knowledge_base_guide
