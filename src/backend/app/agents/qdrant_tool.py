"""Qdrant tool for querying table information from the knowledge base."""
from typing import Dict, List, Optional, Any, Union
from langchain.tools import BaseTool
from langchain_community.vectorstores import Qdrant
from langchain_openai import OpenAIEmbeddings
from qdrant_client import QdrantClient
import os
from qdrant_client.http import models
import os
from utils import get_logger

logger = get_logger(__name__)

class QdrantTool(BaseTool):
    """Tool for querying table information from Qdrant vector store."""
    
    name: str = "query_knowledge_base"
    description: str = """Use this tool to find relevant tables based on the user's query.
    Input should be the user's natural language query about the data they're interested in.
    The tool will return a list of relevant tables with their descriptions.
    """
    
    def __init__(self, collection_name: str = "tables"):
        """Initialize the Qdrant tool with connection details."""
        super().__init__()
        self.collection_name = collection_name
        self.qdrant_client = QdrantClient(host="localhost", port=6333)
        
        # Initialize OpenAI embeddings model
        self.embeddings = OpenAIEmbeddings(
            model="text-embedding-3-small",
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )
        
        # Initialize vector store
        self.vector_store = Qdrant(
            client=self.qdrant_client,
            collection_name=self.collection_name,
            embeddings=self.embeddings
        )
    
    def _get_relevant_tables(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """Retrieve relevant tables from Qdrant based on the query.
        
        Args:
            query: The natural language query to search for
            k: Number of results to return
            
        Returns:
            List of dictionaries containing table information and relevance scores
        """
        try:
            # Convert query to embedding
            query_embedding = self.embeddings.embed_query(query)
            
            # Search for similar documents using the embedded query
            search_results = self.qdrant_client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=k,
                with_vectors=False,
                with_payload=True,
                score_threshold=0.3  # Optional: set a minimum relevance threshold
            )
            
            # Format results
            results = []
            for result in search_results:
                if result.payload:
                    # Extract metadata from the payload
                    payload = result.payload
                    table_info = {
                        "table_name": payload.get("table_name", ""),
                        "description": payload.get("table_description", ""),
                        "score": result.score,
                        "columns": ", ".join(payload.get("columns", []))  # Include all columns
                    }
                            
                    results.append(table_info)
                
            return results
            
        except Exception as e:
            logger.error(f"Error querying Qdrant: {str(e)}")
            return []
    
    def _format_table_info(self, table: Dict[str, Any]) -> str:
        """Format table information into a readable string."""
        formatted = [
            f"Table: {table['table_name']}",
            f"Description: {table['description']}",
            f"Relevance Score: {table['score']:.2f}",
            "",
            "Columns: " + table['columns'],
            "-" * 50
        ]
        
        return "\n".join(formatted)
    
    def _run(self, query: str) -> str:
        """Use the tool synchronously."""
        tables = self._get_relevant_tables(query)
        if not tables:
            return "No relevant tables found for the given query."
            
        # Format each table's information
        formatted_tables = [self._format_table_info(table) for table in tables]
        
        # Combine all tables with a separator
        return "\n\n".join([f"{i+1}. {table}" for i, table in enumerate(formatted_tables)])
    
    async def _arun(self, query: str) -> str:
        """Use the tool asynchronously."""
        return self._run(query)

def create_qdrant_tool() -> QdrantTool:
    """Create and return a configured Qdrant tool instance."""
    return QdrantTool()
