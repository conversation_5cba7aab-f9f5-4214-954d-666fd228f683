"""Trino agent implementation for database interactions using direct SQL queries."""

import os
import json
import asyncio
import logging
from typing import Any, Dict, List, Optional, Tuple
import uuid
from datetime import datetime, timezone, timedelta

# LangChain imports
from langchain.agents import Too<PERSON>, AgentExecutor, create_structured_chat_agent
# from langchain.agents.structured_chat.base import StructuredChatAgent
# from langchain.agents.format_scratchpad.openai_functions import format_to_openai_functions
from langchain_core.prompts import MessagesPlaceholder
# from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_aws import ChatBedrock
from langchain.tools import BaseTool, StructuredTool
from langchain.memory import ConversationBufferMemory

# SQLAlchemy imports
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from .base_agent import BaseAgent, AgentResponse
from .help_tool import create_help_tool
from .knowledge_base_tool import create_knowledge_base_tool
from .qdrant_tool import create_qdrant_tool

import logging

logger = logging.getLogger(__name__)

ist = timezone(timedelta(hours=5, minutes=30))  # IST is UTC+5:30

class TrinoAgent(BaseAgent):
    """Agent for executing Trino SQL queries with natural language interface."""
    
    def __init__(self):
        super().__init__(name="trino_agent")
        self.llm = None
        self.engine = None
        self.Session = None
        self.agent_executor = None
        self._initialized = False
        # Initialize session memory store
        self.session_memories = {}
        # https://github.com/langchain-ai/langchain/issues/4000
        self.chat_history = MessagesPlaceholder(variable_name="chat_history")
        # Store conversation memories by session ID
        self.conversation_memories: Dict[str, ConversationBufferMemory] = {}
        # Default system message
        self.system_message = """You are a helpful database assistant. Your role is to help users 
        explore and analyze data by writing and executing SQL queries. Always be concise and 
        provide clear explanations for your actions. If you need to make assumptions, state them 
        clearly."""
        
    def quote_identifier(self, identifier: str) -> str:
        """Properly quote an SQL identifier.
        
        Args:
            identifier: The identifier to quote
            
        Returns:
            Properly quoted SQL identifier
        """
        if not identifier or not isinstance(identifier, str):
            return 'NULL'
            
        # Remove any existing quotes and then add double quotes
        return '"' + identifier.replace('"', '""') + '"'
        
    async def initialize(self, session_id: str = "default") -> AgentResponse:
        """Initialize the Trino agent with LLM and database connection.
        
        Args:
            session_id: The session ID to initialize the agent with
        """
        if self._initialized:
            return AgentResponse(
                success=True,
                message="Agent already initialized"
            )
            
        try:
            # Initialize LLM
            logger.info("Initializing LLM...")
            self.llm = self._initialize_llm()
            
            # Initialize database connection
            logger.info("Initializing database connection...")
            self.engine, self.Session = self._initialize_database()
            
            # Create tools
            logger.info("Creating tools...")
            tools = self._create_tools()
            
            # Initialize agent
            logger.info("Initializing agent executor...")
            self.agent_executor = self._create_agent_executor(tools)
            
            self._initialized = True
            return AgentResponse(
                success=True,
                message="Trino agent initialized successfully"
            )
            
        except Exception as e:
            logger.error(f"Failed to initialize Trino agent: {str(e)}", exc_info=True)
            return AgentResponse(
                success=False,
                message=f"Failed to initialize Trino agent: {str(e)}"
            )
    
    def _initialize_llm(self):
        """Initialize the language model."""
        return ChatBedrock(
            model_id=os.environ.get("BEDROCK_MODEL_ID", "anthropic.claude-3-5-sonnet-20240620-v1:0"),
            region_name=os.environ.get("AWS_REGION", "ap-southeast-1"),
            model_kwargs={
                "temperature": 0.1,
                "max_tokens": 4000
            },
            streaming=True
        )
    
    def _initialize_database(self):
        """Initialize the database connection."""
        try:
            trino_host = os.environ.get("TRINO_HOST", "localhost")
            trino_port = os.environ.get("TRINO_PORT", "8889")
            trino_user = os.environ.get("TRINO_USER", "bi_tableau")
            trino_catalog = os.environ.get("TRINO_CATALOG", "blinkit_iceberg")
            trino_schema = os.environ.get("TRINO_SCHEMA", "dwh")
            
            logger.info(f"Connecting to Trino at {trino_host}:{trino_port} as {trino_user}")
            logger.info(f"Using catalog: {trino_catalog}, schema: {trino_schema}")
            
            connection_url = f"trino://{trino_user}@{trino_host}:{trino_port}"
            
            engine = create_engine(
                connection_url,
                connect_args={
                    'http_scheme': 'http',
                    'auth': None,
                    'catalog': trino_catalog,
                    'schema': trino_schema
                },
                echo=True,  # Enable SQL query logging
                future=True
            )
            
            # Test the connection
            with engine.connect() as conn:
                logger.info("Successfully connected to Trino")
            
            Session = sessionmaker(bind=engine)
            return engine, Session
            
        except Exception as e:
            logger.error(f"Failed to initialize database connection: {str(e)}", exc_info=True)
            raise
    
    def _create_tools(self) -> List[BaseTool]:
        """Create tools for the agent to use."""
        tools = [
            # Help tools
            create_help_tool(),
            create_knowledge_base_tool(),
            
            # Knowledge base search tool
            create_qdrant_tool(),
            
            # Data exploration tools
            StructuredTool.from_function(
                func=self._list_catalogs_structured,
                name="list_catalogs",
                description="List all available catalogs. No input required."
            ),
            Tool(
                name="list_schemas",
                func=self._list_schemas_sync,
                description="List all schemas in a catalog. Input should be the catalog name."
            ),
            Tool(
                name="list_tables",
                func=self._list_tables_sync,
                description="List all tables in a schema. Input should be in format 'catalog.schema'."
            ),
            Tool(
                name="describe_table",
                func=self._describe_table_sync,
                description="Describe a table's schema. Input should be in format 'catalog.schema.table'."
            ),
            Tool(
                name="execute_sql",
                func=self._execute_query_sync,
                description="Execute a SQL query. Input should be a valid SQL query."
            )
        ]
        return tools
    
    def _get_or_create_memory(self, session_id: str) -> ConversationBufferMemory:
        """Get or create a memory instance for the given session."""
        if session_id not in self.conversation_memories:
            self.conversation_memories[session_id] = ConversationBufferMemory(
                memory_key="chat_history",
                return_messages=True,
                input_key="input"
            )
        return self.conversation_memories[session_id]
    
    def _create_agent_executor(self, tools: List[BaseTool], session_id: str = "default") -> AgentExecutor:
        """Create the agent executor with the given tools and custom prompt.
        
        Args:
            tools: List of tools the agent can use
            session_id: Unique identifier for the conversation session
        """
        from langchain.agents import initialize_agent, AgentType
        from langchain.agents.agent import AgentExecutor
        from langchain.memory import ConversationBufferMemory
        
        # Get or create memory for this session
        memory = self._get_or_create_memory(session_id)
        
        # Create the agent with STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION
        agent = initialize_agent(
            tools=tools,
            llm=self.llm,
            agent=AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=15,
            return_intermediate_steps=True,
            memory=memory,
            agent_kwargs={
                "memory_prompts": [self.chat_history],
                "input_variables": ["input", "agent_scratchpad", "chat_history"]
            }
        )
        
        # Create a wrapper to handle additional context
        class ContextAwareAgentExecutor(AgentExecutor):
            session_id: str = "default"
            
            def __init__(self, *args, **kwargs):
                session_id = kwargs.pop('session_id', 'default')
                super().__init__(*args, **kwargs)
                self.session_id = session_id
                
            async def ainvoke(self, input_data, *args, **kwargs):
                if isinstance(input_data, dict):
                    # Add context to the input
                    context = []
                    if "selected_catalog" in input_data:
                        context.append(f"Catalog: {input_data['selected_catalog']}")
                    if "selected_schema" in input_data:
                        context.append(f"Schema: {input_data['selected_schema']}")
                    
                    if context:
                        input_data["input"] = f"{input_data.get('input', '')}\nContext: {', '.join(context)}"
                
                return await super().ainvoke(input_data, *args, **kwargs)
        
        return ContextAwareAgentExecutor.from_agent_and_tools(
            agent=agent.agent,
            tools=tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=15,
            return_intermediate_steps=True,
            memory=memory,
            session_id=session_id
        )
    
    # Synchronous wrapper methods for tools
    def _list_catalogs_structured(self) -> str:
        """StructuredTool-compatible wrapper for list_catalogs."""
        return asyncio.run(self.list_catalogs())
    
    def _list_schemas_sync(self, catalog_name: str) -> str:
        """Synchronous wrapper for list_schemas."""
        return asyncio.run(self.list_schemas(catalog_name))
    
    def _list_tables_sync(self, schema_path: str) -> str:
        """Synchronous wrapper for list_tables."""
        parts = schema_path.split('.')
        if len(parts) != 2:
            return "Invalid input format. Expected 'catalog.schema'"
        return asyncio.run(self.list_tables(parts[0], parts[1]))
    
    def _describe_table_sync(self, table_path: str) -> str:
        """Synchronous wrapper for describe_table."""
        parts = table_path.split('.')
        if len(parts) != 3:
            return "Invalid input format. Expected 'catalog.schema.table'"
        return asyncio.run(self.describe_table(parts[0], parts[1], parts[2]))
    
    def _execute_query_sync(self, query: str) -> dict:
        """Synchronous wrapper for execute_query, returns dict not string"""
        return asyncio.run(self.execute_query(query))
    
    # Core async methods
    async def _execute_sql(self, query: str, params: dict = None) -> List[Dict[str, Any]]:
        """Execute a SQL query and return the results."""
        try:
            logger.info(f"Executing SQL query: {query}")
            if params:
                logger.info(f"With parameters: {params}")
                
            # Create a new connection for this operation
            with self.engine.connect() as conn:
                # Begin a transaction
                with conn.begin():
                    # Execute the query
                    if params:
                        result = conn.execute(text(query), params)
                    else:
                        result = conn.execute(text(query))
                    
                    # Fetch results if this is a SELECT or similar query
                    if result.returns_rows:
                        columns = list(result.keys())
                        rows = []
                        for row in result.fetchall():
                            # Convert row to dict, handling different SQLAlchemy row types
                            if hasattr(row, '_asdict'):
                                rows.append(row._asdict())
                            elif hasattr(row, '_mapping'):
                                rows.append(dict(row._mapping))
                            else:
                                rows.append(dict(zip(columns, row)))
                        
                        logger.info(f"Query returned {len(rows)} rows")
                        return rows
                    
                    # For non-result queries (INSERT, UPDATE, etc.) return the row count
                    return [{"rows_affected": result.rowcount}]
                    
        except Exception as e:
            error_msg = f"Error executing query: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise Exception(error_msg) from e
    
    async def list_catalogs(self) -> str:
        """List all available catalogs."""
        try:
            results = await self._execute_sql("SHOW CATALOGS")
            catalogs = [row["Catalog"] for row in results if "Catalog" in row]
            return json.dumps({"catalogs": catalogs}, indent=2)
        except Exception as e:
            logger.error("Error listing catalogs", exc_info=True)
            return json.dumps({"error": str(e)})

    async def list_schemas(self, catalog: str) -> Dict[str, Any]:
        """List all schemas in a catalog.
        
        Args:
            catalog: Name of the catalog
            
        Returns:
            Dictionary containing the list of schemas and metadata
        """
        try:
            query = f"SHOW SCHEMAS FROM {self.quote_identifier(catalog)}"
            result = await self._execute_sql(query)
            
            # Extract schema names from the result
            schemas = []
            if result and isinstance(result, list):
                for row in result:
                    if isinstance(row, dict) and 'Schema' in row:
                        schemas.append(row['Schema'])
                    elif hasattr(row, 'get') and 'Schema' in row:
                        schemas.append(row.get('Schema'))
                    elif isinstance(row, (list, tuple)) and len(row) > 0:
                        schemas.append(row[0])
                        
            return schemas
            
        except Exception as e:
            error_msg = f"Error listing schemas for catalog {catalog}: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return []

    async def list_tables(self, catalog: str, schema: str) -> Dict[str, Any]:
        """List all tables in a schema.
        
        Args:
            catalog: Name of the catalog
            schema: Name of the schema
            
        Returns:
            Dictionary containing the list of tables
        """
        try:
            # Execute the SHOW TABLES query
            query = f"SHOW TABLES FROM {catalog}.{schema}"
            result = await self._execute_sql(query)
            
            # Extract table names from the result
            tables = []
            if result and isinstance(result, list):
                for row in result:
                    if isinstance(row, dict) and 'Table' in row:
                        tables.append(row['Table'])
                    elif hasattr(row, 'get') and 'Table' in row:
                        tables.append(row.get('Table'))
                    elif isinstance(row, (list, tuple)) and len(row) > 0:
                        tables.append(row[0])
            
            self.logger.info(f"Found {len(tables)} tables in {catalog}.{schema}")
            return {
                'success': True,
                'tables': tables,
                'catalog': catalog,
                'schema': schema
            }
            
        except Exception as e:
            error_msg = f"Error listing tables for {catalog}.{schema}: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {
                'success': False,
                'error': error_msg,
                'catalog': catalog,
                'schema': schema
            }

    async def describe_table(self, catalog_name: str, schema_name: str, table_name: str) -> str:
        """Get the schema of a table using DESCRIBE command."""
        try:
            # Get column information using DESCRIBE
            query = f"DESCRIBE {catalog_name}.{schema_name}.{table_name}"
            describe_results = await self._execute_sql(query)
            
            # Format the results
            columns = []
            for row in describe_results:
                if 'Column' in row and 'Type' in row and 'Extra' in row and 'Comment' in row:
                    columns.append({
                        'column_name': row['Column'],
                        'data_type': row['Type'],
                        'is_nullable': 'YES' if 'NULL' in row['Extra'] else 'NO',
                        'default_value': row.get('Default', None),
                        'comment': row.get('Comment', '')
                    })
            
            # Get table metadata
            table_info = {
                'catalog': catalog_name,
                'schema': schema_name,
                'table': table_name,
                'columns': columns
            }
            
            return json.dumps(table_info, indent=2)
            
        except Exception as e:
            error_msg = f"Error describing table {catalog_name}.{schema_name}.{table_name}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return json.dumps({"error": error_msg})
    
    async def execute_query(self, query: str, min_rows: int = 0, max_rows: int = 1000) -> dict:
        """Execute a custom SQL query.

        Returns a dict result, not a JSON string!
        """
        try:
            # Basic validation
            query = query.strip()
            if not query:
                return {"error": "Empty query"}

            # Check for potentially destructive operations at the start of the query
            destructive_operations = ['DROP', 'TRUNCATE', 'DELETE', 'UPDATE', 'INSERT']
            query_upper = query.upper().strip()
            if any(query_upper.startswith(op) for op in destructive_operations):
                return {"error": "This operation is not allowed for security reasons."}

            # Add LIMIT clause if max_rows is specified
            modified_query = query.rstrip(';').strip()
            if max_rows > 0:
                if 'LIMIT ' not in modified_query.upper():
                    modified_query = f"{modified_query} LIMIT {max_rows + 1}"

            # Execute the modified query
            rows = await self._execute_sql(modified_query)
            row_count = len(rows)

            truncated = False
            if max_rows > 0 and row_count > max_rows:
                rows = rows[:max_rows]
                truncated = True
                original_row_count = row_count
                row_count = max_rows

            if row_count < min_rows:
                return {
                    'success': False,
                    'query': query,
                    'row_count': row_count,
                    'error': f'Query returned {row_count} rows, but at least {min_rows} rows were expected'
                }

            response = {
                'success': True,
                'query': query,
                'row_count': row_count,
                'results': rows
            }

            if truncated:
                response.update({
                    'truncated': True,
                    'original_row_count': original_row_count,
                    'max_rows_limit': max_rows
                })

            return response

        except Exception as e:
            error_msg = f"Error executing query: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {"error": error_msg}
    
    async def execute(
        self,
        query: str,
        session_id: str = "default",
        **kwargs
    ) -> Dict[str, Any]:
        """Execute a natural language query with conversation memory.
        
        Args:
            query: The natural language query
            session_id: Unique identifier for the conversation session
            **kwargs: Additional arguments including selected_catalog and selected_schema
            
        Returns:
            Dictionary containing:
            - answer: The final answer
            - intermediate_steps: List of steps taken
            - session_id: The conversation ID
            - error: Error message if any
        """
        if not self._initialized:
            await self.initialize()
        
        # Get or create memory for this session
        if session_id not in self.conversation_memories:
            self.conversation_memories[session_id] = ConversationBufferMemory(
                memory_key="chat_history",
                return_messages=True,
                output_key="output"
            )
        
        memory = self.conversation_memories[session_id]
        
        try:
            # Debug: Print session ID and memory state
            print(f"\n=== Debug: Processing query for session {session_id} ===")
            print(f"Conversation memories: {list(self.conversation_memories.keys())}")
            
            # Prepare context with catalog and schema if provided
            context = ""
            if kwargs.get("selected_catalog"):
                context += f"\nUse only this Catalog: {kwargs['selected_catalog']}"
            if kwargs.get("selected_schema"):
                context += f"\nUse only this Schema: {kwargs['selected_schema']}"
            if kwargs.get("selected_tables"):
                table_list = ", ".join(kwargs['selected_tables'])
                context += f"\nUse only these Tables: {table_list}"
                
            # Add context to the query
            enriched_query = f"{query}{context}" if context else query

            current_date_ist = datetime.now(ist).strftime("%Y-%m-%d %H:%M:%S %Z")
            full_query = f"""
            {enriched_query}
            
            Additional Info:
                Current Date and Time (IST): {current_date_ist}
                Currency to use: Rupees
            """
            
            # Load existing conversation history
            chat_history = memory.load_memory_variables({})
            print(f"Loaded chat history: {chat_history}")
            print(f"Memory buffer: {memory.buffer}")
            
            # Prepare input with chat history
            input_data = {
                "input": full_query,
                "chat_history": chat_history.get("chat_history", []),
                "agent_scratchpad": []
            }
            
            # Print formatted input for debugging
            print("\n=== Input to agent ===")
            print(f"Input: {input_data['input']}")
            print(f"Chat history length: {len(input_data['chat_history'])}")
            for i, msg in enumerate(input_data['chat_history']):
                role = "Human" if hasattr(msg, 'type') and msg.type == 'human' else "AI"
                print(f"{i}. {role}: {msg.content}")
            
            # Execute the agent
            result = await self.agent_executor.ainvoke(input_data)
            
            # Debug: Print what we're about to save to memory
            print(f"Saving to memory - Input: {full_query}")
            print(f"Saving to memory - Output: {result['output']}")
            
            # Save the interaction to memory
            memory.save_context(
                {"input": full_query},
                {"output": result.get("output", "")}
            )
            
            # Process the result
            if isinstance(result, dict):
                answer = result.get("output", str(result))
                intermediate_steps = []
                
                # Process intermediate steps to extract tool info
                for step in result.get("intermediate_steps", []):
                    if len(step) >= 2:
                        action, observation = step
                        step_info = {
                            "tool": getattr(action, "tool", "unknown"),
                            "tool_input": getattr(action, "tool_input", ""),
                            "log": getattr(action, "log", ""),
                            "observation": str(observation)[:2000]
                        }
                        intermediate_steps.append(step_info)
            else:
                answer = str(result)
                intermediate_steps = []
            
            logger.info(f"Processed intermediate steps: {json.dumps(intermediate_steps, indent=2)}")
            
            return {
                "answer": answer,
                "intermediate_steps": intermediate_steps,
                "session_id": session_id,
                "error": None
            }
        except Exception as e:
            error_msg = f"Error executing query: {str(e)}"
            logger.error(error_msg, exc_info=True)
            response = {
                "answer": f"Error: {error_msg}",
                "error": error_msg,
                "success": False
            }
    
        return response
    
    async def close(self) -> None:
        """Clean up resources."""
        if self.engine:
            self.engine.dispose()
        self._initialized = False
        self.conversation_memories.clear()
    
    async def clear_conversation(self, session_id: str = "default") -> bool:
        """Clear conversation history for a specific session.
        
        Args:
            session_id: The ID of the session to clear
            
        Returns:
            bool: True if session was cleared, False if not found
        """
        if session_id in self.conversation_memories:
            del self.conversation_memories[session_id]
            return True
        return False
