"""Utility functions for agent guide and help system."""
import os
from pathlib import Path
from typing import Dict, List, Optional

from utils import get_logger

logger = get_logger(__name__)

class AgentGuide:
    """Manages the agent's help guide and documentation."""
    
    def __init__(self, guide_path: Optional[str] = None):
        """Initialize with path to the guide markdown file."""
        if not guide_path:
            # Default path if not specified
            base_dir = Path(__file__).parent.parent
            guide_path = base_dir / "docs" / "AGENT_GUIDE.md"
        
        self.guide_path = Path(guide_path)
        self._guide_content = None
    
    def load_guide(self) -> str:
        """Load the guide content from file."""
        if not self._guide_content:
            try:
                with open(self.guide_path, 'r', encoding='utf-8') as f:
                    self._guide_content = f.read()
            except Exception as e:
                logger.error(f"Failed to load agent guide: {str(e)}")
                self._guide_content = "# Agent Guide\n\nUnable to load the full guide. Please contact support."
        return self._guide_content
    
    def get_quick_reference(self) -> str:
        """Extract just the quick reference section."""
        content = self.load_guide()
        # Extract content between Quick Reference headers
        start = content.find("## 🎯 Quick Reference Card")
        end = content.find("## 🔍", start)
        return content[start:end].strip() if start >= 0 else "Quick reference not available."
    
    def search_guide(self, query: str) -> str:
        """Search the guide for relevant sections based on query."""
        content = self.load_guide().lower()
        query = query.lower()
        
        # Simple keyword matching - can be enhanced with more sophisticated search
        if any(term in query for term in ['how to', 'how do i', 'help']):
            if any(term in query for term in ['list', 'show', 'tables', 'schemas']):
                return self._extract_section("## 🔍 Data Discovery")
            elif any(term in query for term in ['query', 'sql', 'select']):
                return self._extract_section("### Data Analysis")
        
        # Default to quick reference for general help
        return self.get_quick_reference()
    
    def _extract_section(self, header: str) -> str:
        """Extract a section of the guide by header."""
        content = self.load_guide()
        start = content.find(header)
        if start < 0:
            return f"Section not found: {header}"
            
        # Find the next section at the same level
        next_header = content.find("## ", start + 3)
        return content[start:next_header].strip()

# Singleton instance
_guide = None

def get_guide() -> AgentGuide:
    """Get the singleton guide instance."""
    global _guide
    if _guide is None:
        _guide = AgentGuide()
    return _guide
