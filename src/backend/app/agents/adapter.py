"""Adapter for Trino agent to handle different execution modes."""

import asyncio
import json
import uuid
import re
from typing import AsyncI<PERSON><PERSON>, Dict, Any, Optional

from .trino_agent import TrinoAgent
from utils import get_logger

logger = get_logger(__name__)

class TrinoAgentAdapter:
    """Adapter for Trino agent to handle different execution modes."""
    
    def __init__(self, streaming: bool = False):
        """Initialize the adapter.
        
        Args:
            streaming: Whether to enable streaming mode
        """
        self.streaming = streaming
        self.trino_agent = TrinoAgent()
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize the adapter and underlying agent."""
        if not self._initialized:
            await self.trino_agent.initialize()
            self._initialized = True
    
    async def query(self, question: str, **kwargs) -> Dict[str, Any]:
        """Execute a query and return the full result.
        
        Args:
            question: The natural language question to answer
            selected_catalog: The selected catalog (optional)
            selected_schema: The selected schema (optional)
            
        Returns:
            Dictionary containing the query result and metadata with suggested questions
        """
        await self.initialize()
        result = await self.trino_agent.execute(
            question,
            selected_catalog=kwargs.get('selected_catalog'),
            selected_schema=kwargs.get('selected_schema')
        )
        
        # Add suggested questions to the result if there's an answer
        if "answer" in result:
            result["suggested_questions"] = await self._generate_suggested_questions(
                question,
                result.get("answer", "")
            )
            
        return result
        
    async def list_catalogs(self) -> Dict[str, Any]:
        """List all available catalogs.
        
        Returns:
            List of catalog names
        """
        try:
            await self.initialize()
            result = await self.trino_agent.list_catalogs()
            if isinstance(result, str):
                result = json.loads(result)
            # Return a simple list of catalogs
            return result.get("catalogs", [])
        except Exception as e:
            logger.error(f"Error listing catalogs: {str(e)}", exc_info=True)
            return []
            
    async def list_schemas(self, catalog: str) -> Dict[str, Any]:
        """List all schemas in a catalog.
        
        Args:
            catalog: The catalog name
            
        Returns:
            Dictionary containing the list of schemas and metadata
        """
        try:
            await self.initialize()
            return await self.trino_agent.list_schemas(catalog)
        except Exception as e:
            logger.error(f"Error listing schemas for catalog {catalog}: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'catalog': catalog
            }
        
    async def list_tables(self, catalog: str, schema: str) -> Dict[str, Any]:
        """List all tables in a schema.
        
        Args:
            catalog: The catalog name
            schema: The schema name
            
        Returns:
            Dictionary containing the list of tables and metadata
        """
        try:
            await self.initialize()
            return await self.trino_agent.list_tables(catalog, schema)
        except Exception as e:
            logger.error(f"Error listing tables for {catalog}.{schema}: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'catalog': catalog,
                'schema': schema
            }
    
    async def stream_query(self, question: str, **kwargs) -> AsyncIterator[str]:
        """Stream the query results as they are generated.
        
        Args:
            question: The natural language question to answer
            selected_catalog: The selected catalog (optional)
            selected_schema: The selected schema (optional)
            selected_tables: The selected tables (optional)
            session_id: Unique identifier for the conversation (optional, will generate if not provided)
            
        Yields:
            Dictionary chunks containing partial results in the format expected by the frontend
        """
        await self.initialize()
        
        try:
            # Generate or use provided session ID
            session_id = kwargs.get("session_id", str(uuid.uuid4()))
            
            # Send initial status with session ID
            yield json.dumps({
                "type": "status",
                "content": "Processing your request...",
                "session_id": session_id
            })
            
            logger.info(f"Processing query for session {session_id}. Kwargs: {kwargs}")
            
            # Get the result from the agent with context and session
            result = await self.trino_agent.execute(
                query=question,
                session_id=session_id,
                selected_catalog=kwargs.get('selected_catalog'),
                selected_schema=kwargs.get('selected_schema'),
                selected_tables=kwargs.get('selected_tables')
            )
            
            # Send intermediate steps if available
            intermediate_steps = result.get("intermediate_steps") or []
            for step in intermediate_steps:
                try:
                    yield json.dumps({
                        "type": "step",
                        "content": step if isinstance(step, (str, dict)) else str(step),
                        "session_id": session_id
                    })
                except Exception as e:
                    logger.error(f"Error processing step: {e}", exc_info=True)
                    yield json.dumps({
                        "type": "error",
                        "content": f"Error processing step: {str(e)}",
                        "session_id": session_id
                    })
            
            # Generate suggested follow-up questions based on the query context using LLM
            suggested_questions = await self._generate_suggested_questions(
                question, 
                result.get("answer", "")
            )
            
            # Send the final answer with suggested questions
            if "answer" in result:
                yield json.dumps({
                    "type": "answer",
                    "content": result["answer"],
                    "session_id": session_id,
                    "suggested_questions": suggested_questions
                })
            elif "error" in result:
                yield json.dumps({
                    "type": "error",
                    "error": result["error"],
                    "session_id": session_id
                })
            
            # Send completion message
            yield json.dumps({
                "type": "completion",
                "session_id": session_id
            })
            
        except Exception as e:
            error_msg = f"Error processing query: {str(e)}"
            logger.error(error_msg, exc_info=True)
            yield json.dumps({
                "type": "error",
                "error": error_msg,
                "session_id": session_id
            })
    
    async def clear_conversation(self, session_id: str = "default") -> bool:
        """Clear conversation history for a specific session.
        
        Args:
            session_id: The ID of the session to clear
            
        Returns:
            bool: True if session was cleared, False if not found
        """
        if not self._initialized:
            await self.initialize()
        return await self.trino_agent.clear_conversation(session_id)
        
    async def _generate_suggested_questions(self, user_query: str, agent_response: str) -> list[str]:
        """Generate relevant follow-up questions using an LLM based on the user's query and the agent's response.
        
        Args:
            user_query: The original user question
            agent_response: The agent's response
            
        Returns:
            List of suggested follow-up questions
        """
        # Default questions that are always relevant (fallback in case LLM fails)
        default_questions = [
            "Can you explain this in more detail?",
            "Can you show me the raw data behind this?",
            "How does this compare to last month?",
            "What are the key insights from this data?"
        ]
        
        # If either input is empty, return default questions
        if not user_query or not agent_response:
            logger.warning("Empty user query or agent response, returning default questions")
            return default_questions
            
        try:
            from langchain_core.messages import HumanMessage, SystemMessage
            
            # Create system message with instructions
            system_message = SystemMessage(
                content="""You are a helpful assistant that generates relevant follow-up questions based on a user's query and the agent's response. 
                Generate exactly 4 concise, specific questions that would help the user explore the data further.
                Format: One question per line, without any numbering or bullet points."""
            )
            
            # Create user message with the conversation context
            user_message = HumanMessage(
                content=f"""Based on this conversation, generate 4 relevant follow-up questions:
                
                User Query: {user_query}
                
                Agent Response: {agent_response}"""
            )
            
            # Use the same LLM that the agent is using
            if not hasattr(self.trino_agent, 'llm') or not self.trino_agent.llm:
                logger.warning("No LLM available in trino_agent, using default questions")
                return default_questions
                
            llm = self.trino_agent.llm
            
            try:
                # Generate questions using the LLM with proper message format
                logger.info("Calling LLM to generate suggested questions")
                
                # Check if this is a Bedrock LLM
                if 'Bedrock' in llm.__class__.__name__:
                    # For Bedrock, we need to use invoke with the proper message format
                    from langchain_core.messages import HumanMessage, SystemMessage
                    
                    # Create the messages in the format Bedrock expects
                    messages = [
                        {"role": "user", "content": system_message.content},
                        {"role": "assistant", "content": "I understand I should generate follow-up questions."},
                        {"role": "user", "content": user_message.content}
                    ]
                    
                    # Call the model directly with the messages
                    response = await llm.ainvoke(messages)
                    generated_text = response.content if hasattr(response, 'content') else str(response)
                    
                # For other chat models
                elif hasattr(llm, 'achat'):
                    response = await llm.achat([system_message, user_message])
                    generated_text = response.content
                    
                # For completion models
                elif hasattr(llm, 'agenerate'):
                    combined_prompt = f"""{system_message.content}
                    
                    {user_message.content}"""
                    response = await llm.agenerate([combined_prompt])
                    if hasattr(response, 'generations') and response.generations:
                        generated_text = response.generations[0][0].text
                    else:
                        generated_text = str(response)
                else:
                    logger.warning("LLM doesn't support any known generation methods")
                    return default_questions
            
                if not generated_text or not isinstance(generated_text, str):
                    logger.warning("No valid text generated, using default questions")
                    return default_questions
                
                # Parse the response to extract questions
                questions = [
                    q.strip() 
                    for q in generated_text.split('\n') 
                    if q.strip() and not q.strip().startswith(('1.', '2.', '3.', '4.', '-', '*'))
                ]
                
                # Clean up any remaining markdown or numbering
                questions = [
                    re.sub(r'^\s*[0-9]+\.?\s*', '', q)  # Remove numbering like "1. " or "1) "
                    .replace('"', '')  # Remove quotes
                    .replace("'", '')   # Remove single quotes
                    .strip()
                    for q in questions
                    if q.strip()
                ]
                        
                logger.info(f"Extracted questions: {questions}")
                
                # Ensure we have at least one question
                if questions:
                    # Ensure we have exactly 4 questions
                    if len(questions) >= 4:
                        return questions[:4]
                    # If we got some questions but not enough, fill the rest with defaults
                    return questions + default_questions[len(questions):4]
                
                logger.warning("Could not extract questions from LLM response, using default questions")
                return default_questions
                
            except Exception as llm_error:
                logger.error(f"Error calling LLM for suggested questions: {str(llm_error)}", exc_info=True)
                return default_questions
            
        except Exception as e:
            logger.error(f"Unexpected error generating suggested questions: {str(e)}", exc_info=True)
            return default_questions
    
    async def close(self) -> None:
        """Clean up resources."""
        if self.trino_agent:
            await self.trino_agent.close()
            self._initialized = False
