"""Base agent class for all agents in the application."""
from typing import Any, Dict, List, Optional
import logging
from pydantic import BaseModel, Field

from config import settings
from utils import get_logger

class AgentResponse(BaseModel):
    """Standard response format for agent operations."""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class BaseAgent:
    """Base class for all agents with common functionality."""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """Initialize the agent.
        
        Args:
            name: Name of the agent
            config: Configuration dictionary
        """
        self.name = name
        self.config = config or {}
        self.logger = get_logger(f"agent.{name}")
        self.initialized = False
    
    async def initialize(self) -> AgentResponse:
        """Initialize the agent's resources."""
        try:
            self.logger.info(f"Initializing {self.name} agent")
            # Add initialization logic here
            self.initialized = True
            return AgentResponse(
                success=True,
                message=f"{self.name} agent initialized successfully"
            )
        except Exception as e:
            self.logger.error(f"Failed to initialize {self.name} agent: {str(e)}", exc_info=True)
            return AgentResponse(
                success=False,
                message=f"Failed to initialize {self.name} agent",
                error=str(e)
            )
    
    async def execute(self, command: str, **kwargs) -> AgentResponse:
        """Execute a command with the agent.
        
        Args:
            command: Command to execute
            **kwargs: Additional arguments for the command
            
        Returns:
            AgentResponse with the result of the command
        """
        if not self.initialized:
            init_result = await self.initialize()
            if not init_result.success:
                return init_result
        
        self.logger.info(f"Executing command: {command}")
        try:
            # Default implementation - should be overridden by subclasses
            return AgentResponse(
                success=False,
                message=f"Command '{command}' not implemented",
                error="Not implemented"
            )
        except Exception as e:
            self.logger.error(f"Error executing command '{command}': {str(e)}", exc_info=True)
            return AgentResponse(
                success=False,
                message=f"Error executing command: {command}",
                error=str(e)
            )
    
    async def cleanup(self) -> AgentResponse:
        """Clean up resources used by the agent."""
        try:
            self.logger.info(f"Cleaning up {self.name} agent")
            # Add cleanup logic here
            self.initialized = False
            return AgentResponse(
                success=True,
                message=f"{self.name} agent cleaned up successfully"
            )
        except Exception as e:
            self.logger.error(f"Error cleaning up {self.name} agent: {str(e)}", exc_info=True)
            return AgentResponse(
                success=False,
                message=f"Error cleaning up {self.name} agent",
                error=str(e)
            )
    
    def __del__(self):
        """Ensure resources are cleaned up when the agent is destroyed."""
        if self.initialized:
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.cleanup())
                else:
                    loop.run_until_complete(self.cleanup())
            except Exception as e:
                self.logger.error(f"Error in agent cleanup: {str(e)}", exc_info=True)
