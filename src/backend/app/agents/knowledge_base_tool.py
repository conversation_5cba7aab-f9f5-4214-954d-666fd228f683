"""Knowledge base tool for the Lang<PERSON><PERSON>n agent to reference the knowledge base guide."""
from typing import Optional, Dict, Any
from langchain.tools import BaseTool
from .knowledge_base_utils import get_knowledge_base_guide

class KnowledgeBaseTool(BaseTool):
    """Tool for providing knowledge base guidance to users."""
    
    name: str = "knowledge_base_help"
    description: str = """Use this tool when the user asks for help with:
    - Finding relevant tables in the knowledge base
    - Understanding how to use the knowledge base
    - Learning about available data domains
    - Getting examples of knowledge base queries
    
    Input should be the user's question about the knowledge base.
    Examples:
    - "How do I find tables about customer data?"
    - "Show me examples of knowledge base queries"
    - "What kind of information is in the knowledge base?"
    """
    
    def _run(self, query: str) -> str:
        """Use the tool synchronously."""
        guide = get_knowledge_base_guide()
        return guide.search_guide(query)
    
    async def _arun(self, query: str) -> str:
        """Use the tool asynchronously."""
        return self._run(query)

def create_knowledge_base_tool() -> KnowledgeBaseTool:
    """Create and return a configured knowledge base tool instance."""
    return KnowledgeBaseTool()
