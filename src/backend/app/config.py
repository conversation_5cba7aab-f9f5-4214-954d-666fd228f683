"""Configuration settings for the application."""

import os
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file
# load_dotenv()

class Settings(BaseSettings):
    """Application settings."""
    
    # AWS settings for Bedrock
    AWS_ACCESS_KEY_ID: str = os.getenv("AWS_ACCESS_KEY_ID", "")
    AWS_SECRET_ACCESS_KEY: str = os.getenv("AWS_SECRET_ACCESS_KEY", "")
    AWS_SESSION_TOKEN: str = os.getenv("AWS_SESSION_TOKEN", "")
    AWS_REGION: str = os.getenv("AWS_REGION", "ap-southeast-1")
    BEDROCK_MODEL_ID: str = os.getenv("BEDROCK_MODEL_ID", "anthropic.claude-3-5-sonnet-20240620-v1:0")
    
    # Trino database connection
    TRINO_CONNECTION_STRING: str = os.getenv("TRINO_CONNECTION_STRING", "")
    TRINO_HOST: str = os.getenv("TRINO_HOST", "localhost")
    TRINO_PORT: int = int(os.getenv("TRINO_PORT", "8889"))
    TRINO_USER: str = os.getenv("TRINO_USER", "user")
    TRINO_CATALOG: str = os.getenv("TRINO_CATALOG", "")
    TRINO_SCHEMA: str = os.getenv("TRINO_SCHEMA", "")
    
    # Application settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"

    # model_config = {
    #     "env_file": ".env",
    #     "case_sensitive": True
    # }

# Create a global settings object
settings = Settings()