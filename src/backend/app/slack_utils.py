import os
import warnings
from slack_sdk.web.base_client import SlackResponse
from slack_sdk.errors import SlackApiError
import hvac
import json
from slack_sdk import WebClient
from dotenv import load_dotenv

# load_dotenv()

SLACK_BOT_NAME = "bl-analytics-bot"
SLACK_TOKEN_VAULT_PATH = "dse/services/slack/tokens"

_client = {"slack": {}}

def get_hvac_client():
    try:
        client = _client["hvac"]
    except KeyError:
        client = hvac.Client(url=os.environ.get("VAULT_URL"))

    if not client.is_authenticated():
        client.token = os.environ.get("VAULT_SERVICE_TOKEN")
        if not client.is_authenticated():
            client = _authenticate_using_github_token(client)
        if not client.is_authenticated():
            raise ValueError("Could not authenticate to Vault")
        _client["hvac"] = client

    return client

def _authenticate_using_github_token(client) -> hvac.Client:
    vault_github_token = os.environ.get("VAULT_TOKEN")
    if vault_github_token:
        print("Authenticating with vault using github token")
        client.auth.github.login(token=vault_github_token)

    return client

def get_slack_client(user):
    def _slack_client():
        hvac_client = get_hvac_client()
        secrets = hvac_client.read(SLACK_TOKEN_VAULT_PATH)["data"]
        slack_token = secrets[user]
        client = WebClient(token=slack_token)
        _client["slack"][user] = client
        return client

    try:
        client = _client["slack"][user]
        if not client.auth_test().get("ok"):
            client = _slack_client()
    except (KeyError, SlackApiError):
        client = _slack_client()
    return client

def _response_handler(responses):
    errors = []
    results = []
    for response in responses:
        if not isinstance(response, SlackResponse):
            response = response.json()
        if not response["ok"]:
            errors.append(response)
        else:
            results.append(response)
    if errors:
        raise Exception(errors)
    else:
        return results


def send_slack_message(channel, text=None, user=None, files=[], thread_ts=None, source_info=True):
    def _send_message(channel, text, thread_ts):
        responses = []
        client = get_slack_client(SLACK_BOT_NAME)
        channel_id = channel

        # Retrieving metadata
        user = ""
        source = ""
        source_text = ""

        # Combine text and source info message in a single call
        combined_text = (
            text
            if not source_info
            else (text + "\n\n" + f"_{source_text}_" if text else f"_{source_text}_")
        )

        if combined_text:
            message_kwargs = {"channel": channel, "text": combined_text}
            if thread_ts:
                message_kwargs["thread_ts"] = thread_ts
            response = client.chat_postMessage(**message_kwargs)
            responses.append(response)
            channel_id = response["channel"]

        file_uploads = []
        for filepath in files:
            if not os.path.exists(filepath):
                raise FileNotFoundError(f"File not found at {filepath}")
            file_uploads.append({"filename": os.path.basename(filepath), "file": filepath})

        if file_uploads:
            try:
                responses.append(
                    client.files_upload_v2(
                        file_uploads=file_uploads, channel=channel_id, thread_ts=thread_ts
                    )
                )
            except SlackApiError as e:
                error_message = str(e)
                if "Server Error" in error_message:
                    warnings.warn(
                        "Failed to upload document due to internal server error",
                        category=RuntimeWarning,
                        stacklevel=2,
                    )
                else:
                    raise e

        return responses

    if not text and not files:
        raise Exception("Please pass either 'text' or 'files' in the function 'send_slack_message'")

    if user:
        warnings.warn(
            "Parameter 'user' in function 'send_slack_message' is Deprecated. Please avoid passing 'user' in function 'send_slack_message'",
            category=DeprecationWarning,
            stacklevel=2,
        )

    responses = _send_message(channel, text, thread_ts)
    return _response_handler(responses)

# if __name__ == "__main__":
#     send_slack_message(channel="neelesh-personal", text="Hello World", source_info=False)