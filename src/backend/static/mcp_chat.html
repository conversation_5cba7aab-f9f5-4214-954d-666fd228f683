<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Natural Language Query Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
            height: 600px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: #007bff;
            color: white;
            margin-left: auto;
        }

        .message.assistant {
            background: white;
            border: 1px solid #e9ecef;
            margin-right: auto;
        }

        .message.system {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            margin: 0 auto;
            font-size: 12px;
            color: #6c757d;
            max-width: 90%;
        }

        .message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            margin: 0 auto;
            max-width: 90%;
        }

        .message.tool-call {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            margin: 0 auto;
            max-width: 90%;
            font-family: monospace;
            font-size: 12px;
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .input-field {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
        }

        .input-field:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            padding: 8px 16px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            margin-bottom: 10px;
            font-size: 12px;
            color: #1976d2;
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            margin-right: auto;
            max-width: 80%;
        }

        .typing-indicator.show {
            display: block;
        }

        .dots {
            display: inline-block;
        }

        .dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #007bff;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .dots span:nth-child(1) { animation-delay: -0.32s; }
        .dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .server-status {
            padding: 10px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-size: 12px;
            color: #6c757d;
        }

        .server-status.online {
            background: #d4edda;
            color: #155724;
        }

        .server-status.offline {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 MCP Natural Language Query</h1>
            <p>Ask questions about your data using natural language</p>
        </div>
        
        <div id="serverStatus" class="server-status">
            Checking server status...
        </div>
        
        <div class="chat-container">
            <div id="messages" class="messages">
                <div class="message system">
                    Welcome! You can ask questions like:
                    <br>• "Show me all dashboards in Superset"
                    <br>• "List the available databases"
                    <br>• "Execute a query to count users"
                    <br>• "What charts are available?"
                </div>
            </div>
            
            <div class="typing-indicator" id="typingIndicator">
                <div class="dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                Processing your request...
            </div>
        </div>
        
        <div class="input-container">
            <div class="input-group">
                <input 
                    type="text" 
                    id="messageInput" 
                    class="input-field" 
                    placeholder="Ask a question about your data..."
                    autocomplete="off"
                >
                <button id="sendButton" class="send-button">Send</button>
            </div>
        </div>
    </div>

    <script>
        class MCPChatInterface {
            constructor() {
                this.messagesContainer = document.getElementById('messages');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.typingIndicator = document.getElementById('typingIndicator');
                this.serverStatus = document.getElementById('serverStatus');
                
                this.baseUrl = window.location.origin;
                this.isProcessing = false;
                
                this.init();
            }
            
            init() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !this.isProcessing) {
                        this.sendMessage();
                    }
                });
                
                this.checkServerStatus();
                setInterval(() => this.checkServerStatus(), 30000); // Check every 30 seconds
            }
            
            async checkServerStatus() {
                try {
                    const response = await fetch(`${this.baseUrl}/api/mcp/health`);
                    if (response.ok) {
                        const data = await response.json();
                        this.serverStatus.textContent = `✅ MCP Service Online - ${data.tools || 0} tools available`;
                        this.serverStatus.className = 'server-status online';
                    } else {
                        throw new Error('Server not responding');
                    }
                } catch (error) {
                    this.serverStatus.textContent = '❌ MCP Service Offline';
                    this.serverStatus.className = 'server-status offline';
                }
            }
            
            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || this.isProcessing) return;
                
                this.isProcessing = true;
                this.messageInput.value = '';
                this.sendButton.disabled = true;
                this.sendButton.textContent = 'Processing...';
                
                // Add user message
                this.addMessage(message, 'user');
                
                // Show typing indicator
                this.showTypingIndicator();
                
                try {
                    await this.processQuery(message);
                } catch (error) {
                    this.addMessage(`Error: ${error.message}`, 'error');
                } finally {
                    this.hideTypingIndicator();
                    this.isProcessing = false;
                    this.sendButton.disabled = false;
                    this.sendButton.textContent = 'Send';
                }
            }
            
            async processQuery(query) {
                const response = await fetch(`${this.baseUrl}/api/mcp/query/stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query, stream: true })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') return;
                            
                            try {
                                const parsed = JSON.parse(data);
                                this.handleStreamData(parsed);
                            } catch (e) {
                                // Ignore JSON parse errors for partial chunks
                            }
                        }
                    }
                }
            }
            
            handleStreamData(data) {
                switch (data.type) {
                    case 'status':
                        this.addMessage(`📊 ${data.message}`, 'system');
                        break;
                    case 'llm_response':
                        if (data.data && data.data.content) {
                            this.addMessage(data.data.content, 'assistant');
                        }
                        break;
                    case 'tool_call_start':
                        this.addMessage(`🔧 Calling tool: ${data.tool_name}`, 'tool-call');
                        break;
                    case 'tool_call_result':
                        this.addMessage(`📋 Result: ${data.result}`, 'tool-call');
                        break;
                    case 'tool_call_error':
                        this.addMessage(`❌ Tool error: ${data.error}`, 'error');
                        break;
                    case 'error':
                        this.addMessage(`❌ ${data.message}`, 'error');
                        break;
                    case 'complete':
                        this.addMessage(`✅ ${data.message}`, 'system');
                        break;
                }
            }
            
            addMessage(content, type) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                messageDiv.textContent = content;
                
                this.messagesContainer.appendChild(messageDiv);
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }
            
            showTypingIndicator() {
                this.typingIndicator.classList.add('show');
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }
            
            hideTypingIndicator() {
                this.typingIndicator.classList.remove('show');
            }
        }
        
        // Initialize the chat interface when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new MCPChatInterface();
        });
    </script>
</body>
</html>
