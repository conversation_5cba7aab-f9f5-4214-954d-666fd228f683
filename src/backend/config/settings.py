"""Application settings and configuration."""
import logging
import os
from pathlib import Path
from typing import Optional
from pydantic import BaseModel, Field

class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = "INFO"
    output: str = "console"  # 'console' or 'file'
    file_path: Optional[str] = "logs/app.log"
    max_size: int = 10  # MB
    backup_count: int = 5

class Settings(BaseModel):
    """Application settings."""
    app_name: str = "Insights Project"
    environment: str = "development"
    debug: bool = False
    logging: LoggingConfig = Field(default_factory=LoggingConfig)

    class Config:
        """Pydantic config."""
        env_file = ".env"
        env_nested_delimiter = "__"

def get_settings() -> Settings:
    """Get application settings."""
    return Settings()

def setup_logging(config: LoggingConfig) -> None:
    """Configure logging for the application.
    
    Args:
        config: Logging configuration
    """
    log_level = getattr(logging, config.level.upper(), logging.INFO)
    
    # Create logs directory if it doesn't exist
    if config.output == "file" and config.file_path:
        log_path = Path(config.file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure handlers
    handlers = []
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    handlers.append(console_handler)
    
    # File handler if configured
    if config.output == "file" and config.file_path:
        file_handler = logging.handlers.RotatingFileHandler(
            config.file_path,
            maxBytes=config.max_size * 1024 * 1024,  # Convert MB to bytes
            backupCount=config.backup_count
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Add new handlers
    for handler in handlers:
        root_logger.addHandler(handler)
    
    # Set logging level for specific loggers
    for logger_name in ['backend', 'uvicorn', 'fastapi']:
        logger = logging.getLogger(logger_name)
        logger.setLevel(log_level)
        logger.propagate = True
