@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&family=Fira+Code:wght@400;500&display=swap');

:root {
  /* Core colors */
  --primary: #3a86ff;
  --primary-light: #4361ee;
  --primary-dark: #3a0ca3;
  --secondary: #4cc9f0;
  --accent: #7209b7;
  /* UI Elements */
  --success: #06d6a0;
  --warning: #ffd166;
  --error: #ef476f;
  
  /* Background & Surface colors */
  --bg-main: #f8f9fd;
  --card-bg: #ffffff;
  --input-bg: #ffffff;
  
  /* Text colors */
  --text: #1e293b;
  --text-light: #64748b;
  --text-muted: #94a3b8;
  --text-on-primary: #ffffff;
  /* Borders */
  --border: #e2e8f0;
  --border-hover: #cbd5e1;/* Effects */
  --radius: 0.625rem;
  --radius-sm: 0.375rem;
  --radius-lg: 1rem;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 8px 20px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
  /* Transitions */
  --transition: all 0.2s ease;
  /* Tools colors */
  --tool-list-tables: #4dabf7;
  --tool-describe-table: #ffd43b;
  --tool-execute-query: #69db7c;
  --tool-thought: #7c4dff;
  /* Dimensions */
  --header-height: 70px;
  --footer-height: 90px;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-main);
  color: var(--text);line-height: 1.6;overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  letter-spacing: -0.025em;
  margin-top: 0;
}

code {
  font-family: 'Fira Code', Menlo, Monaco, Consolas, 'Courier New', monospace;
  font-size: 0.9em;
}

* {
  box-sizing: border-box;
}

button, input, textarea {
  font-family: 'Inter', sans-serif;
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}