/**
 * Utilities for safely handling complex JSON objects with circular references
 */

/**
 * Safely stringify any JavaScript object, handling circular references,
 * DOM nodes, Error objects, and other problematic types
 * 
 * @param {any} obj - The object to stringify
 * @param {number} maxDepth - Maximum depth to traverse before truncating (default: 4)
 * @param {number} indent - Number of spaces for indentation (default: 2)
 * @returns {string} A safely stringified representation of the object
 */
export const safeStringify = (obj, maxDepth = 4, indent = 2) => {
  try {
    // Handle primitive types directly
    if (obj === null || obj === undefined) return String(obj);
    if (typeof obj !== 'object' && typeof obj !== 'function') return JSON.stringify(obj);

    const seen = new WeakSet();
    
    // We need a more sophisticated depth tracking mechanism
    const depthMap = new Map();
    
    const replacer = (key, value) => {
      // Special case for the root object
      const isRoot = key === '';
      const path = isRoot ? 'root' : key;
      
      // Handle non-object types
      if (value === null || value === undefined) return value;
      // Handle special types
      if (typeof value === 'function') return '[Function]';
      if (value instanceof Error) return `[Error: ${value.message}]`;
      if (value instanceof Date) return value.toISOString();
      if (value instanceof RegExp) return value.toString();
      
      // Skip DOM nodes and window references
      if (
        // DOM node detection
        (typeof Node !== 'undefined' && value instanceof Node) ||
        // Window detection
        (typeof value === 'object' && value.window === value) ||
        // Common DOM properties that cause circular references
        key === 'ownerDocument' ||
        key === 'parentNode' ||
        key === 'parentElement'
      ) {
        return '[DOM Element]';
      }
      
      // Handle regular objects - with circular reference detection
      if (typeof value === 'object') {
        // Don't re-process objects we've already seen (circular reference)
        if (seen.has(value)) return '[Circular Reference]';
        
        // Get parent depth or default to 0 for root
        const parentDepth = isRoot ? 0 : (depthMap.get(this) || 0);
        const currentDepth = parentDepth + 1;
        
        // Check if we've reached maximum depth
        if (currentDepth > maxDepth) {
          return '[Nested Object]';
        }
        
        // Mark this object as seen
        seen.add(value);
        
        // Store depth for this object's children
        depthMap.set(value, currentDepth);
      }
      
      // For all other cases, return the value as is
      return value;
    };
    
    return JSON.stringify(obj, replacer, indent);
  } catch (err) {
    console.error("Safe stringify error:", err);
    return `[Cannot display: ${err.message}]`;
  }
};

/**
 * Attempts to parse a string as JSON, safely handling errors
 * 
 * @param {string} str - The string to parse
 * @returns {Object|null} The parsed object or null if parsing failed
 */
export const safeParse = (str) => {
  if (typeof str !== 'string') return null;
  
  try {
    return JSON.parse(str);
  } catch (err) {
    console.error("Safe parse error:", err);
    return null;
  }
};