import React, { useState, useEffect, useRef } from 'react';
import { FaRobot, FaUser, FaChevronDown, FaChevronRight, FaSearch, FaDatabase, FaCode, FaStream, FaLightbulb, FaBars, FaTimes, FaEnvelope, FaCheckSquare, FaSquare } from 'react-icons/fa';
import { IoSend } from 'react-icons/io5';
import ReactMarkdown from 'react-markdown';
import './App.css';
import { safeStringify } from './utils/safeJson';
import api from './api';

// Component for displaying code with proper formatting
const CodeBlock = ({ children, language }) => {
  return (
    <pre className="code-block">
      <code className={language ? `language-${language}` : ''}>{children}</code>
    </pre>
  );
};

// Component for displaying a thinking/loading animation
const ThinkingAnimation = () => (
  <div className="thinking">
    <div className="thinking-dots">
      <span></span>
      <span></span>
      <span></span>
    </div>
  </div>
);

// Component for displaying suggested questions
const SuggestedQuestions = ({ questions, onQuestionClick }) => {
  if (!questions || questions.length === 0) return null;

  return (
    <div className="suggested-questions">
      <div className="suggested-questions-title">Suggested follow-up questions:</div>
      <div className="suggested-questions-list">
        {questions.map((question, index) => (
          <button 
            key={index} 
            className="suggested-question"
            onClick={() => onQuestionClick(question)}
          >
            {question}
          </button>
        ))}
      </div>
    </div>
  );
};

// Component for displaying individual steps in the thought process
const Step = ({ step, index }) => {
  // Keep steps closed by default, can be expanded when needed
  const [isOpen, setIsOpen] = useState(false);
  const toggleOpen = () => setIsOpen(!isOpen);

  // Handle different tool types with appropriate icons
  const getToolIcon = () => {
    switch (step.tool) {
      case 'list_tables':
        return <FaDatabase className="tool-icon" />;
      case 'describe_table':
        return <FaSearch className="tool-icon" />;
      case 'execute_query':
        return <FaCode className="tool-icon" />;
      case 'thought':
        return <FaLightbulb className="tool-icon" />;
      default:
        return <FaStream className="tool-icon" />;
    }
  };

  return (
    <div className="step" data-tool={step.tool}>
      <div className="step-header" onClick={toggleOpen}>
        <div className="step-header-content">
          <span className="step-number">{index + 1}</span>
          <span className="step-tool">
            {getToolIcon()}
            {step.tool === 'thought' ? 'Thinking' : step.tool?.replace(/_/g, ' ')}
          </span>
        </div>
        <span className="step-toggle">
          {isOpen ? <FaChevronDown /> : <FaChevronRight />}
        </span>
      </div>
      {isOpen && (
        <div className="step-content">
          {step.tool === 'thought' ? (
            // Display thought content directly
            <div className="step-observation">{step.observation}</div>
          ) : (
            // Display tool execution details
            <>
              {step.input && (
                <div className="step-section">
                  <div className="section-header">Input</div>
                  <div className="step-input">
                    <CodeBlock language="sql">{step.input}</CodeBlock>
                  </div>
                </div>
              )}
              {step.log && (
                <div className="step-section">
                  <div className="section-header">Process</div>
                  <div className="step-log">
                    {step.log.split('\n').map((line, i) => (
                      <div key={i} className="log-line">
                        {line}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              <div className="step-section">
                <div className="section-header">Result</div>
                <div className="step-observation">
                  {typeof step.observation === 'string' ? (
                    step.observation.startsWith('{') || step.observation.startsWith('[') ? (
                      // Try to pretty print JSON
                      <CodeBlock language="json">
                        {step.observation}
                      </CodeBlock>
                    ) : (
                      // Regular text
                      <div>{step.observation}</div>
                    )
                  ) : (
                    // For non-string values, stringify them
                    <CodeBlock language="json">
                      {safeStringify(step.observation)}
                    </CodeBlock>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

// Login Modal Component
const LoginModal = ({ isOpen, onClose, onLogin, loginError }) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!email.trim()) return;

    setIsLoading(true);
    try {
      await onLogin(email);
    } catch (error) {
      console.error("Login error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="login-modal-overlay">
      <div className="login-modal">
        <h2>Welcome to Trino AI Assistant</h2>
        <p>Please enter your email to continue</p>
        <form onSubmit={handleSubmit}>
          <div className="input-group">
            <span className="input-icon">
              <FaEnvelope />
            </span>
            <input
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          {loginError && (
            <div className="error-message">{loginError}</div>
          )}
          <button
            type="submit"
            className="login-button"
            disabled={!email.trim() || isLoading}
          >
            {isLoading ? 'Verifying...' : 'Login'}
          </button>
        </form>
      </div>
    </div>
  );
};

// Format message content to clean up extra spaces and newlines
const formatMessageContent = (content) => {
  if (!content) return '';
  
  // First, normalize all line endings to LF
  let formatted = content.replace(/\r\n?/g, '\n');
  
  // Remove trailing whitespace from each line
  formatted = formatted.split('\n').map(line => line.trimEnd()).join('\n');
  
  // Replace 3+ newlines with exactly 2 newlines
  formatted = formatted.replace(/\n{3,}/g, '\n\n');
  
  // Clean up common markdown formatting issues
  formatted = formatted
    // Fix markdown list formatting
    .replace(/(\n\s*[-*+]\s+)(?=\S)/g, '\n* ')
    // Fix numbered lists
    .replace(/(\n\s*)\d+\.\s+/g, '\n1. ')
    // Clean up code blocks
    .replace(/```(\w*)\n([\s\S]*?)\n```/g, (match, lang, code) => {
      // Clean up code block content
      const cleanCode = code.trim()
        .replace(/^\s+/gm, '')  // Remove leading whitespace from each line
        .replace(/\s+$/gm, ''); // Remove trailing whitespace from each line
      return `\`\`\`${lang}\n${cleanCode}\n\`\`\``;
    });
    
  return formatted;
};

function App() {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [socket, setSocket] = useState(null);
  const [currentMessage, setCurrentMessage] = useState(null);
  const [catalogs, setCatalogs] = useState([]);
  const [schemas, setSchemas] = useState([]);
  const [selectedCatalog, setSelectedCatalog] = useState('');
  const [selectedSchema, setSelectedSchema] = useState('');
  const [isLoadingCatalogs, setIsLoadingCatalogs] = useState(false);
  const [isLoadingSchemas, setIsLoadingSchemas] = useState(false);
  const [tables, setTables] = useState([]);
  const [selectedTables, setSelectedTables] = useState([]);
  const [tableSearchTerm, setTableSearchTerm] = useState('');
  const [isLoadingTables, setIsLoadingTables] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [sessionId, setSessionId] = useState('');
  const messagesEndRef = useRef(null);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [loginError, setLoginError] = useState(null);
  const [userData, setUserData] = useState(null);
  // Ref to access userData without triggering re-renders
  const userDataRef = useRef(null);
  
  // Keep userDataRef in sync with userData
  useEffect(() => {
    userDataRef.current = userData;
  }, [userData]);

  // Check for user data in local storage and session ID on component mount
  useEffect(() => {
    // Check for user data
    const storedUserData = localStorage.getItem('textToInsightsUserData');
    if (storedUserData) {
      try {
        const parsedUserData = JSON.parse(storedUserData);
        if (parsedUserData && parsedUserData.email) {
          setUserData(parsedUserData);
          // If the stored user data has catalog/schema/table selections, use them
          if (parsedUserData.selectedCatalog) {
            setSelectedCatalog(parsedUserData.selectedCatalog);
          }
          
          if (parsedUserData.selectedSchema) {
            setSelectedSchema(parsedUserData.selectedSchema);
          }
          
          if (parsedUserData.selectedTables && Array.isArray(parsedUserData.selectedTables)) {
            setSelectedTables(parsedUserData.selectedTables);
          }
        } else {
          setShowLoginModal(true);
        }
      } catch (error) {
        console.error('Error parsing user data from local storage:', error);
        setShowLoginModal(true);
      }
    } else {
      setShowLoginModal(true);
    }
    
    // Generate or retrieve session ID
    const storedSessionId = localStorage.getItem('sessionId');
    if (storedSessionId) {
      setSessionId(storedSessionId);
    } else {
      const newSessionId = `session_${Date.now()}`;
      setSessionId(newSessionId);
      localStorage.setItem('sessionId', newSessionId);
    }
  }, []);

  // Fetch catalogs on component mount
  // Handle login function
  const handleLogin = async (email) => {
    try {
      setLoginError(null);
      const response = await api.post('/validate-email', { email });
      
      if (response.data.valid && response.data.user_data) {
        // Create user data object including the existing catalog/schema if any
        const userDataToStore = {
          ...response.data.user_data,
          email: email,
          selectedCatalog: selectedCatalog || '',
          selectedSchema: selectedSchema || ''
        };
        
        // Set user data in state
        setUserData(userDataToStore);
        
        // Save to local storage
        localStorage.setItem('textToInsightsUserData', JSON.stringify(userDataToStore));
        
        // Close the login modal
        setShowLoginModal(false);
      } else {
        setLoginError(response.data.error || 'Invalid email or user not found');
      }
    } catch (error) {
      console.error('Login error:', error);
      if (error.response) {
        setLoginError(`Error: ${error.response.status} - ${error.response.data.detail || 'Unknown error'}`);
      } else if (error.request) {
        setLoginError('No response from server. Please check your connection.');
      } else {
        setLoginError(`Error: ${error.message}`);
      }
    }
  };
  
  // Handle logout function
  const handleLogout = () => {
    setUserData(null);
    localStorage.removeItem('textToInsightsUserData');
    setShowLoginModal(true);
  };

    // Fetch catalogs on component mount
    useEffect(() => {
      const fetchCatalogs = async () => {
        // If we already have catalogs in userData and the array is not empty, use those
        const storedUserData = localStorage.getItem('textToInsightsUserData');
        if (storedUserData) {
          try {
            const parsedUserData = JSON.parse(storedUserData);
            if (parsedUserData && parsedUserData.catalogs && Array.isArray(parsedUserData.catalogs) && parsedUserData.catalogs.length > 0) {
              setCatalogs(parsedUserData.catalogs);
              
              // If there's a selectedCatalog in userData, use it
              if (parsedUserData.selectedCatalog && parsedUserData.catalogs.includes(parsedUserData.selectedCatalog)) {
                setSelectedCatalog(parsedUserData.selectedCatalog);
                return; // Skip API call if we have valid data
              }
            }
          } catch (error) {
            console.error('Error parsing catalogs from local storage:', error);
          }
        }
        
        // If we don't have valid catalogs in local storage or the selected catalog is invalid, fetch from API
        try {
          setIsLoadingCatalogs(true);
          setError(null);
          const response = await api.get('/catalogs');
          
          // Check if response.data exists and is an array
          if (Array.isArray(response.data)) {
            setCatalogs(response.data);
            
            // Auto-select the first catalog if only one exists
            if (response.data.length === 1) {
              setSelectedCatalog(response.data[0]);
            }
            
            // Update catalogs in localStorage without updating the userData state
            // This prevents an infinite loop since we don't trigger the effect again
            if (userDataRef.current) {
              const updatedUserData = {
                ...userDataRef.current,
                catalogs: response.data
              };
              localStorage.setItem('textToInsightsUserData', JSON.stringify(updatedUserData));
      // Don't call setUserData here to prevent infinite loop
              // Don't call setUserData here as it would trigger the effect again
            }
          } else {
            console.error('Unexpected catalogs format:', response.data);
            setError('Invalid response format from server');
            setCatalogs([]);
          }
        } catch (error) {
          console.error('Error fetching catalogs:', error);
          if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            console.error('Response data:', error.response.data);
            console.error('Response status:', error.response.status);
            console.error('Response headers:', error.response.headers);
            setError(`Error: ${error.response.status} - ${error.response.data.detail || 'Unknown error'}`);
          } else if (error.request) {
            // The request was made but no response was received
            console.error('No response received:', error.request);
            setError('No response from server. Please check your connection.');
          } else {
            // Something happened in setting up the request that triggered an Error
            console.error('Error setting up request:', error.message);
            setError(`Error: ${error.message}`);
          }
          setIsLoadingCatalogs(false);
        } finally {
          setIsLoadingCatalogs(false);
        }
      };

      fetchCatalogs();
    }, []); // Remove userData dependency to prevent infinite loop

  // TablesList component
  const TablesList = ({ 
    tables, 
    selectedTables, 
    onTableToggle, 
    searchTerm, 
    onSearchChange,
    isLoading
  }) => {
    const listRef = useRef(null);
    
    // Memoize filtered and sorted tables for better performance
    const filteredTables = React.useMemo(() => {
      return [...tables]
        .filter(table => 
          table.toLowerCase().includes(searchTerm.toLowerCase())
        )
        .sort((a, b) => {
          const aSelected = selectedTables.includes(a);
          const bSelected = selectedTables.includes(b);
          if (aSelected && !bSelected) return -1;
          if (!aSelected && bSelected) return 1;
          return a.localeCompare(b);
        });
    }, [tables, searchTerm, selectedTables]);

    // Auto-scroll to top when search changes
    useEffect(() => {
      if (listRef.current) {
        listRef.current.scrollTop = 0;
      }
    }, [searchTerm]);

    return (
      <div className="form-group tables-container">
        <div className="form-group-header">
          <label>
            Tables 
            {isLoading && <span className="loading-spinner" />}
            <span className="tables-count">
              ({selectedTables.length} selected{filteredTables.length !== tables.length ? `, ${filteredTables.length} filtered` : ''})
            </span>
          </label>
        </div>
        <div className="search-container">
          <FaSearch className="search-icon" />
          <input
            type="text"
            placeholder="Search tables..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="search-input"
            disabled={isLoading}
          />
          {searchTerm && (
            <button 
              className="clear-search"
              onClick={() => onSearchChange('')}
              aria-label="Clear search"
              disabled={isLoading}
            >
              ×
            </button>
          )}
        </div>
        <div className="tables-list" ref={listRef}>
          {filteredTables.length > 0 ? (
            filteredTables.map((table) => {
              const isSelected = selectedTables.includes(table);
              return (
                <div 
                  key={table} 
                  className={`table-item ${isSelected ? 'selected' : ''}`}
                  onClick={() => onTableToggle(table)}
                  title={table}
                >
                  {isSelected ? <FaCheckSquare /> : <FaSquare />}
                  <span className="table-name">{table}</span>
                </div>
              );
            })
          ) : (
            <div className="no-tables">
              {searchTerm ? 'No tables match your search' : 'No tables available in this schema'}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Fetch tables when schema is selected
  useEffect(() => {
    const fetchTables = async () => {
      if (!selectedCatalog || !selectedSchema) {
        setTables([]);
        setSelectedTables([]);
        return;
      }

      try {
        setIsLoadingTables(true);
        const response = await api.get(
          `/tables?catalog=${encodeURIComponent(selectedCatalog)}&schema=${encodeURIComponent(selectedSchema)}`
        );
        
        if (Array.isArray(response.data)) {
          setTables(response.data);
          // Keep only the selected tables that still exist in the new list
          setSelectedTables(prev => 
            prev.filter(table => response.data.includes(table))
          );
        } else {
          setTables([]);
          setSelectedTables([]);
        }
      } catch (error) {
        console.error('Error fetching tables:', error);
        setTables([]);
        setSelectedTables([]);
      } finally {
        setIsLoadingTables(false);
      }
    };

    fetchTables();
  }, [selectedCatalog, selectedSchema]);

  // Fetch schemas when catalog is selected
  useEffect(() => {
    const fetchSchemas = async () => {
      if (!selectedCatalog) {
        setSchemas([]);
        setSelectedSchema('');
        return;
      }
      
      // Check if we already have schemas for this catalog in userData
      const storedUserData = localStorage.getItem('textToInsightsUserData');
      if (storedUserData) {
        try {
          const parsedUserData = JSON.parse(storedUserData);
          if (
            parsedUserData &&
            parsedUserData.schemas &&
            parsedUserData.selectedCatalog === selectedCatalog &&
            Array.isArray(parsedUserData.schemas) &&
            parsedUserData.schemas.length > 0
          ) {
            setSchemas(parsedUserData.schemas);
            
            // If there's a selectedSchema in userData and it's valid, use it
            if (parsedUserData.selectedSchema && parsedUserData.schemas.includes(parsedUserData.selectedSchema)) {
              setSelectedSchema(parsedUserData.selectedSchema);
              return; // Skip API call if we have valid data
            }
          }
        } catch (error) {
          console.error('Error parsing schemas from local storage:', error);
        }
      }
      
      // If we don't have valid schemas in local storage or the selected schema is invalid, fetch from API
      try {
        setIsLoadingSchemas(true);
        setError(null);
        
        const response = await api.get(`/schemas?catalog=${encodeURIComponent(selectedCatalog)}`);
        
        if (Array.isArray(response.data)) {
          
          setSchemas(response.data);
          // Auto-select the first schema if only one exists
          if (response.data.length === 1) {
            setSelectedSchema(response.data[0]);
                    } else {
                      setSelectedSchema('');
                    }
                    // Update schemas in localStorage without updating userData state
          if (userDataRef.current) {
            const updatedUserData = {
              ...userDataRef.current,
              schemas: response.data,
              selectedCatalog: selectedCatalog
            };
            localStorage.setItem('textToInsightsUserData', JSON.stringify(updatedUserData));
      // Don't call setUserData here to prevent infinite loop
            // Don't call setUserData here to prevent infinite loop
          }
        } else {
          console.error('Unexpected schemas format:', response.data);
          setError('Invalid response format from server');
          setSchemas([]);
          setSelectedSchema('');
        }
      } catch (error) {
        console.error('Error fetching schemas:', error);
        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.error('Response data:', error.response.data);
          console.error('Response status:', error.response.status);
          console.error('Response headers:', error.response.headers);
          setError(`Error: ${error.response.status} - ${error.response.data.detail || 'Failed to load schemas'}`);
        } else if (error.request) {
          // The request was made but no response was received
          console.error('No response received:', error.request);
          setError('No response from server. Please check your connection.');
        } else {
          // Something happened in setting up the request that triggered an Error
          console.error('Error setting up request:', error.message);
          setError(`Error: ${error.message}`);
        }
        setSchemas([]);
        setSelectedSchema('');
      } finally {
        setIsLoadingSchemas(false);
      }
    };

    fetchSchemas();
  }, [selectedCatalog]); // Remove userData dependency to prevent infinite loop

  // Handle WebSocket messages
  const handleWebSocketMessage = (event) => {
    try {
      const data = JSON.parse(event.data);

      // Update session ID if provided by server
      if (data.session_id) {
        setSessionId(data.session_id);
        localStorage.setItem('sessionId', data.session_id);
      }

      // Handle different message types from the server
      switch (data.type) {
        case 'status':
          // Status messages indicate processing has started
          break;

        case 'step':
          // Step messages contain intermediate steps
          setCurrentMessage(prev => {
            const currentState = prev || { role: 'assistant', content: '', steps: [] };
            const updatedMessage = {
              ...currentState,
              steps: [...(currentState.steps || []), data.content]
            };
            return updatedMessage;
          });
          break;

        case 'answer':
          // Use a function to ensure we get the latest state
          setCurrentMessage(prev => {
            const currentSteps = prev?.steps || [];
            
            // Create new message with content and preserved steps
            const newMessage = {
              role: 'assistant',
              content: formatMessageContent(data.content),
              steps: [...currentSteps],
              suggested_questions: data.suggested_questions || []
            };

            
            // Update messages in a single operation
            setMessages(messages => {
              const lastMessage = messages[messages.length - 1];
              const updatedMessages = lastMessage && lastMessage.role === 'assistant' && !lastMessage.content
                ? [...messages.slice(0, -1), newMessage]
                : [...messages, newMessage];
              
              return updatedMessages;
            });
            
            setIsLoading(false);
            
            // Return null to clear the current message
            return null;
          });
          break;

        case 'error':
          // Error message from server
          setError(data.error || 'An error occurred');
          setIsLoading(false);
          setCurrentMessage(null);
          break;

        case 'completion':
          // Completion message indicates the full response is sent
          setIsLoading(false);
          break;

        default:
          // Unknown message type
          break;
      }
    } catch (err) {
      setError('Error processing response');
      setIsLoading(false);
    }
  };

  // Connect to WebSocket on component mount
  useEffect(() => {
    let reconnectAttempts = 0;
    let socketInstance = null;

    const connectWebSocket = () => {
      // Determine WebSocket protocol (ws or wss) based on current protocol
      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      
      // Use the environment variable for WebSocket URL, or construct it from current host
      const wsUrl = process.env.REACT_APP_WS_URL || 
                   `${wsProtocol}//${window.location.host}${process.env.PUBLIC_URL || ''}/ws/query`;
      
      // const wsUrl = process.env.REACT_APP_WS_URL || 
      // `${wsProtocol}//${window.location.host}${process.env.PUBLIC_URL || ''}/ws/query`;
      

      console.log('Connecting to WebSocket:', wsUrl);
      socketInstance = new WebSocket(wsUrl);

      socketInstance.onopen = () => {
        setSocket(socketInstance);
        setError(null);
        reconnectAttempts = 0;
      };

      socketInstance.onclose = () => {
        if (reconnectAttempts < 5) { // Limit reconnection attempts
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // Exponential backoff
          setTimeout(connectWebSocket, delay);
          reconnectAttempts++;
        } else {
          setError('Connection lost. Please refresh the page to reconnect.');
        }
      };

      socketInstance.onerror = () => {
        setError('Connection error. Please try again later.');
      };

      socketInstance.onmessage = handleWebSocketMessage;

      setSocket(socketInstance);
    };

    connectWebSocket();

    // Cleanup on component unmount
    return () => {
      if (socketInstance) {
        socketInstance.onclose = null; // Disable onclose handler
        socketInstance.close();
      }
    };
  }, []);

  // Scroll to bottom whenever messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages, currentMessage]);

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (!input.trim() || isLoading) return;

    // Add user message to chat
    const userMessage = {
      role: 'user',
      content: input
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);
    setError(null);

    // Send message to WebSocket if connected
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify({
        type: 'query',
        query: input,
        catalog: selectedCatalog || null,
        schema: selectedSchema || null,
        tables: selectedTables || null,
        session_id: sessionId,
        slack_id: userData?.slack_id || null
      }));
    } else {
      setError('Connection not established. Please try again.');
      setIsLoading(false);
    }
  };

  // Start a new conversation
  const startNewChat = () => {
    const newSessionId = `session_${Date.now()}`;
    setSessionId(newSessionId);
    localStorage.setItem('sessionId', newSessionId);
    setMessages([]);
    setCurrentMessage(null);
    setError(null);
    
    // Notify the server about the new session
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify({
        type: 'clear_history',
        session_id: newSessionId
      }));
    }
  };

  // Handle click on example question
  const handleExampleClick = (question) => {
    setInput(question);
  };

  // Scroll to bottom of message list
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Update local storage when selectedSchema or selectedTables change
  useEffect(() => {
    if (userDataRef.current) {
      const updatedUserData = {
        ...userDataRef.current,
        selectedSchema: selectedSchema,
        selectedTables: selectedTables
      };
      localStorage.setItem('textToInsightsUserData', JSON.stringify(updatedUserData));
      // Don't call setUserData here to prevent infinite loop
    }
  }, [selectedSchema, selectedTables]); // Remove userData dependency to prevent infinite loop

  const clearContext = () => {
    setSelectedCatalog('');
    setSelectedSchema('');
    setSchemas([]);
    setTables([]);
    setSelectedTables([]);
    // Also update in localStorage if userData exists
    if (userDataRef.current) {
      const updatedUserData = {
        ...userDataRef.current,
        selectedCatalog: '',
        selectedSchema: '',
        selectedTables: []
      };
      localStorage.setItem('textToInsightsUserData', JSON.stringify(updatedUserData));
      // Don't call setUserData here to prevent infinite loop
    }
  };

  // Handle table selection toggle
  const handleTableToggle = (table) => {
    setSelectedTables(prev => 
      prev.includes(table)
        ? prev.filter(t => t !== table)
        : [...prev, table]
    );
  };

  return (
    <div className={`app ${isSidebarOpen ? 'sidebar-open' : ''}`}>
      <div className="sidebar">
        <div className="sidebar-header">
          <h3>Database Explorer</h3>
          <button className="sidebar-close" onClick={toggleSidebar} aria-label="Close sidebar">
            <FaTimes />
          </button>
        </div>

        <div className="form-group">
          <label htmlFor="catalog-select">Catalog {isLoadingCatalogs && <span className="spinner"></span>}</label>
          <select
            id="catalog-select"
            className="form-control"
            value={selectedCatalog}
            onChange={(e) => {
              const newCatalog = e.target.value;
              setSelectedCatalog(newCatalog);
              
              // Update in localStorage if userData exists
              if (userDataRef.current) {
                const updatedUserData = {
                  ...userDataRef.current,
                  selectedCatalog: newCatalog
                };
                localStorage.setItem('textToInsightsUserData', JSON.stringify(updatedUserData));
      // Don't call setUserData here to prevent infinite loop
              }
            }}
            disabled={isLoadingCatalogs}
            aria-busy={isLoadingCatalogs}
            aria-label="Select a catalog"
          >
            <option value="">Select a catalog</option>
            {catalogs.length > 0 ? (
              catalogs.map(catalog => (
                <option key={catalog} value={catalog}>
                  {catalog}
                </option>
              ))
            ) : (
              !isLoadingCatalogs && <option value="" disabled>No catalogs available</option>
            )}
          </select>
          {isLoadingCatalogs && (
            <div className="loading-text">Loading catalogs...</div>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="schema-select">Schema {isLoadingSchemas && <span className="spinner"></span>}</label>
          <select
            id="schema-select"
            className="form-control"
            value={selectedSchema}
            onChange={(e) => {
              const newSchema = e.target.value;
              setSelectedSchema(newSchema);
              
              // Update in localStorage if userData exists
              if (userDataRef.current) {
                const updatedUserData = {
                  ...userDataRef.current,
                  selectedSchema: newSchema
                };
                localStorage.setItem('textToInsightsUserData', JSON.stringify(updatedUserData));
      // Don't call setUserData here to prevent infinite loop
              }
            }}
            disabled={!selectedCatalog || isLoadingSchemas}
            aria-busy={isLoadingSchemas}
            aria-label="Select a schema"
          >
            <option value="">Select a schema</option>
            {schemas.length > 0 ? (
              schemas.map(schema => (
                <option key={schema} value={schema}>
                  {schema}
                </option>
              ))
            ) : selectedCatalog ? (
              !isLoadingSchemas && <option value="" disabled>No schemas available</option>
            ) : (
              <option value="" disabled>Select a catalog first</option>
            )}
          </select>
          {isLoadingSchemas && (
            <div className="loading-text">Loading schemas...</div>
          )}
        </div>

        <div className="selected-context">
          {selectedCatalog && selectedSchema && (
            <div className="context-badge">
              <span>{selectedCatalog}.{selectedSchema}</span>
              <button 
                onClick={clearContext}
                className="clear-context"
                aria-label="Clear selection"
                title="Clear selection"
              >
                <FaTimes />
              </button>
            </div>
          )}
          {!selectedCatalog && !selectedSchema && (
            <div className="context-hint">
              Select a catalog and schema to filter your queries
            </div>
          )}
        </div>

        {selectedCatalog && selectedSchema && (
          <TablesList 
            tables={tables}
            selectedTables={selectedTables}
            onTableToggle={handleTableToggle}
            searchTerm={tableSearchTerm}
            onSearchChange={setTableSearchTerm}
            isLoading={isLoadingTables}
          />
        )}
      </div>

      <div className="main-content">
        <header className="app-header">
          <button 
            className="sidebar-toggle" 
            onClick={toggleSidebar}
            aria-label={isSidebarOpen ? 'Close sidebar' : 'Open sidebar'}
            aria-expanded={isSidebarOpen}
            aria-controls="sidebar"
          >
            {isSidebarOpen ? <FaTimes /> : <FaBars />}
          </button>
          <h1>Trino AI Assistant</h1>
          <div className="header-actions">
            {userData && (
              <div className="user-info">
                <span className="user-name">{userData.first_name || userData.email}</span>
                <button onClick={handleLogout} className="logout-button">Logout</button>
              </div>
            )}
            {selectedCatalog && selectedSchema && (
              <div className="current-context">
                <FaDatabase className="context-icon" />
                <span>{selectedCatalog}.{selectedSchema}</span>
              </div>
            )}
            <div className="session-info">
              <span className="session-id">Session: {sessionId.slice(-8)}</span>
              <button 
                onClick={startNewChat} 
                className="new-chat-button"
                title="Start a new conversation"
              >
                New Chat
              </button>
            </div>
          </div>
        </header>

        <main className="chat-container">
          {error && (
            <div className="error-banner">{error}</div>
          )}
          {/* {selectedCatalog && selectedSchema && (
            <div className="context-banner">
              Using: <strong>{selectedCatalog}.{selectedSchema}</strong>
            </div>
          )} */}
          <div className="messages">
            {/* Show empty state if no messages */}
            {messages.length === 0 && !currentMessage && (
              <div className="empty-state">
                <div className="empty-state-content">
                  <h4>Get Started with these examples</h4>
                  <ul className="example-questions">
                    {['Give week on week trend of order count', 'Tell me the most famous Electronic product on blinkit', 'Get me the top 5 orders by value for yesterday', "What's the average purchase amount by region?"].map((q, i) => (
                      <li key={i} onClick={() => handleExampleClick(q)}>{q}</li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
            {/* Display all messages */}
            {messages.map((message, index) => (
              <div
                key={index}
                className={`message ${message.role === 'assistant' ? 'assistant' : 'user'}`}
              >
                <div className="message-avatar">
                  <div className={`avatar ${message.role}`}>
                    {message.role === 'assistant' ? <FaRobot /> : <FaUser />}
                  </div>
                </div>
                <div className="message-content">
                  <div className="message-text">
                    <ReactMarkdown 
                      components={{
                        code: ({ node, inline, className, children, ...props }) => {
                          const match = /language-(\w+)/.exec(className || '');
                          return !inline ? (
                            <CodeBlock language={match ? match[1] : null}>
                              {String(children).replace(/\n$/, '')}
                            </CodeBlock>
                          ) : (
                            <code className={className} {...props}>
                              {children}
                            </code>
                          );
                        },
                        p: ({ node, ...props }) => <p style={{ margin: '0.5em 0' }} {...props} />,
                        ul: ({ node, ...props }) => <ul style={{ margin: '0.5em 0', paddingLeft: '1.5em' }} {...props} />,
                        ol: ({ node, ...props }) => <ol style={{ margin: '0.5em 0', paddingLeft: '1.5em' }} {...props} />,
                        li: ({ node, ...props }) => <li style={{ margin: '0.25em 0' }} {...props} />,
                        blockquote: ({ node, ...props }) => (
                          <blockquote style={{
                            margin: '0.75em 0', 
                            padding: '0.5em 1em',
                            borderLeft: '3px solid #ddd',
                            color: '#666',
                            fontStyle: 'italic'
                          }} {...props} />
                        )
                      }}
                    >
                      {formatMessageContent(message.content)}
                    </ReactMarkdown>
                    {message.role === 'assistant' && message.suggested_questions && (
                      <SuggestedQuestions 
                        questions={message.suggested_questions} 
                        onQuestionClick={(question) => {
                          setInput(question);
                          // Auto-submit the question if the user clicks on it
                          setTimeout(() => {
                            document.querySelector('.input-form button[type="submit"]')?.click();
                          }, 50);
                        }}
                      />
                    )}
                  </div>

                  {/* Show intermediate steps for assistant messages */}
                  {message.role === 'assistant' && message.steps && message.steps.length > 0 ? (
                    <div className="steps-container">
                      <h3 className="thought-process-title">Thought Process: ({message.steps.length} steps)</h3>
                      {message.steps.map((step, stepIndex) => (
                        <Step key={stepIndex} step={step} index={stepIndex} />
                      ))}
                    </div>
                  ) : message.role === 'assistant' ? (
                    <div className="no-steps-message">No thought process steps available</div>
                  ) : null}
                </div>
              </div>
            ))}
            {/* Show currently generating message */}
            {currentMessage && (
              <div className="message assistant">
                <div className="message-avatar">
                  <div className="avatar assistant">
                    <FaRobot />
                  </div>
                </div>
                <div className="message-content">
                  {currentMessage.content ? (
                    <div className="message-text">
                      <ReactMarkdown 
                      components={{
                        code: ({ node, inline, className, children, ...props }) => {
                          const match = /language-(\w+)/.exec(className || '');
                          return !inline ? (
                            <CodeBlock language={match ? match[1] : null}>
                              {String(children).replace(/\n$/, '')}
                            </CodeBlock>
                          ) : (
                            <code className={className} {...props}>
                              {children}
                            </code>
                          );
                        },
                        p: ({ node, ...props }) => <p style={{ margin: '0.5em 0' }} {...props} />,
                        ul: ({ node, ...props }) => <ul style={{ margin: '0.5em 0', paddingLeft: '1.5em' }} {...props} />,
                        ol: ({ node, ...props }) => <ol style={{ margin: '0.5em 0', paddingLeft: '1.5em' }} {...props} />,
                        li: ({ node, ...props }) => <li style={{ margin: '0.25em 0' }} {...props} />,
                        blockquote: ({ node, ...props }) => (
                          <blockquote style={{
                            margin: '0.75em 0', 
                            padding: '0.5em 1em',
                            borderLeft: '3px solid #ddd',
                            color: '#666',
                            fontStyle: 'italic'
                          }} {...props} />
                        )
                      }}
                    >
                      {formatMessageContent(currentMessage.content)}
                    </ReactMarkdown>
                    </div>
                  ) : (
                    <ThinkingAnimation />
                  )}
                  {/* Show intermediate steps being generated */}
                  {currentMessage.steps && currentMessage.steps.length > 0 ? (
                    <div className="steps-container">
                      <h3 className="thought-process-title">Thought Process: ({currentMessage.steps.length} steps)</h3>
                      {currentMessage.steps.map((step, stepIndex) => (
                        <Step key={stepIndex} step={step} index={stepIndex} />
                      ))}
                    </div>
                  ) : (
                    <div>Waiting for thought process steps...</div>
                  )}
                </div>
              </div>
            )}
            {/* Loading indicator when waiting for response */}
            {isLoading && !currentMessage && (
              <div className="message assistant">
                <div className="message-avatar">
                  <div className="avatar assistant">
                    <FaRobot />
                  </div>
                </div>
                <div className="message-content">
                  <ThinkingAnimation />
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
          <div className="input-container">
            <form onSubmit={handleSubmit} className="input-form">
              <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Ask a question about your database..."
                disabled={isLoading}
              />
              <button type="submit" disabled={!input.trim() || isLoading}>
                {isLoading ? (
                  <div className="spinner"></div>
                ) : (
                  <IoSend />
                )}
              </button>
            </form>
            <div className="input-hint">
              Press Enter to submit
            </div>
          </div>
        </main>
      </div>
      
      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onLogin={handleLogin}
        loginError={loginError}
      />
    </div>
  );
}

export default App;