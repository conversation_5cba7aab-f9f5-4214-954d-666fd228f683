# Lang<PERSON>hain Trino Agent - Full Stack Demo

This project is a full stack implementation of a LangChain Trino Agent that allows users to interact with a Trino database using natural language. The application provides a chat interface for querying the database, and it displays the agent's thought process in a collapsible component.

## Project Structure

```
src/
├── backend/              # FastAPI backend server
│   ├── app/              # Application code
│   │   ├── __init__.py
│   │   ├── agent.py      # Lang<PERSON>hain Trino Agent
│   │   └── main.py       # FastAPI app with routes
│   └── requirements.txt  # Python dependencies
│
└── frontend/             # React frontend
    ├── public/           # Static assets
    │   ├── index.html
    │   └── manifest.json
    └── src/# React source code
        ├── App.js        # Main React component
        ├── App.css       # Styling
        ├── index.js# React entry point
        └── index.css     # Global styles
```

## Features

- Chat interface for natural language queries
- WebSocket connection for streaming responses
- Visualization of the agent's thought process
- Collapsible component to see intermediate steps
- Docker-based setup for easy deployment

## Setup and Installation

1. Make sure you have Docker and Docker Compose installed on your system
2. Copy `.env.example` to `.env` and fill in your AWS credentials
3. Start the services:

```bash
docker-compose up
```

4. Open your browser to http://localhost:3000

## Environment Variables

- `AWS_ACCESS_KEY_ID`: AWS credentials for Bedrock access
- `AWS_SECRET_ACCESS_KEY`: AWS credentials for Bedrock access
- `AWS_REGION`: AWS region (e.g., ap-southeast-1)
- `BEDROCK_MODEL_ID`: The Bedrock model ID to use (e.g., anthropic.claude-3-5-sonnet-20240620-v1:0)
- `TRINO_CONNECTION_STRING`: Connection string for the Trino database

## Usage Examples

You can ask questions like:

- "What tables are available in the database?"
- "Show me the schema of the customers table"
- "Get me the top 5 orders by value"
- "What's the average purchase amount by region?"

## Development

To run the services separately for development:

### Backend

```bash
cd src/backend
pip install -r requirements.txt
export .env file
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend

```bash
cd src/frontend
npm install
export REACT_APP_API_BASE_URL=http://localhost:8000
export REACT_APP_WS_URL=http://localhost:8000/ws/query
export REACT_APP_API_URL=http://localhost:8000/api
npm start