# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
FROM python:3.10-slim

ARG SUPERSET_BASE_URL
ARG SUPERSET_USERNAME
ARG SUPERSET_PASSWORD

ENV SUPERSET_BASE_URL=${SUPERSET_BASE_URL} \
    SUPERSET_USERNAME=${SUPERSET_USERNAME} \
    SUPERSET_PASSWORD=${SUPERSET_PASSWORD}

# Set working directory
WORKDIR /app

# Install system dependencies if required
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy project files
COPY . /app

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir .

# Expose port if needed (optional, as MCP communicates over stdio typically)
EXPOSE 8001

# Set default command to run the MCP server
CMD ["python", "main.py"]