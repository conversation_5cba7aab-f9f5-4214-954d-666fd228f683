version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: docker/frontend/Dockerfile
    container_name: frontend
    ports:
      - "80:80"
    environment:
      - REACT_APP_API_URL=http://backend:8000
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN:-}
    depends_on:
      - backend
    networks:
      - langchain-network
    restart: unless-stopped

  backend:
    build:
      context: .
      dockerfile: docker/backend/Dockerfile
    container_name: backend
    ports:
      - "8000:8000"
    environment:
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN:-}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - BEDROCK_MODEL_ID=${BEDROCK_MODEL_ID}
      - TRINO_HOST=${TRINO_HOST}
      - TRINO_PORT=${TRINO_PORT}
      - TRINO_USER=${TRINO_USER}
      - TRINO_CATALOG=${TRINO_CATALOG}
      - TRINO_SCHEMA=${TRINO_SCHEMA}
    volumes:
      - ./src/backend:/app
    networks:
      - langchain-network
    restart: unless-stopped

networks:
  langchain-network:
    driver: bridge