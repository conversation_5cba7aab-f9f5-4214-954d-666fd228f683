# Insights Project Architecture

## Project Structure

```
src/
├── backend/                  # Backend application code
│   ├── app/                 # Main application package
│   │   ├── __init__.py
│   │   ├── agent.py         # Main agent implementation
│   │   ├── base_agent.py    # Base agent class
│   │   └── main.py          # FastAPI application
│   ├── config/              # Configuration management
│   │   ├── __init__.py
│   │   └── settings.py      # Application settings
│   └── utils/               # Utility functions
│       └── __init__.py
├── frontend/                # Frontend application code
│   └── ...
├── tests/                   # Test files
├── .env                    # Environment variables (gitignored)
├── .env.example            # Example environment variables
├── README.md               # Project documentation
├── requirements.txt        # Production dependencies
└── requirements-dev.txt    # Development dependencies
```

## Key Components

### Backend

- **FastAPI**: Modern, fast web framework for building APIs
- **Pydantic**: Data validation and settings management
- **Structured Logging**: Configurable logging with rotation
- **Modular Design**: Easy to extend with new features

### Configuration

Configuration is managed through environment variables and the `config` module:
- Environment variables take precedence
- `.env` file for local development
- Type-safe settings with Pydantic

### Logging

Logging is configured in `config/settings.py` and supports:
- Multiple log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Console and file output
- Log rotation based on file size

## Development Workflow

1. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt
   ```

3. Set up pre-commit hooks:
   ```bash
   pre-commit install
   ```

4. Run tests:
   ```bash
   pytest
   ```

5. Start the development server:
   ```bash
   uvicorn backend.app.main:app --reload
   ```

## Deployment

### Production

1. Set environment variables in production
2. Install production dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run with a production ASGI server like Uvicorn with Gunicorn:
   ```bash
   gunicorn -w 4 -k uvicorn.workers.UvicornWorker backend.app.main:app
   ```

## Monitoring

- Application logs are written to `logs/app.log`
- Health check endpoint at `/health`
- API documentation available at `/docs`
