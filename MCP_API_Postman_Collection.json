{"info": {"name": "MCP API Collection", "description": "Collection for testing MCP (Model Context Protocol) API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8081", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check if the MCP API server is healthy and get basic stats"}}, {"name": "MCP Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/mcp/status", "host": ["{{base_url}}"], "path": ["mcp", "status"]}, "description": "Get detailed status of all MCP servers and tools"}}, {"name": "List All Tools", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/mcp/tools", "host": ["{{base_url}}"], "path": ["mcp", "tools"]}, "description": "List all available MCP tools with their parameters"}}, {"name": "Get AI Gateway Tools", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/mcp/tools/ai-gateway", "host": ["{{base_url}}"], "path": ["mcp", "tools", "ai-gateway"]}, "description": "Get tools formatted for AI Gateway integration"}}, {"name": "Get Configuration", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/mcp/config", "host": ["{{base_url}}"], "path": ["mcp", "config"]}, "description": "Get current MCP server configuration"}}, {"name": "Call Tool - Get Databases", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tool_name\": \"rsuperset:get_databases\",\n  \"arguments\": {}\n}"}, "url": {"raw": "{{base_url}}/mcp/tools/call", "host": ["{{base_url}}"], "path": ["mcp", "tools", "call"]}, "description": "Call the get_databases tool to retrieve all Superset databases"}}, {"name": "Call Tool - Get Datasets", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tool_name\": \"rsuperset:get_datasets\",\n  \"arguments\": {\n    \"database_id\": 1\n  }\n}"}, "url": {"raw": "{{base_url}}/mcp/tools/call", "host": ["{{base_url}}"], "path": ["mcp", "tools", "call"]}, "description": "Call the get_datasets tool with optional database_id parameter"}}, {"name": "Call Tool - Get Charts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tool_name\": \"rsuperset:get_charts\",\n  \"arguments\": {\n    \"dataset_id\": 1\n  }\n}"}, "url": {"raw": "{{base_url}}/mcp/tools/call", "host": ["{{base_url}}"], "path": ["mcp", "tools", "call"]}, "description": "Call the get_charts tool with optional dataset_id parameter"}}, {"name": "Call Tool - Get Dashboards", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tool_name\": \"rsuperset:get_dashboards\",\n  \"arguments\": {}\n}"}, "url": {"raw": "{{base_url}}/mcp/tools/call", "host": ["{{base_url}}"], "path": ["mcp", "tools", "call"]}, "description": "Call the get_dashboards tool to retrieve all Superset dashboards"}}, {"name": "Call Tool - Execute SQL", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tool_name\": \"rsuperset:execute_sql\",\n  \"arguments\": {\n    \"database_id\": 1,\n    \"sql\": \"SELECT COUNT(*) as total_users FROM users\"\n  }\n}"}, "url": {"raw": "{{base_url}}/mcp/tools/call", "host": ["{{base_url}}"], "path": ["mcp", "tools", "call"]}, "description": "Execute a SQL query on a Superset database"}}, {"name": "Call Tool - Create Chart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tool_name\": \"rsuperset:create_chart\",\n  \"arguments\": {\n    \"dataset_id\": 1,\n    \"chart_name\": \"User Count Chart\",\n    \"viz_type\": \"bar\"\n  }\n}"}, "url": {"raw": "{{base_url}}/mcp/tools/call", "host": ["{{base_url}}"], "path": ["mcp", "tools", "call"]}, "description": "Create a new chart in Superset"}}, {"name": "Stream Tool Call - Get Databases", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tool_name\": \"rsuperset:get_databases\",\n  \"arguments\": {}\n}"}, "url": {"raw": "{{base_url}}/mcp/tools/call/stream", "host": ["{{base_url}}"], "path": ["mcp", "tools", "call", "stream"]}, "description": "Call tool with streaming response (Server-Sent Events)"}}, {"name": "Restart Server", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/mcp/servers/rsuperset/restart", "host": ["{{base_url}}"], "path": ["mcp", "servers", "rsuperset", "restart"]}, "description": "Restart a specific MCP server"}}]}