# Local Development Setup for AI Insights Project

This guide will help you set up and run the AI Insights project with <PERSON><PERSON><PERSON>n Trino Agent locally.

## Prerequisites

- Python 3.9 or above
- Docker and <PERSON>er Compose (for Redis, PostgreSQL and optionally Trino)
- AWS credentials with access to Bedrock (for LLM)

## Step 1: Setting up Trino

You have two options for setting up Trino:

### Option A: Using Docker

1. Create a `docker-compose.yml` file in the project root with the following content:

```yaml
version: '3'
services:
  # Existing services like PostgreSQL and Redis
  postgres:
    image: postgres:15
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgresPOSTGRES_DB: insights
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data

  redis:
    image: redis:7
    ports:
      - "6379:6379"# Add Trino service
  trino:
    image: trinodb/trino:latest
    ports:
      - "8080:8080"
    volumes:
      - ./trino-config:/etc/trino
    environment:
      - TRINO_LOG_LEVEL=INFO

volumes:
  postgres-data:
```

2. Create a `trino-config` directory in the project root with required configuration files:

```bash
mkdir -p trino-config/catalog
```

3. Create `trino-config/catalog/memory.properties`:

```properties
connector.name=memory
memory.max-data-per-node=128MB
```

4. Start the Docker services:

```bash
docker-compose up -d
```

### Option B: Using an existing Trino Server

If you already have a Trino server, update your `.env` file with the appropriate settings.

## Step 2: Configure the environment

1. Make sure you have the latest `.env` file with the Trino-specific settings:

```
# Trino Configuration
TRINO_HOST=localhost
TRINO_PORT=8080
TRINO_USER=trino
# TRINO_PASSWORD=  # Uncomment and set if authentication is needed
TRINO_HTTP_SCHEME=http
TRINO_CATALOG=memory
TRINO_SCHEMA=default
```

2. Update AWS credentials for Bedrock:

```
AWS_REGION=ap-southeast-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
# AWS_SESSION_TOKEN=  # If using temporary credentials

BEDROCK_MODEL_ID=anthropic.claude-3-5-sonnet-20240620-v1:0
```

## Step 3: Install dependencies

1. Create and activate a virtual environment:

```bash
python -m venv venv
source venv/bin/activate# On Windows: venv\Scripts\activate
```

2. Install the dependencies:

```bash
pip install -r src/backend/requirements.txt
```

## Step 4: Initialize the database

1. Run the database setup script:

```bash
python src/database/setup_db.py
```

## Step 5: Run the application

1. Start the FastAPI server:

```bash
cd src/backend
uvicorn main:app --reload
```

2. The API should now be available at `http://localhost:8000`

## Step 6: Add a test database connection

You can add a database connection through the API or directly to the database:

1. Using the API:

```bash
curl -X POST "http://localhost:8000/api/v1/connections" \
  -H "Content-Type: application/json" \
  -d '{
    "connection_name": "Local Trino",
    "connection_type": "trino",
    "connection_string": "trino://trino@localhost:8080/memory/default",
    "is_active": true
  }'
```

2. Or access the PostgreSQL database and insert directly:

```sql
INSERT INTO database_connections (connection_name, connection_type, connection_string, is_active)
VALUES ('Local Trino', 'trino', 'trino://trino@localhost:8080/memory/default', true);
```

## Step 7: Test the Trino agent

1. Insert sample data into Trino memory connector:

```bash
docker exec -it $(docker ps -q -f name=trino) trino --server localhost:8080 --catalog memory --schema default
```

Within the Trino CLI:

```sql
CREATE TABLE users (
  id INTEGER,
  name VARCHAR,
  email VARCHAR,
  signup_date DATE
);

INSERT INTO users VALUES
  (1, 'Alice', '<EMAIL>', DATE '2023-01-15'),
  (2, 'Bob', '<EMAIL>', DATE '2023-02-20'),
  (3, 'Charlie', '<EMAIL>', DATE '2023-03-25'),
  (4, 'Dana', '<EMAIL>', DATE '2023-04-10');

CREATE TABLE orders (
  order_id INTEGER,
  user_id INTEGER,
  product VARCHAR,
  price DECIMAL(10, 2),
  order_date DATE
);

INSERT INTO orders VALUES
  (101, 1, 'Laptop', 999.99, DATE '2023-03-01'),
  (102, 2, 'Phone', 699.99, DATE '2023-03-15'),
  (103, 3, 'Headphones', 149.99, DATE '2023-04-02'),
  (104, 1, 'Monitor', 299.99, DATE '2023-04-10'),
  (105, 2, 'Keyboard', 59.99, DATE '2023-04-20');
```

2. Test the natural language query API:

```bash
curl -X POST "http://localhost:8000/api/v1/chat/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "How many usersdo we have and what is their average order value?",
    "connection_id": "1"  # Replace with the actual ID of your connection
  }'
```

## Troubleshooting

### Connection Issues

If you have trouble connecting to Trino:

1. Verify Trino is running:
   ```bash
   docker ps | grep trino
   ```

2. Check Trino logs:
   ```bash
   docker logs $(docker ps -q -f name=trino)
   ```

3. Test direct connection:
   ```bash
   docker exec -it $(docker ps -q -f name=trino) trino --server localhost:8080 --catalog memory --schema default
   ```

### AWS Credentials Issues

If you have trouble with AWS Bedrock:

1. Verify your credentials with:
   ```bash
   aws sts get-caller-identity
   ```

2. Make sure your AWS user has access to Bedrock services.

3. Check that the model ID is available in your AWS region.