# MCP API Documentation

## Overview

The MCP (Model Context Protocol) API provides a generic, configuration-driven backend for integrating any MCP server with web applications. It supports both subprocess and HTTP-based MCP servers with streaming capabilities.

## Features

- ✅ **Generic Configuration**: Load any MCP server via JSON config
- ✅ **Multi-Protocol Support**: Subprocess and HTTP MCP servers
- ✅ **Tool Discovery**: Automatic tool detection and registration
- ✅ **Streaming Responses**: Real-time tool execution with Server-Sent Events
- ✅ **AI Gateway Integration**: Tools formatted for AI assistant integration
- ✅ **RESTful API**: Standard HTTP endpoints for all operations
- ✅ **Health Monitoring**: Server status and tool availability tracking

## Quick Start

### 1. Configuration

Create or update `.stitch/mcp.json`:

```json
{
  "mcpServers": {
    "rsuperset": {
      "type": "streamable-http",
      "url": "http://localhost:8000/mcp/",
      "disabled": false,
      "alwaysAllow": []
    },
    "filesystem": {
      "type": "subprocess",
      "command": ["npx", "-y", "@modelcontextprotocol/server-filesystem"],
      "args": ["/tmp"],
      "disabled": false
    }
  }
}
```

### 2. Start the API Server

```bash
python run_mcp_api.py --config .stitch/mcp.json --port 8081
```

### 3. Test with Postman

Import `MCP_API_Postman_Collection.json` into Postman for ready-to-use API tests.

## API Endpoints

### Health & Status

#### `GET /health`
Basic health check and server statistics.

**Response:**
```json
{
  "status": "healthy",
  "service": "mcp-api",
  "servers_running": 1,
  "total_servers": 1,
  "total_tools": 6
}
```

#### `GET /mcp/status`
Detailed MCP server status and tool overview.

**Response:**
```json
{
  "servers": [
    {
      "name": "rsuperset",
      "status": "running",
      "type": "streamable-http",
      "url": "http://localhost:8000/mcp/"
    }
  ],
  "tools_count": 6,
  "active_servers": 1
}
```

### Tool Management

#### `GET /mcp/tools`
List all available MCP tools with detailed parameters.

**Response:**
```json
{
  "tools": [
    {
      "name": "rsuperset:get_databases",
      "description": "Get all available databases in Superset",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      },
      "server_name": "rsuperset"
    }
  ]
}
```

#### `GET /mcp/tools/ai-gateway`
Get tools formatted for AI Gateway integration.

**Response:**
```json
{
  "tools": [
    {
      "type": "FUNCTION",
      "function": {
        "name": "rsuperset_get_databases",
        "description": "Get all available databases in Superset",
        "parameters": {
          "type": "object",
          "properties": {},
          "required": []
        }
      }
    }
  ]
}
```

### Tool Execution

#### `POST /mcp/tools/call`
Execute a tool with JSON response.

**Request:**
```json
{
  "tool_name": "rsuperset:get_databases",
  "arguments": {}
}
```

**Response:**
```json
{
  "success": true,
  "result": "Tool execution result",
  "error": null
}
```

#### `POST /mcp/tools/call/stream`
Execute a tool with streaming response (Server-Sent Events).

**Request:**
```json
{
  "tool_name": "rsuperset:get_dashboards",
  "arguments": {}
}
```

**Response (Stream):**
```
data: {"type": "status", "message": "Starting tool call"}

data: {"type": "status", "message": "Calling tool: rsuperset:get_dashboards"}

data: {"type": "chunk", "data": "Partial result..."}

data: {"type": "complete", "message": "Tool call completed"}

data: [DONE]
```

### Configuration

#### `GET /mcp/config`
Get current MCP server configuration.

**Response:**
```json
{
  "servers": {
    "rsuperset": {
      "type": "streamable-http",
      "url": "http://localhost:8000/mcp/",
      "disabled": false,
      "status": "running"
    }
  }
}
```

### Server Management

#### `POST /mcp/servers/{server_name}/restart`
Restart a specific MCP server.

**Response:**
```json
{
  "success": true,
  "message": "Server rsuperset restarted"
}
```

## Available Tools (Superset Example)

When connected to the Superset MCP server, the following tools are available:

| Tool Name | Description | Parameters |
|-----------|-------------|------------|
| `rsuperset:get_databases` | Get all available databases | None |
| `rsuperset:get_datasets` | Get all datasets/tables | `database_id` (optional) |
| `rsuperset:get_charts` | Get all charts | `dataset_id` (optional) |
| `rsuperset:get_dashboards` | Get all dashboards | None |
| `rsuperset:create_chart` | Create a new chart | `dataset_id`, `chart_name`, `viz_type` |
| `rsuperset:execute_sql` | Execute SQL query | `database_id`, `sql` |

## Configuration Options

### Server Types

#### `streamable-http`
For FastMCP servers (like Superset MCP):
```json
{
  "type": "streamable-http",
  "url": "http://localhost:8000/mcp/",
  "disabled": false
}
```

#### `http`
For standard HTTP MCP servers:
```json
{
  "type": "http",
  "url": "http://localhost:9000/mcp",
  "disabled": false
}
```

#### `subprocess`
For command-line MCP servers:
```json
{
  "type": "subprocess",
  "command": ["npx", "-y", "@modelcontextprotocol/server-filesystem"],
  "args": ["/tmp"],
  "env": {"VAR": "value"},
  "workingDir": "/path/to/dir",
  "disabled": false
}
```

## Testing

### Automated Testing
```bash
python test_mcp_api.py
```

### Manual Testing with curl

```bash
# Health check
curl http://localhost:8081/health

# List tools
curl http://localhost:8081/mcp/tools

# Call a tool
curl -X POST http://localhost:8081/mcp/tools/call \
  -H "Content-Type: application/json" \
  -d '{"tool_name": "rsuperset:get_databases", "arguments": {}}'

# Stream a tool call
curl -X POST http://localhost:8081/mcp/tools/call/stream \
  -H "Content-Type: application/json" \
  -d '{"tool_name": "rsuperset:get_dashboards", "arguments": {}}'
```

## Integration with Frontend

The API is designed to be easily integrated with web frontends:

1. **Tool Discovery**: Use `/mcp/tools/ai-gateway` to get tools for AI integration
2. **Real-time Execution**: Use streaming endpoints for live updates
3. **Error Handling**: All responses include success/error status
4. **Configuration**: Dynamic server management via API

## Error Handling

The API provides comprehensive error handling:

- **Connection Errors**: Server unavailable or network issues
- **Tool Errors**: Invalid tool names or parameters
- **Execution Errors**: Tool execution failures
- **Configuration Errors**: Invalid server configurations

All errors are returned in a consistent format:
```json
{
  "success": false,
  "result": null,
  "error": "Error description"
}
```

## Next Steps

1. **Authentication**: Add authentication for production use
2. **Rate Limiting**: Implement rate limiting for tool calls
3. **Caching**: Cache tool results for better performance
4. **Monitoring**: Add detailed logging and metrics
5. **Frontend Integration**: Build React components for tool interaction
