"""
Generic MCP Manager

Manages multiple MCP servers based on configuration and provides
a unified interface for tool discovery and execution.
"""

import asyncio
import json
import logging
import os
import subprocess
from typing import Dict, List, Optional, Any, Tuple
import aiohttp
from dataclasses import dataclass

from .mcp_config import <PERSON><PERSON>on<PERSON>g<PERSON>ana<PERSON>, MCPServerConfig

logger = logging.getLogger(__name__)


@dataclass
class MCPTool:
    """Represents an MCP tool."""
    name: str
    description: str
    parameters: Dict[str, Any]
    server_name: str


class MCPManager:
    """Manages multiple MCP servers and provides unified tool access."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_manager = MCPConfigManager(config_path)
        self.servers: Dict[str, MCPServerConfig] = {}
        self.tools: Dict[str, MCPTool] = {}
        self.server_processes: Dict[str, subprocess.Popen] = {}
        self.http_sessions: Dict[str, aiohttp.ClientSession] = {}
        self.server_status: Dict[str, str] = {}  # "running", "stopped", "error"
        self.fastmcp_sessions: Dict[str, str] = {}  # Store FastMCP session IDs
        
    async def initialize(self) -> bool:
        """Initialize the MCP manager by loading config and starting servers."""
        try:
            # Load configuration
            if not self.config_manager.load_config():
                logger.error("Failed to load MCP configuration")
                return False
            
            # Validate configuration
            errors = self.config_manager.validate_config()
            if errors:
                logger.error(f"Configuration validation errors: {errors}")
                return False
            
            self.servers = self.config_manager.get_all_servers()
            logger.info(f"Loaded {len(self.servers)} MCP server configurations")
            
            # Start all servers
            success_count = 0
            for server_name, server_config in self.servers.items():
                if await self.start_server(server_name):
                    success_count += 1
            
            logger.info(f"Successfully started {success_count}/{len(self.servers)} MCP servers")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Failed to initialize MCP manager: {e}")
            return False
    
    async def start_server(self, server_name: str) -> bool:
        """Start a specific MCP server."""
        if server_name not in self.servers:
            logger.error(f"Server {server_name} not found in configuration")
            return False
            
        server_config = self.servers[server_name]
        
        try:
            if server_config.type in ["http", "streamable-http"]:
                return await self._start_http_server(server_name, server_config)
            elif server_config.type == "subprocess":
                return await self._start_subprocess_server(server_name, server_config)
            else:
                logger.error(f"Unsupported server type: {server_config.type}")
                self.server_status[server_name] = "error"
                return False
                
        except Exception as e:
            logger.error(f"Failed to start server {server_name}: {e}")
            self.server_status[server_name] = "error"
            return False
    
    async def _start_http_server(self, server_name: str, config: MCPServerConfig) -> bool:
        """Start an HTTP-based MCP server."""
        try:
            # Create HTTP session with FastMCP compatible headers
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json, text/event-stream"
            }
            session = aiohttp.ClientSession(headers=headers)
            self.http_sessions[server_name] = session
            
            # Test connection
            async with session.get(config.url.rstrip('/')) as response:
                if response.status < 500:  # Accept any non-server-error response
                    logger.info(f"Connected to HTTP MCP server: {server_name} at {config.url}")
                    self.server_status[server_name] = "running"
                    
                    # Discover tools
                    await self._discover_http_tools(server_name, config)
                    return True
                else:
                    logger.error(f"HTTP server {server_name} returned error: {response.status}")
                    await session.close()
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to connect to HTTP server {server_name}: {e}")
            if server_name in self.http_sessions:
                await self.http_sessions[server_name].close()
                del self.http_sessions[server_name]
            return False
    
    async def _start_subprocess_server(self, server_name: str, config: MCPServerConfig) -> bool:
        """Start a subprocess-based MCP server."""
        try:
            # Prepare command
            cmd = config.command + (config.args or [])
            env = {**os.environ, **(config.env or {})}
            
            # Start the server process
            process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                cwd=config.working_dir
            )
            
            self.server_processes[server_name] = process
            self.server_status[server_name] = "running"
            logger.info(f"Started subprocess MCP server: {server_name}")
            
            # Initialize and discover tools
            await self._initialize_subprocess_server(server_name)
            return True
            
        except Exception as e:
            logger.error(f"Failed to start subprocess server {server_name}: {e}")
            return False
    
    async def _discover_http_tools(self, server_name: str, config: MCPServerConfig) -> None:
        """Discover tools from HTTP MCP server."""
        session = self.http_sessions.get(server_name)
        if not session:
            return

        try:
            # For FastMCP, we need to create a session first
            if config.type == "streamable-http":
                await self._initialize_fastmcp_session(server_name, config)
            else:
                # Try standard MCP protocol tools/list
                mcp_request = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "tools/list",
                    "params": {}
                }

                url = config.url
                if not url.endswith('/'):
                    url += '/'

                async with session.post(url, json=mcp_request) as response:
                    if response.status == 200:
                        response_data = await response.json()

                        if "result" in response_data and "tools" in response_data["result"]:
                            tools_list = response_data["result"]["tools"]

                            for tool_data in tools_list:
                                tool = MCPTool(
                                    name=tool_data["name"],
                                    description=tool_data.get("description", ""),
                                    parameters=tool_data.get("inputSchema", {}),
                                    server_name=server_name
                                )
                                tool_key = f"{server_name}:{tool.name}"
                                self.tools[tool_key] = tool
                                logger.info(f"Discovered tool: {tool_key}")
                        else:
                            logger.warning(f"Unexpected response format from {server_name}")
                    else:
                        logger.warning(f"Failed to discover tools from {server_name}: {response.status}")

        except Exception as e:
            logger.error(f"Failed to discover tools from {server_name}: {e}")

    async def _initialize_fastmcp_session(self, server_name: str, config: MCPServerConfig) -> None:
        """Initialize FastMCP session and discover tools."""
        session = self.http_sessions.get(server_name)
        if not session:
            return

        try:
            # Try to create a session with the FastMCP server
            logger.info(f"Attempting to create FastMCP session for {server_name}")

            # First, try to initialize the session
            init_request = {
                "jsonrpc": "2.0",
                "id": "init-1",
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "mcp-api-client",
                        "version": "1.0.0"
                    }
                }
            }

            url = config.url
            if not url.endswith('/'):
                url += '/'

            async with session.post(url, json=init_request) as response:
                if response.status == 200:
                    # Check if response is Server-Sent Events
                    content_type = response.headers.get('content-type', '')
                    if 'text/event-stream' in content_type:
                        # Handle Server-Sent Events response
                        session_id = response.headers.get('mcp-session-id')
                        if session_id:
                            self.fastmcp_sessions[server_name] = session_id
                            logger.info(f"FastMCP session created: {session_id}")

                            # Parse SSE response
                            async for line in response.content:
                                line_str = line.decode('utf-8').strip()
                                if line_str.startswith('data: '):
                                    data_str = line_str[6:]  # Remove 'data: ' prefix
                                    try:
                                        init_response = json.loads(data_str)
                                        logger.info(f"FastMCP initialization response: {init_response}")
                                        break
                                    except json.JSONDecodeError:
                                        continue

                            # Now try to list tools with session ID
                            await self._list_fastmcp_tools(server_name, config, url)
                        else:
                            logger.warning("No session ID in FastMCP response")
                    else:
                        # Regular JSON response
                        init_response = await response.json()
                        logger.info(f"FastMCP initialization response: {init_response}")
                        await self._list_fastmcp_tools(server_name, config, url)
                else:
                    logger.warning(f"FastMCP initialization failed: {response.status}")
                    # Fall back to direct tools/list call
                    await self._try_direct_tools_list(server_name, config, url)

        except Exception as e:
            logger.error(f"Failed to initialize FastMCP session for {server_name}: {e}")
            # Try direct tools/list as fallback
            try:
                await self._try_direct_tools_list(server_name, config, config.url)
            except Exception as e2:
                logger.error(f"Fallback tools/list also failed: {e2}")

    async def _list_fastmcp_tools(self, server_name: str, config: MCPServerConfig, url: str) -> None:
        """List tools from FastMCP server after initialization."""
        session = self.http_sessions.get(server_name)
        if not session:
            return

        try:
            tools_request = {
                "jsonrpc": "2.0",
                "id": "tools-1",
                "method": "tools/list",
                "params": {}
            }

            # Add session ID header if available
            headers = {}
            session_id = self.fastmcp_sessions.get(server_name)
            if session_id:
                headers['mcp-session-id'] = session_id
                logger.info(f"Using FastMCP session ID: {session_id}")

            async with session.post(url, json=tools_request, headers=headers) as response:
                if response.status == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'text/event-stream' in content_type:
                        # Handle Server-Sent Events response
                        logger.info("Handling SSE response for tools/list")
                        async for line in response.content:
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: '):
                                data_str = line_str[6:]  # Remove 'data: ' prefix
                                try:
                                    tools_response = json.loads(data_str)
                                    logger.info(f"FastMCP tools response: {tools_response}")

                                    if "result" in tools_response and "tools" in tools_response["result"]:
                                        tools_list = tools_response["result"]["tools"]

                                        for tool_data in tools_list:
                                            tool = MCPTool(
                                                name=tool_data["name"],
                                                description=tool_data.get("description", ""),
                                                parameters=tool_data.get("inputSchema", {}),
                                                server_name=server_name
                                            )
                                            tool_key = f"{server_name}:{tool.name}"
                                            self.tools[tool_key] = tool
                                            logger.info(f"Discovered FastMCP tool: {tool_key}")
                                        break  # Exit after processing the tools response
                                    elif "error" in tools_response:
                                        logger.warning(f"FastMCP tools/list error: {tools_response['error']}")
                                        # Fall back to loading known Superset tools
                                        await self._load_known_superset_tools(server_name)
                                        break
                                    else:
                                        logger.warning(f"Unexpected tools response format: {tools_response}")
                                except json.JSONDecodeError:
                                    continue
                    else:
                        # Regular JSON response
                        tools_response = await response.json()
                        logger.info(f"FastMCP tools response: {tools_response}")

                        if "result" in tools_response and "tools" in tools_response["result"]:
                            tools_list = tools_response["result"]["tools"]

                            for tool_data in tools_list:
                                tool = MCPTool(
                                    name=tool_data["name"],
                                    description=tool_data.get("description", ""),
                                    parameters=tool_data.get("inputSchema", {}),
                                    server_name=server_name
                                )
                                tool_key = f"{server_name}:{tool.name}"
                                self.tools[tool_key] = tool
                                logger.info(f"Discovered FastMCP tool: {tool_key}")
                        else:
                            logger.warning(f"Unexpected tools response format: {tools_response}")
                else:
                    logger.warning(f"FastMCP tools/list failed: {response.status}")

        except Exception as e:
            logger.error(f"Failed to list FastMCP tools: {e}")

    async def _try_direct_tools_list(self, server_name: str, config: MCPServerConfig, url: str) -> None:
        """Try direct tools/list call without session initialization."""
        session = self.http_sessions.get(server_name)
        if not session:
            return

        try:
            logger.info(f"Trying direct tools/list for {server_name}")

            # Try different approaches to get tools
            approaches = [
                # Standard MCP tools/list
                {
                    "jsonrpc": "2.0",
                    "id": "direct-1",
                    "method": "tools/list",
                    "params": {}
                },
                # Try with empty session
                {
                    "jsonrpc": "2.0",
                    "id": "direct-2",
                    "method": "tools/list",
                    "params": {},
                    "session": ""
                }
            ]

            for i, request in enumerate(approaches):
                try:
                    async with session.post(url, json=request) as response:
                        response_text = await response.text()
                        logger.info(f"Approach {i+1} - Status: {response.status}, Response: {response_text}")

                        if response.status == 200:
                            try:
                                response_data = await response.json()
                                if "result" in response_data and "tools" in response_data["result"]:
                                    tools_list = response_data["result"]["tools"]

                                    for tool_data in tools_list:
                                        tool = MCPTool(
                                            name=tool_data["name"],
                                            description=tool_data.get("description", ""),
                                            parameters=tool_data.get("inputSchema", {}),
                                            server_name=server_name
                                        )
                                        tool_key = f"{server_name}:{tool.name}"
                                        self.tools[tool_key] = tool
                                        logger.info(f"Discovered tool via approach {i+1}: {tool_key}")

                                    return  # Success, exit
                            except json.JSONDecodeError:
                                logger.warning(f"Invalid JSON in response: {response_text}")

                except Exception as e:
                    logger.warning(f"Approach {i+1} failed: {e}")

        except Exception as e:
            logger.error(f"All direct tools/list approaches failed: {e}")

    async def _load_known_superset_tools(self, server_name: str) -> None:
        """Load known Superset tools when dynamic discovery fails."""
        logger.info(f"Loading known Superset tools for {server_name}")

        # Key Superset tools based on the actual MCP server source code
        known_tools = [
            {
                "name": "superset_config_get_base_url",
                "description": "Get the base URL of the Superset instance",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "superset_auth_authenticate_user",
                "description": "Authenticate user with Superset",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "username": {"type": "string", "description": "Username"},
                        "password": {"type": "string", "description": "Password"}
                    },
                    "required": []
                }
            },
            {
                "name": "superset_auth_check_token_validity",
                "description": "Check if the current access token is still valid",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "superset_dashboard_list",
                "description": "Get a list of dashboards from Superset",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "superset_chart_list",
                "description": "Get a list of charts from Superset",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "superset_database_list",
                "description": "Get a list of databases from Superset",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "superset_dataset_list",
                "description": "Get a list of datasets from Superset",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "superset_sqllab_execute_query",
                "description": "Execute SQL query in Superset SQL Lab",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "database_id": {"type": "integer", "description": "Database ID"},
                        "sql": {"type": "string", "description": "SQL query to execute"}
                    },
                    "required": ["database_id", "sql"]
                }
            },
            {
                "name": "superset_user_get_current",
                "description": "Get information about the currently authenticated user",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        ]

        for tool_data in known_tools:
            tool = MCPTool(
                name=tool_data["name"],
                description=tool_data["description"],
                parameters=tool_data["inputSchema"],
                server_name=server_name
            )
            tool_key = f"{server_name}:{tool.name}"
            self.tools[tool_key] = tool
            logger.info(f"Loaded known Superset tool: {tool_key}")

        logger.info(f"Loaded {len(known_tools)} known Superset tools")
    
    async def _initialize_subprocess_server(self, server_name: str) -> None:
        """Initialize subprocess MCP server and discover tools."""
        process = self.server_processes.get(server_name)
        if not process:
            return
            
        try:
            # Send initialize request
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {},
                    "clientInfo": {"name": "ai-gateway", "version": "1.0.0"}
                }
            }
            
            request_json = json.dumps(init_request) + "\n"
            process.stdin.write(request_json)
            process.stdin.flush()
            
            # Read response
            response_line = process.stdout.readline()
            if response_line:
                response = json.loads(response_line.strip())
                logger.info(f"Server {server_name} initialized: {response}")
                
                # List tools
                await self._list_subprocess_tools(server_name)
                
        except Exception as e:
            logger.error(f"Failed to initialize subprocess server {server_name}: {e}")
    
    async def _list_subprocess_tools(self, server_name: str) -> None:
        """List tools from subprocess MCP server."""
        process = self.server_processes.get(server_name)
        if not process:
            return
            
        try:
            # Send tools/list request
            list_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list",
                "params": {}
            }
            
            request_json = json.dumps(list_request) + "\n"
            process.stdin.write(request_json)
            process.stdin.flush()
            
            # Read response
            response_line = process.stdout.readline()
            if response_line:
                response = json.loads(response_line.strip())
                
                if "result" in response and "tools" in response["result"]:
                    tools_list = response["result"]["tools"]
                    
                    for tool_data in tools_list:
                        tool = MCPTool(
                            name=tool_data["name"],
                            description=tool_data.get("description", ""),
                            parameters=tool_data.get("inputSchema", {}),
                            server_name=server_name
                        )
                        tool_key = f"{server_name}:{tool.name}"
                        self.tools[tool_key] = tool
                        logger.info(f"Discovered tool: {tool_key}")
                        
        except Exception as e:
            logger.error(f"Failed to list tools from subprocess server {server_name}: {e}")
    
    async def call_tool(self, tool_key: str, arguments: Dict[str, Any]) -> Optional[str]:
        """Call a tool on the appropriate MCP server."""
        if tool_key not in self.tools:
            logger.error(f"Tool {tool_key} not found")
            return None
            
        tool = self.tools[tool_key]
        server_config = self.servers[tool.server_name]
        
        if server_config.type in ["http", "streamable-http"]:
            return await self._call_http_tool(tool, arguments)
        elif server_config.type == "subprocess":
            return await self._call_subprocess_tool(tool, arguments)
        else:
            logger.error(f"Unsupported server type: {server_config.type}")
            return None

    async def _call_http_tool(self, tool: MCPTool, arguments: Dict[str, Any]) -> Optional[str]:
        """Call a tool on an HTTP-based MCP server."""
        server_config = self.servers[tool.server_name]
        session = self.http_sessions.get(tool.server_name)

        if not session:
            logger.error(f"HTTP session not available for server {tool.server_name}")
            return None

        try:
            # Use MCP protocol over HTTP
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": tool.name,
                    "arguments": arguments
                }
            }

            url = server_config.url
            if not url.endswith('/'):
                url += '/'

            # Add session ID header if available for FastMCP
            headers = {}
            session_id = self.fastmcp_sessions.get(tool.server_name)
            if session_id:
                headers['mcp-session-id'] = session_id
                logger.info(f"Using FastMCP session ID for tool call: {session_id}")

            async with session.post(url, json=mcp_request, headers=headers) as response:
                if response.status == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'text/event-stream' in content_type:
                        # Handle Server-Sent Events response
                        logger.info("Handling SSE response for tool call")
                        async for line in response.content:
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: '):
                                data_str = line_str[6:]  # Remove 'data: ' prefix
                                try:
                                    response_data = json.loads(data_str)
                                    logger.info(f"Tool call SSE response: {response_data}")

                                    if "result" in response_data:
                                        result = response_data["result"]
                                        if "content" in result:
                                            content_items = result["content"]
                                            if isinstance(content_items, list) and content_items:
                                                return content_items[0].get("text", str(result))
                                        return str(result)
                                    elif "error" in response_data:
                                        error = response_data["error"]
                                        logger.error(f"MCP tool call error: {error}")
                                        return f"Error: {error.get('message', 'Unknown error')}"
                                    # Continue reading if no result/error yet
                                except json.JSONDecodeError:
                                    continue
                        return "No valid response received from tool call"
                    else:
                        # Regular JSON response
                        response_data = await response.json()

                        if "result" in response_data:
                            result = response_data["result"]
                            if "content" in result:
                                content_items = result["content"]
                                if isinstance(content_items, list) and content_items:
                                    return content_items[0].get("text", str(result))
                            return str(result)
                        elif "error" in response_data:
                            error = response_data["error"]
                            logger.error(f"MCP tool call error: {error}")
                            return f"Error: {error.get('message', 'Unknown error')}"
                        else:
                            return str(response_data)
                else:
                    error_text = await response.text()
                    logger.error(f"HTTP tool call failed: {response.status} - {error_text}")
                    return f"Error: HTTP {response.status} - {error_text}"

        except Exception as e:
            logger.error(f"Failed to call HTTP tool {tool.name}: {e}")
            return f"Error calling HTTP tool: {str(e)}"

    async def _call_subprocess_tool(self, tool: MCPTool, arguments: Dict[str, Any]) -> Optional[str]:
        """Call a tool on a subprocess-based MCP server."""
        process = self.server_processes.get(tool.server_name)

        if not process:
            logger.error(f"Subprocess not available for server {tool.server_name}")
            return None

        try:
            # Send tool call request
            call_request = {
                "jsonrpc": "2.0",
                "id": 4,
                "method": "tools/call",
                "params": {
                    "name": tool.name,
                    "arguments": arguments
                }
            }

            request_json = json.dumps(call_request) + "\n"
            process.stdin.write(request_json)
            process.stdin.flush()

            # Read response
            response_line = process.stdout.readline()
            if response_line:
                response = json.loads(response_line.strip())

                if "result" in response:
                    result = response["result"]
                    if "content" in result:
                        content_items = result["content"]
                        if isinstance(content_items, list) and content_items:
                            return content_items[0].get("text", str(result))
                    return str(result)
                elif "error" in response:
                    logger.error(f"Tool call error: {response['error']}")
                    return f"Error: {response['error'].get('message', 'Unknown error')}"

        except Exception as e:
            logger.error(f"Failed to call subprocess tool {tool.name}: {e}")
            return f"Error calling subprocess tool: {str(e)}"

        return None

    def get_all_tools(self) -> Dict[str, MCPTool]:
        """Get all available tools."""
        return self.tools.copy()

    def get_server_status(self) -> Dict[str, str]:
        """Get status of all servers."""
        return self.server_status.copy()

    def get_tools_for_ai_gateway(self) -> List[Dict[str, Any]]:
        """Convert tools to AI Gateway format."""
        ai_gateway_tools = []

        for tool_key, tool in self.tools.items():
            # Create safe function name
            safe_name = tool_key.replace(":", "_").replace("-", "_")

            ai_gateway_tool = {
                "type": "FUNCTION",
                "function": {
                    "name": safe_name,
                    "description": tool.description,
                    "parameters": tool.parameters
                }
            }
            ai_gateway_tools.append(ai_gateway_tool)

        return ai_gateway_tools

    async def stop_server(self, server_name: str) -> None:
        """Stop a specific MCP server."""
        # Stop subprocess
        if server_name in self.server_processes:
            process = self.server_processes[server_name]
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            finally:
                del self.server_processes[server_name]
                logger.info(f"Stopped subprocess server: {server_name}")

        # Close HTTP session
        if server_name in self.http_sessions:
            await self.http_sessions[server_name].close()
            del self.http_sessions[server_name]
            logger.info(f"Closed HTTP session for server: {server_name}")

        # Update status
        self.server_status[server_name] = "stopped"

    async def stop_all_servers(self) -> None:
        """Stop all MCP servers."""
        for server_name in list(self.servers.keys()):
            await self.stop_server(server_name)

        logger.info("All MCP servers stopped")

    async def cleanup(self) -> None:
        """Clean up all resources."""
        await self.stop_all_servers()
        logger.info("MCP manager cleaned up")
