"""
MCP REST API

Provides REST endpoints for testing and interacting with MCP servers.
Supports streaming responses for real-time tool execution.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, AsyncGenerator
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uvicorn

from .mcp_manager import MCPManager

logger = logging.getLogger(__name__)

# Pydantic models for API requests/responses
class ToolCallRequest(BaseModel):
    tool_name: str
    arguments: Dict[str, Any] = {}

class ToolCallResponse(BaseModel):
    success: bool
    result: Optional[str] = None
    error: Optional[str] = None

class ServerStatus(BaseModel):
    name: str
    status: str
    type: str
    url: Optional[str] = None

class ToolInfo(BaseModel):
    name: str
    description: str
    parameters: Dict[str, Any]
    server_name: str

class MCPStatus(BaseModel):
    servers: List[ServerStatus]
    tools_count: int
    active_servers: int


class MCPAPIServer:
    """REST API server for MCP integration."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.app = FastAPI(title="MCP API Server", version="1.0.0")
        self.mcp_manager = MCPManager(config_path)
        self.setup_routes()
        
    def setup_routes(self):
        """Setup API routes."""
        
        @self.app.on_event("startup")
        async def startup_event():
            """Initialize MCP manager on startup."""
            success = await self.mcp_manager.initialize()
            if not success:
                logger.warning("Failed to initialize some MCP servers")
        
        @self.app.on_event("shutdown")
        async def shutdown_event():
            """Cleanup on shutdown."""
            await self.mcp_manager.cleanup()
        
        @self.app.get("/mcp/status", response_model=MCPStatus)
        async def get_mcp_status():
            """Get status of all MCP servers and tools."""
            server_status = self.mcp_manager.get_server_status()
            servers = []
            
            for server_name, config in self.mcp_manager.servers.items():
                status = server_status.get(server_name, "unknown")
                servers.append(ServerStatus(
                    name=server_name,
                    status=status,
                    type=config.type,
                    url=config.url
                ))
            
            active_servers = sum(1 for status in server_status.values() if status == "running")
            tools_count = len(self.mcp_manager.tools)
            
            return MCPStatus(
                servers=servers,
                tools_count=tools_count,
                active_servers=active_servers
            )
        
        @self.app.get("/mcp/tools")
        async def list_tools():
            """List all available MCP tools."""
            tools = []
            for tool_key, tool in self.mcp_manager.get_all_tools().items():
                tools.append(ToolInfo(
                    name=tool_key,
                    description=tool.description,
                    parameters=tool.parameters,
                    server_name=tool.server_name
                ))
            return {"tools": tools}
        
        @self.app.get("/mcp/tools/ai-gateway")
        async def get_ai_gateway_tools():
            """Get tools in AI Gateway format."""
            return {"tools": self.mcp_manager.get_tools_for_ai_gateway()}
        
        @self.app.post("/mcp/tools/call", response_model=ToolCallResponse)
        async def call_tool(request: ToolCallRequest):
            """Call a specific MCP tool."""
            try:
                result = await self.mcp_manager.call_tool(request.tool_name, request.arguments)
                
                if result is not None:
                    return ToolCallResponse(success=True, result=result)
                else:
                    return ToolCallResponse(success=False, error="Tool call failed")
                    
            except Exception as e:
                logger.error(f"Error calling tool {request.tool_name}: {e}")
                return ToolCallResponse(success=False, error=str(e))
        
        @self.app.post("/mcp/tools/call/stream")
        async def call_tool_stream(request: ToolCallRequest):
            """Call a tool with streaming response."""
            
            async def generate_stream():
                """Generate streaming response."""
                try:
                    # Send initial status
                    yield f"data: {json.dumps({'type': 'status', 'message': 'Starting tool call'})}\n\n"
                    
                    # Call the tool
                    yield f"data: {json.dumps({'type': 'status', 'message': f'Calling tool: {request.tool_name}'})}\n\n"
                    
                    result = await self.mcp_manager.call_tool(request.tool_name, request.arguments)
                    
                    if result is not None:
                        # Send result in chunks for demonstration
                        chunk_size = 100
                        for i in range(0, len(result), chunk_size):
                            chunk = result[i:i + chunk_size]
                            yield f"data: {json.dumps({'type': 'chunk', 'data': chunk})}\n\n"
                            await asyncio.sleep(0.1)  # Simulate streaming delay
                        
                        # Send completion
                        yield f"data: {json.dumps({'type': 'complete', 'message': 'Tool call completed'})}\n\n"
                    else:
                        yield f"data: {json.dumps({'type': 'error', 'message': 'Tool call failed'})}\n\n"
                        
                except Exception as e:
                    yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
                
                # End stream
                yield "data: [DONE]\n\n"
            
            return StreamingResponse(
                generate_stream(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Headers": "*",
                }
            )
        
        @self.app.post("/mcp/servers/{server_name}/restart")
        async def restart_server(server_name: str):
            """Restart a specific MCP server."""
            try:
                # Stop the server
                await self.mcp_manager.stop_server(server_name)
                
                # Start it again
                success = await self.mcp_manager.start_server(server_name)
                
                if success:
                    return {"success": True, "message": f"Server {server_name} restarted"}
                else:
                    return {"success": False, "message": f"Failed to restart server {server_name}"}
                    
            except Exception as e:
                logger.error(f"Error restarting server {server_name}: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/mcp/config")
        async def get_config():
            """Get current MCP configuration."""
            return {
                "servers": {
                    name: {
                        "type": config.type,
                        "url": config.url,
                        "disabled": config.disabled,
                        "status": self.mcp_manager.server_status.get(name, "unknown")
                    }
                    for name, config in self.mcp_manager.servers.items()
                }
            }
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint."""
            return {
                "status": "healthy",
                "service": "mcp-api",
                "servers_running": len([s for s in self.mcp_manager.server_status.values() if s == "running"]),
                "total_servers": len(self.mcp_manager.servers),
                "total_tools": len(self.mcp_manager.tools)
            }
    
    def run(self, host: str = "0.0.0.0", port: int = 8080):
        """Run the API server."""
        uvicorn.run(self.app, host=host, port=port)


# Standalone function to run the server
def run_mcp_api_server(config_path: Optional[str] = None, host: str = "0.0.0.0", port: int = 8080):
    """Run the MCP API server."""
    server = MCPAPIServer(config_path)
    server.run(host, port)


if __name__ == "__main__":
    import sys
    
    config_path = sys.argv[1] if len(sys.argv) > 1 else None
    run_mcp_api_server(config_path)
