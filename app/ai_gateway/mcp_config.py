"""
MCP Configuration Manager

Handles loading and managing MCP server configurations from JSON files.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class MCPServerConfig:
    """Configuration for an MCP server."""
    name: str
    type: str  # "subprocess", "http", "streamable-http"
    url: Optional[str] = None
    command: Optional[List[str]] = None
    args: Optional[List[str]] = None
    env: Optional[Dict[str, str]] = None
    working_dir: Optional[str] = None
    disabled: bool = False
    always_allow: Optional[List[str]] = None


class MCPConfigManager:
    """Manages MCP server configurations."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or ".stitch/mcp.json"
        self.servers: Dict[str, MCPServerConfig] = {}
        
    def load_config(self) -> bool:
        """Load MCP server configurations from JSON file."""
        try:
            config_file = Path(self.config_path)
            if not config_file.exists():
                logger.warning(f"MCP config file not found: {self.config_path}")
                return False
                
            with open(config_file, 'r') as f:
                config_data = json.load(f)
                
            # Parse server configurations
            mcp_servers = config_data.get('mcpServers', {})
            
            for server_name, server_config in mcp_servers.items():
                if server_config.get('disabled', False):
                    logger.info(f"Skipping disabled MCP server: {server_name}")
                    continue
                    
                # Create server configuration
                server = MCPServerConfig(
                    name=server_name,
                    type=server_config.get('type', 'subprocess'),
                    url=server_config.get('url'),
                    command=server_config.get('command'),
                    args=server_config.get('args'),
                    env=server_config.get('env'),
                    working_dir=server_config.get('workingDir'),
                    disabled=server_config.get('disabled', False),
                    always_allow=server_config.get('alwaysAllow', [])
                )
                
                self.servers[server_name] = server
                logger.info(f"Loaded MCP server config: {server_name} ({server.type})")
                
            logger.info(f"Loaded {len(self.servers)} MCP server configurations")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load MCP config from {self.config_path}: {e}")
            return False
    
    def get_server_config(self, name: str) -> Optional[MCPServerConfig]:
        """Get configuration for a specific server."""
        return self.servers.get(name)
    
    def get_all_servers(self) -> Dict[str, MCPServerConfig]:
        """Get all server configurations."""
        return self.servers.copy()
    
    def add_server_config(self, server: MCPServerConfig) -> None:
        """Add a server configuration."""
        self.servers[server.name] = server
        logger.info(f"Added MCP server config: {server.name}")
    
    def remove_server_config(self, name: str) -> bool:
        """Remove a server configuration."""
        if name in self.servers:
            del self.servers[name]
            logger.info(f"Removed MCP server config: {name}")
            return True
        return False
    
    def save_config(self) -> bool:
        """Save current configurations to JSON file."""
        try:
            config_data = {
                "mcpServers": {}
            }
            
            for server_name, server in self.servers.items():
                server_config = {
                    "type": server.type,
                    "disabled": server.disabled
                }
                
                if server.url:
                    server_config["url"] = server.url
                if server.command:
                    server_config["command"] = server.command
                if server.args:
                    server_config["args"] = server.args
                if server.env:
                    server_config["env"] = server.env
                if server.working_dir:
                    server_config["workingDir"] = server.working_dir
                if server.always_allow:
                    server_config["alwaysAllow"] = server.always_allow
                    
                config_data["mcpServers"][server_name] = server_config
            
            # Ensure directory exists
            config_file = Path(self.config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
                
            logger.info(f"Saved MCP config to {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save MCP config to {self.config_path}: {e}")
            return False
    
    def validate_config(self) -> List[str]:
        """Validate all server configurations and return any errors."""
        errors = []
        
        for server_name, server in self.servers.items():
            if server.type in ["http", "streamable-http"]:
                if not server.url:
                    errors.append(f"Server {server_name}: URL required for HTTP servers")
            elif server.type == "subprocess":
                if not server.command:
                    errors.append(f"Server {server_name}: Command required for subprocess servers")
            else:
                errors.append(f"Server {server_name}: Unknown server type '{server.type}'")
                
        return errors
